<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Feedback Table Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .demo-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        
        .before {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .before h3 {
            color: #dc2626;
            margin-top: 0;
        }
        
        .after h3 {
            color: #16a34a;
            margin-top: 0;
        }
        
        .demo-info {
            background-color: #e0f2fe;
            border: 1px solid #0284c7;
            color: #0c4a6e;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        /* Mobile-responsive table improvements */
        @media (max-width: 768px) {
            .marking-table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            .marking-table {
                min-width: 600px;
            }
            
            .marking-point-col {
                width: 50%;
                min-width: 250px;
            }
            
            .status-col {
                width: 25%;
                min-width: 100px;
            }
            
            .score-col {
                width: 25%;
                min-width: 80px;
            }
            
            .marking-point-text {
                word-wrap: break-word;
                overflow-wrap: break-word;
                hyphens: auto;
                line-height: 1.4;
            }
            
            .status-badge {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
            
            .evidence-text {
                font-size: 0.75rem;
                line-height: 1.3;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            
            .mobile-table-cell {
                padding: 0.75rem 0.5rem;
            }
        }
        
        .marking-point-description {
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
        }
        
        .evidence-container {
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
        }
        
        /* Demo table styles */
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .demo-table th,
        .demo-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        
        .demo-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }
        
        .overflow-demo {
            background: #fee2e2;
            border: 2px dashed #dc2626;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .fixed-demo {
            background: #dcfce7;
            border: 2px dashed #16a34a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .mobile-preview {
            max-width: 375px;
            border: 2px solid #6b7280;
            border-radius: 12px;
            padding: 10px;
            background: white;
            margin: 0 auto;
        }
        
        .status-badge-demo {
            display: inline-flex;
            align-items: center;
            border-radius: 9999px;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-achieved {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-partial {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-not-achieved {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <h1>Mobile Feedback Table Improvements</h1>
    
    <div class="demo-info">
        <strong>Problem Fixed:</strong> The marking points table was overflowing horizontally on mobile devices, making it impossible to read feedback properly. This update ensures all content fits within screen bounds.
    </div>

    <!-- Problem Demonstration -->
    <div class="demo-container">
        <h2>1. Problem: Table Overflow on Mobile</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ Before: Overflowing Table</h3>
                <div class="overflow-demo">
                    <strong>Issue:</strong> Table extends beyond screen width
                    <div style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                        Very long marking point description that would overflow and be cut off on mobile devices making it impossible to read the full feedback content...
                    </div>
                </div>
            </div>
            
            <div class="after">
                <h3>✅ After: Responsive Table</h3>
                <div class="fixed-demo">
                    <strong>Solution:</strong> Horizontal scroll + text wrapping
                    <div style="word-wrap: break-word; overflow-wrap: break-word;">
                        Very long marking point description that now wraps properly and can be read completely on mobile devices with horizontal scrolling when needed.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Preview -->
    <div class="demo-container">
        <h2>2. Mobile Table Preview</h2>
        
        <div class="mobile-preview">
            <h3 style="text-align: center; margin-top: 0;">📱 Mobile View</h3>
            
            <div class="marking-table-container">
                <table class="demo-table marking-table">
                    <thead>
                        <tr>
                            <th class="marking-point-col">Marking Point</th>
                            <th class="status-col">Status</th>
                            <th class="score-col">Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="mobile-table-cell marking-point-col">
                                <div class="marking-point-description">
                                    <strong>Calculate the equilibrium constant</strong>
                                </div>
                                <div style="margin-top: 8px; font-size: 0.75rem; color: #6b7280; background: #f3f4f6; padding: 8px; border-radius: 4px;">
                                    <strong>Evidence:</strong> <span class="evidence-text">"Student correctly applied Kc = [products]/[reactants] formula"</span>
                                </div>
                            </td>
                            <td class="mobile-table-cell status-col" style="text-align: center;">
                                <span class="status-badge-demo status-achieved">
                                    ✓ Achieved
                                </span>
                            </td>
                            <td class="mobile-table-cell score-col" style="text-align: right;">
                                <strong style="color: #16a34a;">2.0/2.0</strong>
                            </td>
                        </tr>
                        <tr>
                            <td class="mobile-table-cell marking-point-col">
                                <div class="marking-point-description">
                                    <strong>Show correct units in final answer</strong>
                                </div>
                                <div style="margin-top: 8px; font-size: 0.75rem; color: #dc2626; font-style: italic;">
                                    No evidence found in your answer for this marking point.
                                </div>
                            </td>
                            <td class="mobile-table-cell status-col" style="text-align: center;">
                                <span class="status-badge-demo status-not-achieved">
                                    ✗ Not Achieved
                                </span>
                            </td>
                            <td class="mobile-table-cell score-col" style="text-align: right;">
                                <strong style="color: #dc2626;">0.0/1.0</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <p style="text-align: center; font-size: 0.75rem; color: #6b7280; margin-top: 10px;">
                ← Swipe horizontally to see all columns →
            </p>
        </div>
    </div>

    <!-- Technical Improvements -->
    <div class="demo-container">
        <h2>3. Technical Improvements Made</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div>
                <h3>📱 Mobile Responsiveness</h3>
                <ul>
                    <li>Horizontal scroll container</li>
                    <li>Minimum table width (600px)</li>
                    <li>Optimized column widths</li>
                    <li>Touch-friendly scrolling</li>
                </ul>
            </div>
            
            <div>
                <h3>📝 Text Handling</h3>
                <ul>
                    <li>Word wrapping for long text</li>
                    <li>Automatic hyphenation</li>
                    <li>Improved line height</li>
                    <li>Smaller fonts on mobile</li>
                </ul>
            </div>
            
            <div>
                <h3>🎨 Visual Improvements</h3>
                <ul>
                    <li>Compact status badges</li>
                    <li>Reduced cell padding</li>
                    <li>Better evidence display</li>
                    <li>Consistent spacing</li>
                </ul>
            </div>
            
            <div>
                <h3>⚡ Performance</h3>
                <ul>
                    <li>Hardware-accelerated scrolling</li>
                    <li>Optimized CSS selectors</li>
                    <li>Reduced reflows</li>
                    <li>Better memory usage</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- CSS Code Example -->
    <div class="demo-container">
        <h2>4. Key CSS Improvements</h2>
        
        <div style="background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 14px; overflow-x: auto;">
<span style="color: #9ca3af;">/* Mobile-responsive table container */</span>
.marking-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

<span style="color: #9ca3af;">/* Optimized column widths */</span>
.marking-point-col { width: 50%; min-width: 250px; }
.status-col { width: 25%; min-width: 100px; }
.score-col { width: 25%; min-width: 80px; }

<span style="color: #9ca3af;">/* Text wrapping improvements */</span>
.marking-point-description {
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

<span style="color: #9ca3af;">/* Mobile-specific adjustments */</span>
@media (max-width: 768px) {
    .mobile-table-cell {
        padding: 0.75rem 0.5rem;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}
        </div>
    </div>

    <div class="demo-container">
        <h2>5. Result Summary</h2>
        
        <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px;">
            <h3 style="color: #16a34a; margin-top: 0;">✅ Problem Solved!</h3>
            <ul style="color: #166534;">
                <li><strong>No more horizontal overflow</strong> - All content fits within screen bounds</li>
                <li><strong>Readable text</strong> - Long descriptions wrap properly</li>
                <li><strong>Touch-friendly</strong> - Smooth horizontal scrolling when needed</li>
                <li><strong>Consistent layout</strong> - Works across all mobile devices</li>
                <li><strong>Better UX</strong> - Users can read all feedback content easily</li>
            </ul>
        </div>
    </div>
</body>
</html>
