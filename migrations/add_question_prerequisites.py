#!/usr/bin/env python3
"""
Migration script to add question prerequisites table and update existing questions.
This script creates the question_prerequisites table to support prerequisite relationships
between questions in the dojo system.
"""

import sys
import os

# Add the current directory to Python path to import models
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models import db, QuestionPrerequisite

def create_prerequisites_table():
    """Create the question_prerequisites table"""
    with app.app_context():
        try:
            # Create the table
            db.create_all()
            print("✓ Created question_prerequisites table successfully")
            
            # Verify the table was created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if 'question_prerequisites' in tables:
                print("✓ Verified question_prerequisites table exists")
                
                # Show table structure
                columns = inspector.get_columns('question_prerequisites')
                print("\nTable structure:")
                for column in columns:
                    print(f"  - {column['name']}: {column['type']}")
                    
                # Show constraints
                constraints = inspector.get_check_constraints('question_prerequisites')
                unique_constraints = inspector.get_unique_constraints('question_prerequisites')
                
                if constraints:
                    print("\nCheck constraints:")
                    for constraint in constraints:
                        print(f"  - {constraint['name']}: {constraint.get('sqltext', 'N/A')}")
                        
                if unique_constraints:
                    print("\nUnique constraints:")
                    for constraint in unique_constraints:
                        print(f"  - {constraint['name']}: {constraint['column_names']}")
                        
            else:
                print("✗ Failed to create question_prerequisites table")
                return False
                
            return True
            
        except Exception as e:
            print(f"✗ Error creating question_prerequisites table: {e}")
            db.session.rollback()
            return False

def verify_migration():
    """Verify the migration was successful"""
    with app.app_context():
        try:
            # Test creating a sample prerequisite relationship (will be rolled back)
            from models import Question
            
            # Get two questions for testing
            questions = Question.query.limit(2).all()
            if len(questions) >= 2:
                test_prerequisite = QuestionPrerequisite(
                    question_id=questions[1].id,
                    prerequisite_question_id=questions[0].id
                )
                db.session.add(test_prerequisite)
                db.session.flush()  # Test the insert
                
                # Query it back
                retrieved = QuestionPrerequisite.query.filter_by(
                    question_id=questions[1].id,
                    prerequisite_question_id=questions[0].id
                ).first()
                
                if retrieved:
                    print("✓ Successfully tested prerequisite relationship creation")
                    db.session.rollback()  # Clean up test data
                    return True
                else:
                    print("✗ Failed to retrieve test prerequisite relationship")
                    db.session.rollback()
                    return False
            else:
                print("⚠ Not enough questions in database to test relationships")
                return True
                
        except Exception as e:
            print(f"✗ Error verifying migration: {e}")
            db.session.rollback()
            return False

def main():
    """Main migration function"""
    print("Starting question prerequisites migration...")
    
    # Create the table
    if not create_prerequisites_table():
        print("Migration failed!")
        return False
    
    # Verify the migration
    if not verify_migration():
        print("Migration verification failed!")
        return False
    
    print("\n✓ Question prerequisites migration completed successfully!")
    print("\nNext steps:")
    print("1. Use the admin interface to mark questions as dojo questions")
    print("2. Set up prerequisite relationships between questions")
    print("3. Test the new dojo system functionality")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
