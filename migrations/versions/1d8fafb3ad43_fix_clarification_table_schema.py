"""Fix clarification table schema

Revision ID: 1d8fafb3ad43
Revises: 392273ead511
Create Date: 2025-07-09 21:24:39.462405

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1d8fafb3ad43'
down_revision = '392273ead511'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('clarifications', schema=None) as batch_op:
        batch_op.drop_column('score_achieved')
        batch_op.drop_column('was_helpful')
        batch_op.drop_column('recommended_at')
        batch_op.drop_column('attempted_at')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('clarifications', schema=None) as batch_op:
        batch_op.add_column(sa.Column('attempted_at', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('recommended_at', sa.DATETIME(), nullable=False))
        batch_op.add_column(sa.Column('was_helpful', sa.BOOLEAN(), nullable=True))
        batch_op.add_column(sa.Column('score_achieved', sa.FLOAT(), nullable=True))

    # ### end Alembic commands ###
