"""Add clarification table

Revision ID: 392273ead511
Revises: ed0d2f398f44
Create Date: 2025-07-09 21:15:11.098941

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '392273ead511'
down_revision = 'ed0d2f398f44'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('clarifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('question_id', sa.Integer(), nullable=False),
    sa.Column('part_id', sa.Integer(), nullable=True),
    sa.Column('subject', sa.String(length=255), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('context_data', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('teacher_response', sa.Text(), nullable=True),
    sa.Column('teacher_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('recommended_at', sa.DateTime(), nullable=False),
    sa.Column('attempted_at', sa.DateTime(), nullable=True),
    sa.Column('score_achieved', sa.Float(), nullable=True),
    sa.Column('was_helpful', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['part_id'], ['parts.id'], ),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['teacher_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('recommendation_history', schema=None) as batch_op:
        batch_op.drop_column('recommended_at')
        batch_op.drop_column('score_achieved')
        batch_op.drop_column('was_helpful')
        batch_op.drop_column('attempted_at')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('recommendation_history', schema=None) as batch_op:
        batch_op.add_column(sa.Column('attempted_at', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('was_helpful', sa.BOOLEAN(), nullable=True))
        batch_op.add_column(sa.Column('score_achieved', sa.FLOAT(), nullable=True))
        batch_op.add_column(sa.Column('recommended_at', sa.DATETIME(), nullable=False))

    op.drop_table('clarifications')
    # ### end Alembic commands ###
