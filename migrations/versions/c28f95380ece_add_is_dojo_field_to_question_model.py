"""Add is_dojo field to Question model

Revision ID: c28f95380ece
Revises: a1437e688f24
Create Date: 2025-07-09 19:31:27.518312

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c28f95380ece'
down_revision = 'a1437e688f24'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('groups', schema=None) as batch_op:
        batch_op.drop_index('idx_groups_invite_code', sqlite_where=sa.text('invite_code IS NOT NULL'))
        batch_op.drop_constraint('uq_groups_invite_code', type_='unique')

    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_dojo', sa.Boolean(), nullable=False, server_default='0'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.drop_column('is_dojo')

    with op.batch_alter_table('groups', schema=None) as batch_op:
        batch_op.create_unique_constraint('uq_groups_invite_code', ['invite_code'])
        batch_op.create_index('idx_groups_invite_code', ['invite_code'], unique=1, sqlite_where=sa.text('invite_code IS NOT NULL'))

    # ### end Alembic commands ###
