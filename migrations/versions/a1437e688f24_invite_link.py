"""invite link

Revision ID: a1437e688f24
Revises: 
Create Date: 2025-07-04 22:14:37.133940

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1437e688f24'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('groups', schema=None) as batch_op:
        batch_op.add_column(sa.Column('invite_code', sa.String(length=8), nullable=True))
        batch_op.create_unique_constraint('uq_groups_invite_code', ['invite_code'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('groups', schema=None) as batch_op:
        batch_op.drop_constraint('uq_groups_invite_code', type_='unique')
        batch_op.drop_column('invite_code')

    # ### end Alembic commands ###
