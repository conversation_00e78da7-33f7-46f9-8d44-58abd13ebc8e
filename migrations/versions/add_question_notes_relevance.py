"""Add question notes relevance relationships

Revision ID: add_question_notes_relevance
Revises: latest
Create Date: 2025-07-16 22:50:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_question_notes_relevance'
down_revision = None  # Will be set automatically
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('question_notes_relevance',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('question_id', sa.Integer(), nullable=False),
    sa.Column('notes_chunk_id', sa.Integer(), nullable=False),
    sa.Column('relevance_type', sa.String(length=50), nullable=False),
    sa.Column('strength', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], name='fk_notes_relevance_question_id'),
    sa.ForeignKeyConstraint(['notes_chunk_id'], ['notes_chunks.id'], name='fk_notes_relevance_chunk_id'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('question_id', 'notes_chunk_id', name='unique_notes_relevance')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('question_notes_relevance')
    # ### end Alembic commands ###
