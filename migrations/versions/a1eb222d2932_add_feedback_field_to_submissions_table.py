"""Add feedback field to submissions table

Revision ID: a1eb222d2932
Revises: 6bfaf68be3eb
Create Date: 2025-07-21 06:35:49.852128

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1eb222d2932'
down_revision = '6bfaf68be3eb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('submissions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('feedback', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('submissions', schema=None) as batch_op:
        batch_op.drop_column('feedback')

    # ### end Alembic commands ###
