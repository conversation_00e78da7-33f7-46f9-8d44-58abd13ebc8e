"""merge heads for notes relevance

Revision ID: fbcde57ae16e
Revises: 1d8fafb3ad43, add_question_notes_relevance
Create Date: 2025-07-16 22:49:38.336603

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fbcde57ae16e'
down_revision = ('1d8fafb3ad43', 'add_question_notes_relevance')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
