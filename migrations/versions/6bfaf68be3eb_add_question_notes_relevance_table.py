"""add question notes relevance table

Revision ID: 6bfaf68be3eb
Revises: fbcde57ae16e
Create Date: 2025-07-16 22:51:28.698612

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6bfaf68be3eb'
down_revision = 'fbcde57ae16e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('question_notes_relevance',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('question_id', sa.Integer(), nullable=False),
    sa.Column('notes_chunk_id', sa.Integer(), nullable=False),
    sa.Column('relevance_type', sa.String(length=50), nullable=False),
    sa.Column('strength', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], name='fk_notes_relevance_question_id'),
    sa.ForeignKeyConstraint(['notes_chunk_id'], ['notes_chunks.id'], name='fk_notes_relevance_chunk_id'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('question_id', 'notes_chunk_id', name='unique_notes_relevance')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('question_notes_relevance')
    # ### end Alembic commands ###
