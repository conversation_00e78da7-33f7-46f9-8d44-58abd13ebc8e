"""Add question relevance relationships

Revision ID: ed0d2f398f44
Revises: c28f95380ece
Create Date: 2025-07-09 20:27:56.098078

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ed0d2f398f44'
down_revision = 'c28f95380ece'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('question_relevance',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('question_id', sa.Integer(), nullable=False),
    sa.Column('relevant_question_id', sa.Integer(), nullable=False),
    sa.Column('relevance_type', sa.String(length=50), nullable=False),
    sa.Column('strength', sa.Integer(), nullable=False),
    sa.<PERSON>umn('created_at', sa.DateTime(), nullable=False),
    sa.CheckConstraint('question_id != relevant_question_id', name='no_self_relevance'),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], name='fk_relevance_question_id'),
    sa.ForeignKeyConstraint(['relevant_question_id'], ['questions.id'], name='fk_relevance_relevant_id'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('question_id', 'relevant_question_id', name='unique_relevance')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('question_relevance')
    # ### end Alembic commands ###
