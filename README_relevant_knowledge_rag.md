# RAG-Based Relevant Knowledge Population

This script uses Retrieval-Augmented Generation (RAG) with Sentence Transformers and FAISS to automatically find and populate relevant knowledge relationships between questions and DOJO questions.

## Features

- **Semantic Understanding**: Uses advanced NLP models to understand conceptual relationships
- **Efficient Search**: FAISS vector database for fast similarity search
- **Smart Filtering**: Only links questions from the same subject
- **Relevance Scoring**: Automatic relevance type and strength assignment
- **Batch Processing**: Handles large datasets efficiently
- **Safety Features**: Dry-run mode and existing relationship protection

## Installation

1. Install additional dependencies:
```bash
pip install -r requirements_rag.txt
```

2. The script will automatically download the sentence transformer model on first run.

## Usage

### Basic Usage (All Subjects)
```bash
python populate_relevant_knowledge.py
```

### Chemistry Only
```bash
python populate_relevant_knowledge.py --subject-id 3
```

### Advanced Options
```bash
# Overwrite existing relationships
python populate_relevant_knowledge.py --overwrite

# Use different similarity threshold (0.0-1.0)
python populate_relevant_knowledge.py --threshold 0.4

# Use different model
python populate_relevant_knowledge.py --model all-mpnet-base-v2

# Dry run to see what would be done
python populate_relevant_knowledge.py --dry-run
```

## How It Works

1. **Text Extraction**: Combines question title, description, topic, and marking points into rich text
2. **Embedding Generation**: Uses sentence transformers to create semantic embeddings
3. **Similarity Search**: FAISS finds the most semantically similar DOJO questions
4. **Smart Filtering**: Filters by subject, excludes self-references, applies similarity threshold
5. **Relationship Creation**: Creates QuestionRelevance records with appropriate types and strengths

## Relevance Types

- **similar_concept**: High similarity (>0.7) or same topic
- **related_topic**: Medium similarity (0.6-0.8) 
- **general**: Lower similarity but still relevant

## Relevance Strength (1-5 stars)

- **5 stars**: Very high similarity (≥0.9)
- **4 stars**: High similarity (≥0.8)
- **3 stars**: Good similarity (≥0.7)
- **2 stars**: Moderate similarity (≥0.6)
- **1 star**: Basic similarity (≥threshold)

## Models Available

- **all-MiniLM-L6-v2** (default): Fast, good quality, 384 dimensions
- **all-mpnet-base-v2**: Higher quality, slower, 768 dimensions
- **paraphrase-MiniLM-L6-v2**: Good for finding paraphrases

## Performance

- Processing ~1000 questions: 5-10 minutes
- Memory usage: ~500MB-1GB depending on model
- First run downloads model (~100MB)

## Safety Features

- **Existing Relationship Protection**: Won't overwrite unless `--overwrite` specified
- **Subject Filtering**: Only links questions within the same subject
- **Self-Reference Prevention**: Questions won't link to themselves
- **Batch Commits**: Database commits in batches for safety
- **Rollback on Error**: Automatic rollback if errors occur

## Troubleshooting

### No DOJO Questions Found
Make sure you have questions with `is_dojo=True` in your database.

### Memory Issues
Use the smaller model: `--model all-MiniLM-L6-v2`

### Low Quality Results
- Increase threshold: `--threshold 0.4`
- Use better model: `--model all-mpnet-base-v2`

### Performance Issues
- Process one subject at a time: `--subject-id 3`
- Use faster model: `--model all-MiniLM-L6-v2`

## Example Output

```
2024-01-15 10:30:15 - INFO - Loading sentence transformer model: all-MiniLM-L6-v2
2024-01-15 10:30:20 - INFO - Model loaded successfully
2024-01-15 10:30:20 - INFO - Loading DOJO questions from database...
2024-01-15 10:30:21 - INFO - Loaded 150 DOJO questions
2024-01-15 10:30:21 - INFO - Creating embeddings for DOJO questions...
2024-01-15 10:30:35 - INFO - Created FAISS index with 150 vectors
2024-01-15 10:30:35 - INFO - Starting relevant knowledge population...
2024-01-15 10:31:20 - INFO - Processed 50 questions, created 142 relationships
2024-01-15 10:32:05 - INFO - Completed! Processed: 89, Skipped: 23, Created relationships: 267
```

## Next Steps

After running the script:

1. **Review Results**: Check the admin interface to see created relationships
2. **Manual Adjustments**: Use the admin relevance management to fine-tune
3. **Test User Experience**: Check how the relevant knowledge appears on question pages
4. **Iterate**: Adjust threshold or model if needed and re-run with `--overwrite`
