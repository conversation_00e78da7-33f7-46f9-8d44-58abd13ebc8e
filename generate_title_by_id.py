#!/usr/bin/env python3
"""
Standalone script to generate and update question titles by ID or range of IDs.
Usage:
  Single question: python generate_title_by_id.py <question_id>
  Range of questions: python generate_title_by_id.py <start_id> <end_id>
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flask app and models
from app import app
from models import Question
from routes.ai_helpers import generate_and_update_question_title

def generate_title_for_single_question(question_id):
    """Generate title for a single question"""
    try:
        # Check if question exists
        question = Question.query.get(question_id)
        if not question:
            print(f"⚠️  Question with ID {question_id} not found - skipping")
            return False

        print(f"Question ID {question_id}:")
        print(f"  Current title: '{question.title}'")
        print("  Generating new title...")

        # Generate and update the title
        new_title = generate_and_update_question_title(question_id)

        print(f"  ✅ New title: '{new_title}'")
        return True

    except Exception as e:
        print(f"  ❌ Error generating title for question {question_id}: {e}")
        return False

def main():
    if len(sys.argv) not in [2, 3]:
        print("Usage:")
        print("  Single question: python generate_title_by_id.py <question_id>")
        print("  Range of questions: python generate_title_by_id.py <start_id> <end_id>")
        print()
        print("Examples:")
        print("  python generate_title_by_id.py 5")
        print("  python generate_title_by_id.py 1 10")
        sys.exit(1)

    try:
        if len(sys.argv) == 2:
            # Single question mode
            question_id = int(sys.argv[1])
            start_id = end_id = question_id
        else:
            # Range mode
            start_id = int(sys.argv[1])
            end_id = int(sys.argv[2])

            if start_id > end_id:
                print("Error: Start ID must be less than or equal to end ID")
                sys.exit(1)

    except ValueError:
        print("Error: Question IDs must be numbers")
        sys.exit(1)

    with app.app_context():
        if start_id == end_id:
            # Single question
            print(f"Generating title for question {start_id}...")
            print("=" * 50)
            success = generate_title_for_single_question(start_id)
            if not success:
                sys.exit(1)
        else:
            # Range of questions
            total_questions = end_id - start_id + 1
            print(f"Generating titles for questions {start_id} to {end_id} ({total_questions} questions)...")
            print("=" * 70)

            successful = 0
            failed = 0

            for question_id in range(start_id, end_id + 1):
                if generate_title_for_single_question(question_id):
                    successful += 1
                else:
                    failed += 1

                # Add a small delay to avoid overwhelming the API
                if question_id < end_id:
                    time.sleep(1)
                    print()

            print("=" * 70)
            print(f"Summary:")
            print(f"  ✅ Successfully generated: {successful} titles")
            print(f"  ❌ Failed: {failed} questions")
            print(f"  📊 Total processed: {successful + failed} questions")

            if failed > 0:
                sys.exit(1)

if __name__ == "__main__":
    main()
