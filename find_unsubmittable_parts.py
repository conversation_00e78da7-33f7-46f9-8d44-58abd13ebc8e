#!/usr/bin/env python3
"""
<PERSON><PERSON>t to find parts in the database that have answers indicating "Diagram required" or similar.
These parts will be marked as unsubmittable and their IDs saved to unsubmittable.txt.
"""

import os
import sys
import re
from models import db, Part

def find_unsubmittable_parts():
    """
    Scan all parts in the database and identify those with answers that indicate
    a diagram is required or similar non-text responses.
    """
    # Patterns that indicate a part requires a diagram or is unsubmittable
    diagram_patterns = [
        r'diagram\s+required',
        r'draw\s+a?\s*diagram',
        r'sketch\s+required',
        r'drawing\s+required',
        r'figure\s+required',
        r'graph\s+required',
        r'chart\s+required',
        r'illustration\s+required',
        r'visual\s+representation',
        r'cannot\s+be\s+answered\s+in\s+text',
        r'requires?\s+visual',
        r'needs?\s+diagram',
        r'must\s+draw',
        r'show\s+graphically',
        r'plot\s+required',
        r'construct\s+diagram',
        r'draw\s+and\s+label',
        r'sketch\s+and\s+label',
    ]
    
    # Compile patterns for case-insensitive matching
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in diagram_patterns]
    
    unsubmittable_part_ids = []
    
    try:
        # Query all parts from the database
        all_parts = Part.query.all()
        print(f"Scanning {len(all_parts)} parts in the database...")
        
        for part in all_parts:
            if part.answer:
                answer_text = part.answer.strip()
                
                # Check if the answer matches any of our patterns
                for pattern in compiled_patterns:
                    if pattern.search(answer_text):
                        unsubmittable_part_ids.append(part.id)
                        print(f"Found unsubmittable part {part.id}: {answer_text[:100]}...")
                        break
        
        # Write the unsubmittable part IDs to a file
        with open('unsubmittable.txt', 'w') as f:
            for part_id in unsubmittable_part_ids:
                f.write(f"{part_id}\n")

        # Also generate a JavaScript array for easy inclusion in templates
        js_array = f"const unsubmittableParts = {unsubmittable_part_ids};"
        with open('unsubmittable_parts.js', 'w') as f:
            f.write(js_array)

        print(f"\nFound {len(unsubmittable_part_ids)} unsubmittable parts.")
        print(f"Part IDs saved to unsubmittable.txt")
        print(f"JavaScript array saved to unsubmittable_parts.js")
        print(f"Unsubmittable part IDs: {unsubmittable_part_ids}")

        return unsubmittable_part_ids
        
    except Exception as e:
        print(f"Error scanning database: {e}")
        return []

if __name__ == "__main__":
    # Set up Flask app context if needed
    try:
        from app import app
        with app.app_context():
            unsubmittable_parts = find_unsubmittable_parts()
    except ImportError:
        print("Could not import Flask app. Make sure you're running this from the correct directory.")
        sys.exit(1)
