from flask import render_template, request, redirect, url_for, flash, session
from sqlalchemy import func

from models import db, Group, User, Submission, Part # Import necessary models
from .utils import login_required, update_user_activity, error_logger, app_logger # Import helpers

def register_group_routes(app, db, session):

    @app.route('/groups', methods=['GET'])
    @login_required
    def groups():
        """Displays a list of all groups and user's groups."""
        user_id = session['user_id']
        user = User.query.get(user_id)
        if not user: 
             flash("User not found.", "error")
             return redirect(url_for('login'))

        user_groups_ids = {group.id for group in user.groups} # Set for efficient lookup

        # Get ALL groups and calculate their statistics (Consider pagination for large number of groups)
        all_groups = Group.query.order_by(Group.name).all()

        groups_with_stats = []
        try:
            for group in all_groups:
                total_problems = 0
                total_points = 0
                member_count = len(group.members) # Efficiently get count

                # Calculate stats per group (can be slow if many members/submissions)
                # Consider calculating stats periodically or caching if performance is an issue.
                for member in group.members:
                    # Count distinct questions with score > 0
                    problems_completed_query = db.session.query(func.count(Submission.question_id.distinct()))\
                        .filter(Submission.user_id == member.id, Submission.score > 0)\
                        .scalar()
                    problems_completed = problems_completed_query or 0

                    # Sum of MAX scores per part for this member
                    max_scores_subquery = db.session.query(
                        Submission.part_id,
                        func.max(Submission.score).label('max_score')
                    ).filter(
                        Submission.user_id == member.id
                    ).group_by(Submission.part_id).subquery()

                    member_points_query = db.session.query(
                        func.sum(max_scores_subquery.c.max_score)
                    ).scalar()
                    member_points = member_points_query or 0

                    total_problems += problems_completed
                    total_points += member_points

                groups_with_stats.append({
                    'group': group,
                    'total_problems': total_problems,
                    'total_points': total_points,
                    'member_count': member_count,
                    'is_member': group.id in user_groups_ids
                })

            # Sort groups: User's groups first, then by problems, points, members, name
            sorted_groups = sorted(groups_with_stats,
                                 key=lambda x: (not x['is_member'], -x['total_problems'], -x['total_points'], -x['member_count'], x['group'].name))

            return render_template('groups.html',
                                 groups=sorted_groups,
                                 user_groups=user.groups) # Pass user's actual group objects if needed by template

        except Exception as e:
            error_logger.exception("Error loading groups page")
            flash("Error loading groups.", "error")
            return redirect(url_for('index'))


    @app.route('/groups/create', methods=['GET', 'POST'])
    @login_required
    def create_group():
        """Handles creation of a new group."""
        user = User.query.get(session['user_id'])
        if not user:
             flash("User not found.", "error")
             return redirect(url_for('login'))

        # Check group ownership limit (e.g., max 5 owned groups)
        # Use relationship count for efficiency
        owned_group_count = db.session.query(func.count(Group.id)).filter(Group.owner_id == user.id).scalar()
        MAX_OWNED_GROUPS = 5 # Define limit

        if owned_group_count >= MAX_OWNED_GROUPS:
            flash(f'You have reached the maximum limit of {MAX_OWNED_GROUPS} owned groups.', 'warning')
            return redirect(url_for('groups')) 

        if request.method == 'POST':
            name = request.form.get('name')
            description = request.form.get('description', '')

            if not name:
                flash('Group name is required.', 'error')
                return render_template('create_group.html') # Stay on page with error

            if Group.query.filter_by(name=name).first():
                flash(f"A group named '{name}' already exists.", "warning")
                return render_template('create_group.html', name=name, description=description)

            try:
                # Create group, setting owner_id
                new_group = Group(name=name, description=description, owner_id=user.id)
                # Add the owner as a member automatically
                new_group.members.append(user)
                db.session.add(new_group)
                db.session.commit()

                update_user_activity(user.id) # Update activity on creation
                flash(f'Group "{name}" created successfully!', 'success')
                app_logger.info(f"User {user.username} created group '{name}' (ID: {new_group.id})")
                return redirect(url_for('groups')) # Redirect to groups list

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error creating group '{name}' for user {user.username}: {e}")
                flash(f"Error creating group: {str(e)}", "error")
                return render_template('create_group.html', name=name, description=description) # Stay on page

        # GET request
        return render_template('create_group.html')


    @app.route('/groups/join/<int:group_id>', methods=['POST']) # Use POST for actions
    @login_required
    def join_group(group_id):
        """Allows a user to join a group."""
        group = Group.query.get_or_404(group_id)
        user = User.query.get(session['user_id'])
        if not user: return redirect(url_for('login')) # Should be caught by decorator

        if user in group.members:
            flash('You are already a member of this group.', 'info')
        else:
            try:
                group.members.append(user)
                db.session.commit()
                update_user_activity(user.id) # Update activity on join
                flash(f'You have joined the group "{group.name}"!', 'success')
                app_logger.info(f"User {user.username} joined group '{group.name}' (ID: {group.id})")
            except Exception as e:
                 db.session.rollback()
                 error_logger.exception(f"Error joining group {group_id} for user {user.username}: {e}")
                 flash("Error joining group.", "error")

        # Redirect back to the group details or groups list
        return redirect(request.referrer or url_for('group_details', group_id=group_id))


    @app.route('/groups/join-by-code', methods=['GET', 'POST'])
    @login_required
    def join_group_by_code():
        """Allow users to join a group using an invite code"""
        user = User.query.get(session['user_id'])
        if not user:
            flash("User not found.", "error")
            return redirect(url_for('login'))

        if request.method == 'POST':
            invite_code = request.form.get('invite_code', '').strip().upper()

            if not invite_code:
                flash('Please enter an invite code.', 'error')
                return render_template('join_by_code.html')

            # Find group with this invite code
            group = Group.query.filter_by(invite_code=invite_code).first()

            if not group:
                flash('Invalid invite code. Please check the code and try again.', 'error')
                return render_template('join_by_code.html', invite_code=invite_code)

            # Check if user is already a member
            if user in group.members:
                flash(f'You are already a member of "{group.name}".', 'info')
                return redirect(url_for('group_details', group_id=group.id))

            try:
                # Add user to group
                group.members.append(user)
                db.session.commit()
                update_user_activity(user.id)

                flash(f'Successfully joined "{group.name}"!', 'success')
                app_logger.info(f"User {user.username} joined group '{group.name}' using invite code {invite_code}")
                return redirect(url_for('group_details', group_id=group.id))

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error joining group with invite code {invite_code} for user {user.username}: {e}")
                flash("Error joining group. Please try again.", "error")
                return render_template('join_by_code.html', invite_code=invite_code)

        # GET request - show the form
        return render_template('join_by_code.html')


    @app.route('/groups/leave/<int:group_id>', methods=['POST'])
    @login_required
    def leave_group(group_id):
        """Allows a user to leave a group."""
        group = Group.query.get_or_404(group_id)
        user = User.query.get(session['user_id'])
        if not user: return redirect(url_for('login'))

        # Prevent owner from leaving? Or handle ownership transfer/deletion?
        if group.owner_id == user.id:
             flash('Group owners cannot leave their own group. You can delete it instead.', 'warning')
             return redirect(url_for('group_details', group_id=group_id))

        if user not in group.members:
            flash('You are not a member of this group.', 'warning')
        else:
            try:
                group.members.remove(user)
                db.session.commit()
                update_user_activity(user.id) # Update activity on leave
                flash(f'You have left the group "{group.name}".', 'success')
                app_logger.info(f"User {user.username} left group '{group.name}' (ID: {group.id})")
            except Exception as e:
                 db.session.rollback()
                 error_logger.exception(f"Error leaving group {group_id} for user {user.username}: {e}")
                 flash("Error leaving group.", "error")

        return redirect(url_for('groups')) # Redirect to groups list


    @app.route('/group/<int:group_id>')
    @login_required # Require login to view group details?
    def group_details(group_id):
        """Displays details and member statistics for a specific group."""
        group = Group.query.get_or_404(group_id)
        user_id = session['user_id']

        # Check if the current user is a member (optional, for conditional display)
        is_member = User.query.filter(User.id == user_id, User.groups.contains(group)).count() > 0

        # Get member statistics and sort them (similar logic to /groups, but for one group)
        sorted_members = []
        try:
            for member in group.members:
                # Count distinct questions with score > 0
                problems_completed_query = db.session.query(func.count(Submission.question_id.distinct()))\
                    .filter(Submission.user_id == member.id, Submission.score > 0)\
                    .scalar()
                problems_completed = problems_completed_query or 0

                # Sum of MAX scores per part
                max_scores_subquery = db.session.query(
                    Submission.part_id,
                    func.max(Submission.score).label('max_score')
                ).filter(Submission.user_id == member.id).group_by(Submission.part_id).subquery()

                total_points_query = db.session.query(func.sum(max_scores_subquery.c.max_score)).scalar()
                total_points = total_points_query or 0

                sorted_members.append({
                    'user': member, # Pass the whole user object
                    'problems_completed': problems_completed,
                    'total_points': total_points
                })

            # Sort members by total points (descending), then problems, then username
            sorted_members = sorted(sorted_members, key=lambda x: (-x['total_points'], -x['problems_completed'], x['user'].username))

            return render_template('group_details.html',
                                 group=group,
                                 sorted_members=sorted_members,
                                 is_member=is_member,
                                 current_user_id=user_id) # Pass current user ID for owner checks in template

        except Exception as e:
            error_logger.exception(f"Error loading details for group {group_id}")
            flash("Error loading group details.", "error")
            return redirect(url_for('groups'))


    @app.route('/groups/delete/<int:group_id>', methods=['POST'])
    @login_required
    def delete_group(group_id):
        """Deletes a group (only owner)."""
        group = Group.query.get_or_404(group_id)
        user_id = session['user_id']

        if group.owner_id != user_id:
            flash('You do not have permission to delete this group.', 'danger')
            return redirect(url_for('group_details', group_id=group_id))

        try:
            group_name = group.name # Get name before deleting
            # Handle related data:
            # - Problem sets shared ONLY with this group? Decide whether to delete or unshare.
            # - Remove members (SQLAlchemy might handle this via cascade if configured)
            # For simplicity, let's just delete the group for now. Association table entries should cascade delete.
            db.session.delete(group)
            db.session.commit()
            flash(f'Group "{group_name}" deleted successfully.', 'success')
            app_logger.info(f"User {session.get('username')} deleted group '{group_name}' (ID: {group_id})")
            return redirect(url_for('groups'))
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting group {group_id} by user {user_id}: {str(e)}")
            flash(f'Error deleting group: {str(e)}', 'danger')
            return redirect(url_for('groups'))
