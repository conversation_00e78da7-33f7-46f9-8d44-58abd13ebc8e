from flask import render_template, request, redirect, url_for, flash, session, jsonify
from sqlalchemy import func, and_
from datetime import datetime
import markdown

from models import db, Subject, Question, Topic, Part, Submission, User
from .utils import login_required, error_logger # Import necessary utilities/models
from .ai_helpers import generate_marking_points

def register_vault_routes(app, db, session):

    @app.route("/vault", methods=['GET'])
    def vault():
        # Check if user is logged in and needs onboarding
        if 'user_id' in session:
            user = User.query.get(session['user_id'])
            if user and not (user.onboarding_completed is True):
                return redirect(url_for('onboarding'))

        subject_filter = request.args.get('subject_id')
        topic_filter = request.args.get('topic_id')

        query = Question.query

        # Apply subject filter if specified
        if subject_filter:
            query = query.join(Topic).filter(Topic.subject_id == subject_filter)

        # Apply topic filter if specified
        if topic_filter:
            query = query.filter(Question.topic_id == topic_filter)

        # Execute the query and get the results
        questions = query.all()

        # Calculate status for each question if user is logged in
        if 'user_id' in session:
            for question in questions:
                # Get all parts associated with the current question
                parts = Part.query.filter_by(question_id=question.id).all()
                print(question.id)
                total_possible_score = sum(part.score for part in parts)

                total_score = 0
                for part in parts:
                    # Find the latest submission for each part
                    latest_submission = (
                        Submission.query.filter_by(user_id=session['user_id'], part_id=part.id)
                        .order_by(Submission.timestamp.desc())
                        .first()
                    )
                    if latest_submission and latest_submission.score is not None:
                        total_score += latest_submission.score

                if total_possible_score == 0:
                    question.status = 0  # Not attempted
                elif total_score == total_possible_score:
                    question.status = 2  # Correct
                elif total_score == 0:
                    question.status = 0  # Incorrect
                elif 0 < total_score < total_possible_score:
                    question.status = 1  # Partial
                else:
                    question.status = 0  # No submission found

        else: # User not logged in
            for question in questions:
                question.status = 0 # Default status

        # Get all subjects and topics for filtering dropdowns
        subjects = Subject.query.order_by(Subject.id).all()
        # Optimize topic fetching if a subject is selected
        if subject_filter:
            topics = Topic.query.filter_by(subject_id=subject_filter).order_by(Topic.id).all()
        else:
            topics = Topic.query.order_by(Topic.id).all() # Or maybe only show topics for selected subject?

        return render_template("vault.html",
                            questions=questions,
                            subjects=subjects,
                            topics=topics,
                            selected_subject=subject_filter,
                            selected_topic=topic_filter)


    @app.route("/vault/<int:question_id>")
    @login_required
    def load_question(question_id):
        question = Question.query.get_or_404(question_id)
        feedback = request.args.get('feedback')  # Retrieve feedback from query params

        for part in question.parts:
            part.description_html = markdown.markdown(part.description.replace("_", r"\_"))
            # print(f"RAW: {part.description}\n MARKDOWN: {part.description_html}")

        return render_template("question.html",
                             question=question,
                             feedback=feedback)


    @app.route("/submissions", methods=['GET'])
    @login_required # Viewing submissions should require login
    def submissions():
        # Users can only view their own submissions
        current_user_id = session['user_id']
        current_user = User.query.get(current_user_id)

        # Remove the search functionality - users can only see their own submissions
        user_to_filter = current_user
        user_for_header = current_user

        # Base query joining necessary tables for filtering
        # Only show current user's submissions
        query = Submission.query.join(Part, Submission.part_id == Part.id)\
                                .join(Question, Part.question_id == Question.id)\
                                .join(Topic, Question.topic_id == Topic.id)\
                                .join(Subject, Topic.subject_id == Subject.id)\
                                .join(User, Submission.user_id == User.id)\
                                .filter(Submission.user_id == current_user_id)

        # --- Filtering ---
        # selected_question_id = request.args.get('question_id')
        selected_topic_id = request.args.get('topic_id')
        selected_subject_id = request.args.get('subject_id')

        # if selected_question_id: # can consider if we want to implement a data visualisation tool
        #     try:
        #         query = query.filter(Question.id == int(selected_question_id))
        #     except ValueError:
        #         flash("Invalid Question ID.", "warning")
        #         selected_question_id = None # Reset invalid filter

        if selected_subject_id:
            try:
                query = query.filter(Subject.id == int(selected_subject_id))
            except ValueError:
                flash("Invalid Subject ID.", "warning")
                selected_subject_id = None # Reset invalid filter

        if selected_topic_id:
            # Ensure topic filter respects subject filter if applied
            if selected_subject_id:
                 topic_check = Topic.query.filter_by(id=int(selected_topic_id), subject_id=int(selected_subject_id)).first()
                 if not topic_check:
                     flash("Topic does not belong to the selected subject.", "warning")
                     selected_topic_id = None # Reset topic filter
                 else:
                     try:
                         query = query.filter(Topic.id == int(selected_topic_id))
                     except ValueError:
                         flash("Invalid Topic ID.", "warning")
                         selected_topic_id = None # Reset invalid filter
            else:
                 # Filter by topic only if no subject is selected or if topic is valid for subject
                 try:
                     query = query.filter(Topic.id == int(selected_topic_id))
                 except ValueError:
                     flash("Invalid Topic ID.", "warning")
                     selected_topic_id = None # Reset invalid filter

        # --- Sorting ---
        # Add sorting options later
        query = query.order_by(Submission.timestamp.desc())

        # --- Pagination (Add later) ---
        submissions_list = query.limit(50).all() # Limit results for now

        # --- Data for Filters ---
        # Get distinct subjects and topics associated with submissions (all or filtered by user)

        # Subquery to get distinct topic IDs from current user's submissions
        submitted_topic_ids_query = db.session.query(Question.topic_id)\
                                        .join(Submission, Question.id == Submission.question_id)\
                                        .filter(Submission.user_id == current_user_id)
        submitted_topic_ids_sq = submitted_topic_ids_query.distinct().subquery()

        # Subquery to get distinct subject IDs from those topics
        submitted_subject_ids_sq = db.session.query(Topic.subject_id)\
                                            .join(submitted_topic_ids_sq, Topic.id == submitted_topic_ids_sq.c.topic_id)\
                                            .distinct().subquery()

        # Fetch Subject objects based on the subquery
        subjects_for_filter = Subject.query.join(submitted_subject_ids_sq, Subject.id == submitted_subject_ids_sq.c.subject_id)\
                                            .order_by(Subject.name).all()

        # Fetch Topic objects based on the submitted topics subquery
        topics_query = Topic.query.join(submitted_topic_ids_sq, Topic.id == submitted_topic_ids_sq.c.topic_id)
        # If a subject is selected, further filter topics by that subject
        if selected_subject_id:
            topics_query = topics_query.filter(Topic.subject_id == int(selected_subject_id))
        topics_for_filter = topics_query.order_by(Topic.name).all()

        # questions_for_filter = [] # Commented out as requested

        return render_template("submissions.html",
                             user=user_for_header, # Pass the current user for the header
                             submissions=submissions_list,
                             subjects_for_filter=subjects_for_filter,
                             topics_for_filter=topics_for_filter,
                             selected_subject_id=int(selected_subject_id) if selected_subject_id else None,
                             selected_topic_id=int(selected_topic_id) if selected_topic_id else None)


    @app.route("/submission/<int:submission_id>")
    @login_required
    def submission_details(submission_id):
        submission = Submission.query.get_or_404(submission_id)

        # Users can only view their own submissions
        if submission.user_id != session['user_id']:
            flash('You can only view your own submissions.', 'error')
            return redirect(url_for('submissions'))

        return render_template("submission_details.html", submission=submission)
