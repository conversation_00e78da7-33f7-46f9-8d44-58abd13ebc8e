"""
Intelligent Recommendation Engine
Combines spaced repetition, performance analysis, and adaptive difficulty 
to suggest the most beneficial problems for each user to review.
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy import func, and_, or_, desc, not_
from models import (db, User, Submission, Question, Part, Topic, Subject, 
                   ReviewSchedule, PerformanceAnalytics, RecommendationHistory)
from .spaced_repetition import SpacedRepetitionEngine, get_todays_reviews
from .performance_analysis import PerformanceAnalysisEngine
from .utils import app_logger


class RecommendationEngine:
    """Core engine for generating intelligent problem recommendations"""
    
    # Recommendation types
    CRITICAL_REVIEW = 'critical'
    REINFORCEMENT_PRACTICE = 'reinforcement'
    MASTERY_MAINTENANCE = 'mastery'
    NEW_CHALLENGES = 'new'
    
    # Priority weights for different recommendation factors
    WEIGHTS = {
        'spaced_repetition': 0.4,
        'performance_weakness': 0.3,
        'learning_velocity': 0.2,
        'user_preference': 0.1
    }

    @staticmethod
    def generate_recommendations(user_id: int, limit: int = 10) -> List[Dict]:
        """
        Generate personalized problem recommendations for a user
        
        Args:
            user_id: User ID
            limit: Maximum number of recommendations to return
            
        Returns:
            List of recommendation dictionaries sorted by priority
        """
        try:
            recommendations = []
            
            # 1. Critical Review - Due/overdue spaced repetition items
            critical_recommendations = RecommendationEngine._get_critical_review_recommendations(user_id)
            recommendations.extend(critical_recommendations)
            
            # 2. Reinforcement Practice - Weak areas that need attention
            reinforcement_recommendations = RecommendationEngine._get_reinforcement_recommendations(user_id)
            recommendations.extend(reinforcement_recommendations)
            
            # 3. Mastery Maintenance - Strong areas to maintain
            mastery_recommendations = RecommendationEngine._get_mastery_maintenance_recommendations(user_id)
            recommendations.extend(mastery_recommendations)
            
            # 4. New Challenges - Unexplored problems in strong areas
            new_challenge_recommendations = RecommendationEngine._get_new_challenge_recommendations(user_id)
            recommendations.extend(new_challenge_recommendations)
            
            # Sort by priority score and limit results
            recommendations.sort(key=lambda x: x['priority_score'], reverse=True)
            final_recommendations = recommendations[:limit]
            
            # Log recommendations for tracking
            RecommendationEngine._log_recommendations(user_id, final_recommendations)
            
            return final_recommendations
            
        except Exception as e:
            app_logger.error(f"Error generating recommendations for user {user_id}: {e}")
            return []

    @staticmethod
    def _get_critical_review_recommendations(user_id: int) -> List[Dict]:
        """Get critical review recommendations based on spaced repetition"""
        try:
            due_reviews = get_todays_reviews(user_id)
            recommendations = []
            
            for review_info in due_reviews:
                review = review_info['review_schedule']
                question = review_info['question']
                part = review_info['part']
                
                # Calculate priority based on how overdue and performance
                priority_score = review_info['priority_score']
                
                # Boost priority for very poor past performance
                if review.quality_score is not None and review.quality_score <= 2:
                    priority_score += 20
                
                recommendation = {
                    'type': RecommendationEngine.CRITICAL_REVIEW,
                    'question_id': question.id,
                    'part_id': part.id,
                    'question': question,
                    'part': part,
                    'topic': review_info['topic'],
                    'subject': review_info['subject'],
                    'priority_score': priority_score,
                    'reason': RecommendationEngine._generate_critical_reason(review_info),
                    'estimated_difficulty': RecommendationEngine._estimate_difficulty(user_id, question.id, part.id),
                    'review_schedule': review
                }
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            app_logger.error(f"Error getting critical review recommendations for user {user_id}: {e}")
            return []

    @staticmethod
    def _get_reinforcement_recommendations(user_id: int) -> List[Dict]:
        """Get reinforcement recommendations for weak areas"""
        try:
            weak_areas = PerformanceAnalysisEngine.identify_weak_areas(user_id)
            recommendations = []
            
            for weak_area in weak_areas[:3]:  # Focus on top 3 weak areas
                topic_id = weak_area['topic_id']
                
                # Find problems in this weak topic that haven't been attempted recently
                recent_cutoff = datetime.now() - timedelta(days=7)
                
                # Get questions in this topic that either haven't been attempted or were failed recently
                weak_questions = db.session.query(Question, Part).join(Part).filter(
                    Question.topic_id == topic_id
                ).outerjoin(
                    Submission, and_(
                        Submission.question_id == Question.id,
                        Submission.part_id == Part.id,
                        Submission.user_id == user_id
                    )
                ).filter(
                    or_(
                        Submission.id.is_(None),  # Never attempted
                        and_(
                            Submission.timestamp >= recent_cutoff,
                            Submission.score < (Part.score * 0.6)  # Failed recently (< 60%)
                        )
                    )
                ).limit(5).all()
                
                for question, part in weak_questions:
                    priority_score = weak_area['priority_score'] * 0.8  # Slightly lower than critical
                    
                    recommendation = {
                        'type': RecommendationEngine.REINFORCEMENT_PRACTICE,
                        'question_id': question.id,
                        'part_id': part.id,
                        'question': question,
                        'part': part,
                        'topic': question.topic,
                        'subject': question.topic.subject if question.topic else None,
                        'priority_score': priority_score,
                        'reason': f"Strengthen your understanding of {weak_area['topic_name']} (current success rate: {weak_area['success_rate']:.1%})",
                        'estimated_difficulty': RecommendationEngine._estimate_difficulty(user_id, question.id, part.id),
                        'weak_area_info': weak_area
                    }
                    recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            app_logger.error(f"Error getting reinforcement recommendations for user {user_id}: {e}")
            return []

    @staticmethod
    def _get_mastery_maintenance_recommendations(user_id: int) -> List[Dict]:
        """Get mastery maintenance recommendations for strong areas"""
        try:
            strong_areas = PerformanceAnalysisEngine.identify_strong_areas(user_id)
            recommendations = []
            
            for strong_area in strong_areas[:2]:  # Focus on top 2 strong areas
                topic_id = strong_area['topic_id']
                
                # Find problems in this strong topic that are due for review (but not critical)
                upcoming_reviews = db.session.query(ReviewSchedule).filter(
                    ReviewSchedule.user_id == user_id,
                    ReviewSchedule.next_review_date <= date.today() + timedelta(days=3),
                    ReviewSchedule.next_review_date > date.today()
                ).join(Question).filter(
                    Question.topic_id == topic_id
                ).limit(3).all()
                
                for review in upcoming_reviews:
                    priority_score = 30 + (strong_area['mastery_score'] * 0.3)  # Medium priority
                    
                    recommendation = {
                        'type': RecommendationEngine.MASTERY_MAINTENANCE,
                        'question_id': review.question_id,
                        'part_id': review.part_id,
                        'question': review.question,
                        'part': review.part,
                        'topic': review.question.topic,
                        'subject': review.question.topic.subject if review.question.topic else None,
                        'priority_score': priority_score,
                        'reason': f"Maintain your strong performance in {strong_area['topic_name']} (current success rate: {strong_area['success_rate']:.1%})",
                        'estimated_difficulty': RecommendationEngine._estimate_difficulty(user_id, review.question_id, review.part_id),
                        'review_schedule': review
                    }
                    recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            app_logger.error(f"Error getting mastery maintenance recommendations for user {user_id}: {e}")
            return []

    @staticmethod
    def _get_new_challenge_recommendations(user_id: int) -> List[Dict]:
        """Get new challenge recommendations for unexplored problems"""
        try:
            # Get user's strong subjects for new challenges
            strong_areas = PerformanceAnalysisEngine.identify_strong_areas(user_id)
            recommendations = []
            
            if not strong_areas:
                # If no strong areas, recommend from user's preferred subjects
                user = User.query.get(user_id)
                if user and user.subjects_taken:
                    import json
                    try:
                        preferred_subjects = json.loads(user.subjects_taken)
                        subject_names = preferred_subjects if isinstance(preferred_subjects, list) else []
                    except:
                        subject_names = []
                else:
                    subject_names = []
                
                if subject_names:
                    # Get subjects by name
                    subjects = Subject.query.filter(Subject.name.in_(subject_names)).all()
                    subject_ids = [s.id for s in subjects]
                else:
                    # Default to all subjects
                    subject_ids = [s.id for s in Subject.query.all()]
            else:
                # Use strong areas' subjects
                subject_ids = list(set([area.get('subject_id') for area in strong_areas if area.get('subject_id')]))
            
            # Get unattempted questions from these subjects
            attempted_question_ids = db.session.query(Submission.question_id).filter_by(user_id=user_id).distinct().subquery()
            
            new_questions = db.session.query(Question, Part).join(Part).join(Topic).filter(
                Topic.subject_id.in_(subject_ids),
                ~Question.id.in_(attempted_question_ids)
            ).order_by(func.random()).limit(5).all()
            
            for question, part in new_questions:
                priority_score = 20  # Lower priority for new challenges
                
                recommendation = {
                    'type': RecommendationEngine.NEW_CHALLENGES,
                    'question_id': question.id,
                    'part_id': part.id,
                    'question': question,
                    'part': part,
                    'topic': question.topic,
                    'subject': question.topic.subject if question.topic else None,
                    'priority_score': priority_score,
                    'reason': f"Explore new problems in {question.topic.name if question.topic else 'this subject'}",
                    'estimated_difficulty': RecommendationEngine._estimate_difficulty(user_id, question.id, part.id)
                }
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            app_logger.error(f"Error getting new challenge recommendations for user {user_id}: {e}")
            return []

    @staticmethod
    def _generate_critical_reason(review_info: Dict) -> str:
        """Generate explanation for critical review recommendation"""
        days_overdue = review_info['days_overdue']
        quality_score = review_info['review_schedule'].quality_score
        
        if days_overdue > 0:
            reason = f"Review overdue by {days_overdue} day{'s' if days_overdue > 1 else ''}"
        else:
            reason = "Scheduled for review today"
        
        if quality_score is not None and quality_score <= 2:
            reason += f" - previous performance needs improvement"
        
        return reason

    @staticmethod
    def _estimate_difficulty(user_id: int, question_id: int, part_id: int) -> str:
        """Estimate difficulty level for a user based on their performance in the topic"""
        try:
            # Get the topic for this question
            question = Question.query.get(question_id)
            if not question or not question.topic_id:
                return 'medium'
            
            # Get user's performance in this topic
            performance = PerformanceAnalysisEngine.calculate_user_performance_by_topic(user_id, question.topic_id)
            
            if performance['total_attempts'] < 3:
                return 'unknown'
            
            success_rate = performance['success_rate']
            
            if success_rate >= 0.8:
                return 'easy'
            elif success_rate >= 0.6:
                return 'medium'
            else:
                return 'hard'
                
        except Exception as e:
            app_logger.error(f"Error estimating difficulty for user {user_id}, question {question_id}: {e}")
            return 'medium'

    @staticmethod
    def _log_recommendations(user_id: int, recommendations: List[Dict]):
        """Log recommendations for tracking effectiveness"""
        try:
            for rec in recommendations:
                history = RecommendationHistory(
                    user_id=user_id,
                    question_id=rec['question_id'],
                    part_id=rec['part_id'],
                    recommendation_type=rec['type'],
                    recommendation_reason=rec['reason'],
                    priority_score=rec['priority_score']
                )
                db.session.add(history)
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            app_logger.error(f"Error logging recommendations for user {user_id}: {e}")

    @staticmethod
    def get_recommendation_categories(user_id: int) -> Dict:
        """
        Get recommendations organized by category
        
        Returns:
            Dictionary with recommendations grouped by type
        """
        try:
            all_recommendations = RecommendationEngine.generate_recommendations(user_id, limit=20)
            
            categories = {
                RecommendationEngine.CRITICAL_REVIEW: [],
                RecommendationEngine.REINFORCEMENT_PRACTICE: [],
                RecommendationEngine.MASTERY_MAINTENANCE: [],
                RecommendationEngine.NEW_CHALLENGES: []
            }
            
            for rec in all_recommendations:
                categories[rec['type']].append(rec)
            
            return categories
            
        except Exception as e:
            app_logger.error(f"Error getting recommendation categories for user {user_id}: {e}")
            return {
                RecommendationEngine.CRITICAL_REVIEW: [],
                RecommendationEngine.REINFORCEMENT_PRACTICE: [],
                RecommendationEngine.MASTERY_MAINTENANCE: [],
                RecommendationEngine.NEW_CHALLENGES: []
            }

    @staticmethod
    def update_recommendation_feedback(user_id: int, question_id: int, part_id: int, 
                                     was_helpful: bool, score_achieved: Optional[float] = None):
        """
        Update recommendation effectiveness based on user feedback
        
        Args:
            user_id: User ID
            question_id: Question ID
            part_id: Part ID
            was_helpful: Whether the recommendation was helpful
            score_achieved: Score achieved on the problem (if attempted)
        """
        try:
            # Find the most recent recommendation for this problem
            recent_recommendation = RecommendationHistory.query.filter(
                RecommendationHistory.user_id == user_id,
                RecommendationHistory.question_id == question_id,
                RecommendationHistory.part_id == part_id,
                RecommendationHistory.attempted_at.is_(None)
            ).order_by(desc(RecommendationHistory.recommended_at)).first()
            
            if recent_recommendation:
                recent_recommendation.was_helpful = was_helpful
                recent_recommendation.attempted_at = datetime.now()
                if score_achieved is not None:
                    recent_recommendation.score_achieved = score_achieved
                
                db.session.commit()
                
                app_logger.info(f"Updated recommendation feedback for user {user_id}, "
                              f"question {question_id}: helpful={was_helpful}")
            
        except Exception as e:
            db.session.rollback()
            app_logger.error(f"Error updating recommendation feedback: {e}")

    @staticmethod
    def get_recommendation_effectiveness_stats(user_id: int) -> Dict:
        """Get statistics on recommendation effectiveness for a user"""
        try:
            total_recommendations = RecommendationHistory.query.filter_by(user_id=user_id).count()
            
            attempted_recommendations = RecommendationHistory.query.filter(
                RecommendationHistory.user_id == user_id,
                RecommendationHistory.attempted_at.isnot(None)
            ).count()
            
            helpful_recommendations = RecommendationHistory.query.filter(
                RecommendationHistory.user_id == user_id,
                RecommendationHistory.was_helpful == True
            ).count()
            
            attempt_rate = (attempted_recommendations / total_recommendations * 100) if total_recommendations > 0 else 0
            helpfulness_rate = (helpful_recommendations / attempted_recommendations * 100) if attempted_recommendations > 0 else 0
            
            return {
                'total_recommendations': total_recommendations,
                'attempted_recommendations': attempted_recommendations,
                'helpful_recommendations': helpful_recommendations,
                'attempt_rate': round(attempt_rate, 1),
                'helpfulness_rate': round(helpfulness_rate, 1)
            }
            
        except Exception as e:
            app_logger.error(f"Error getting recommendation effectiveness stats for user {user_id}: {e}")
            return {
                'total_recommendations': 0,
                'attempted_recommendations': 0,
                'helpful_recommendations': 0,
                'attempt_rate': 0.0,
                'helpfulness_rate': 0.0
            }
