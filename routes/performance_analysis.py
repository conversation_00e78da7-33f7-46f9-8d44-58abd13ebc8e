"""
Performance Analysis Engine
Analyzes user performance patterns, identifies weak areas, and calculates learning metrics.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from sqlalchemy import func, and_, or_, desc
from models import (db, User, Submission, Question, Part, Topic, Subject, 
                   PerformanceAnalytics, ReviewSchedule)
from .utils import app_logger


class PerformanceAnalysisEngine:
    """Core engine for analyzing user performance and learning patterns"""
    
    # Performance thresholds
    WEAK_PERFORMANCE_THRESHOLD = 0.5  # 50% success rate
    STRONG_PERFORMANCE_THRESHOLD = 0.8  # 80% success rate
    MINIMUM_ATTEMPTS_FOR_ANALYSIS = 3  # Minimum attempts needed for reliable analysis
    
    @staticmethod
    def calculate_user_performance_by_topic(user_id: int, topic_id: int) -> Dict:
        """
        Calculate comprehensive performance metrics for a user in a specific topic
        
        Returns:
            Dictionary with performance metrics
        """
        try:
            # Get all submissions for this user and topic
            submissions = db.session.query(Submission).join(Part).join(Question).filter(
                Submission.user_id == user_id,
                Question.topic_id == topic_id
            ).all()
            
            if not submissions:
                return {
                    'total_attempts': 0,
                    'success_rate': 0.0,
                    'average_score': 0.0,
                    'learning_velocity': 0.0,
                    'recent_performance': 0.0,
                    'performance_trend': 'insufficient_data'
                }
            
            # Calculate basic metrics
            total_attempts = len(submissions)
            total_score = sum(s.score for s in submissions if s.score is not None)
            total_possible = sum(s.part.score for s in submissions)
            
            success_rate = total_score / total_possible if total_possible > 0 else 0.0
            average_score = total_score / total_attempts if total_attempts > 0 else 0.0
            
            # Calculate learning velocity (improvement over time)
            learning_velocity = PerformanceAnalysisEngine._calculate_learning_velocity(submissions)
            
            # Calculate recent performance (last 30 days)
            recent_cutoff = datetime.now() - timedelta(days=30)
            recent_submissions = [s for s in submissions if s.timestamp >= recent_cutoff]
            
            if recent_submissions:
                recent_total_score = sum(s.score for s in recent_submissions if s.score is not None)
                recent_total_possible = sum(s.part.score for s in recent_submissions)
                recent_performance = recent_total_score / recent_total_possible if recent_total_possible > 0 else 0.0
            else:
                recent_performance = success_rate
            
            # Determine performance trend
            performance_trend = PerformanceAnalysisEngine._determine_performance_trend(
                submissions, success_rate, recent_performance
            )
            
            return {
                'total_attempts': total_attempts,
                'success_rate': success_rate,
                'average_score': average_score,
                'learning_velocity': learning_velocity,
                'recent_performance': recent_performance,
                'performance_trend': performance_trend
            }
            
        except Exception as e:
            app_logger.error(f"Error calculating performance for user {user_id}, topic {topic_id}: {e}")
            return {
                'total_attempts': 0,
                'success_rate': 0.0,
                'average_score': 0.0,
                'learning_velocity': 0.0,
                'recent_performance': 0.0,
                'performance_trend': 'error'
            }

    @staticmethod
    def _calculate_learning_velocity(submissions: List[Submission]) -> float:
        """
        Calculate learning velocity based on performance improvement over time
        Returns a value between -1.0 (declining) and 1.0 (improving)
        """
        if len(submissions) < 3:
            return 0.0
        
        # Sort submissions by timestamp
        sorted_submissions = sorted(submissions, key=lambda s: s.timestamp)
        
        # Split into first half and second half
        mid_point = len(sorted_submissions) // 2
        first_half = sorted_submissions[:mid_point]
        second_half = sorted_submissions[mid_point:]
        
        # Calculate average performance for each half
        first_half_avg = PerformanceAnalysisEngine._calculate_average_performance(first_half)
        second_half_avg = PerformanceAnalysisEngine._calculate_average_performance(second_half)
        
        # Calculate velocity as the difference
        velocity = second_half_avg - first_half_avg
        
        # Normalize to [-1, 1] range
        return max(-1.0, min(1.0, velocity))

    @staticmethod
    def _calculate_average_performance(submissions: List[Submission]) -> float:
        """Calculate average performance for a list of submissions"""
        if not submissions:
            return 0.0
        
        total_score = sum(s.score for s in submissions if s.score is not None)
        total_possible = sum(s.part.score for s in submissions)
        
        return total_score / total_possible if total_possible > 0 else 0.0

    @staticmethod
    def _determine_performance_trend(submissions: List[Submission], 
                                   overall_rate: float, recent_rate: float) -> str:
        """Determine the overall performance trend"""
        if len(submissions) < PerformanceAnalysisEngine.MINIMUM_ATTEMPTS_FOR_ANALYSIS:
            return 'insufficient_data'
        
        trend_difference = recent_rate - overall_rate
        
        if trend_difference > 0.1:
            return 'improving'
        elif trend_difference < -0.1:
            return 'declining'
        else:
            return 'stable'

    @staticmethod
    def identify_weak_areas(user_id: int) -> List[Dict]:
        """
        Identify topics where the user is performing poorly
        
        Returns:
            List of dictionaries with weak area information
        """
        try:
            # Get all topics the user has attempted
            attempted_topics = db.session.query(Topic.id, Topic.name, Subject.name.label('subject_name')).join(
                Question, Topic.id == Question.topic_id
            ).join(
                Part, Question.id == Part.question_id
            ).join(
                Submission, Part.id == Submission.part_id
            ).join(
                Subject, Topic.subject_id == Subject.id
            ).filter(
                Submission.user_id == user_id
            ).distinct().all()
            
            weak_areas = []
            
            for topic_id, topic_name, subject_name in attempted_topics:
                performance = PerformanceAnalysisEngine.calculate_user_performance_by_topic(user_id, topic_id)
                
                # Consider an area weak if success rate is below threshold and has enough attempts
                if (performance['success_rate'] < PerformanceAnalysisEngine.WEAK_PERFORMANCE_THRESHOLD and 
                    performance['total_attempts'] >= PerformanceAnalysisEngine.MINIMUM_ATTEMPTS_FOR_ANALYSIS):
                    
                    weak_areas.append({
                        'topic_id': topic_id,
                        'topic_name': topic_name,
                        'subject_name': subject_name,
                        'success_rate': performance['success_rate'],
                        'total_attempts': performance['total_attempts'],
                        'performance_trend': performance['performance_trend'],
                        'priority_score': PerformanceAnalysisEngine._calculate_weakness_priority(performance)
                    })
            
            # Sort by priority score (highest first)
            weak_areas.sort(key=lambda x: x['priority_score'], reverse=True)
            
            return weak_areas
            
        except Exception as e:
            app_logger.error(f"Error identifying weak areas for user {user_id}: {e}")
            return []

    @staticmethod
    def _calculate_weakness_priority(performance: Dict) -> float:
        """Calculate priority score for addressing a weak area"""
        priority = 0.0
        
        # Factor 1: How poor the performance is (lower = higher priority)
        priority += (1.0 - performance['success_rate']) * 50
        
        # Factor 2: Number of attempts (more attempts = higher confidence in assessment)
        priority += min(performance['total_attempts'] * 2, 20)
        
        # Factor 3: Performance trend (declining = higher priority)
        if performance['performance_trend'] == 'declining':
            priority += 15
        elif performance['performance_trend'] == 'stable':
            priority += 5
        
        # Factor 4: Learning velocity (negative velocity = higher priority)
        if performance['learning_velocity'] < 0:
            priority += abs(performance['learning_velocity']) * 10
        
        return priority

    @staticmethod
    def identify_strong_areas(user_id: int) -> List[Dict]:
        """
        Identify topics where the user is performing well
        
        Returns:
            List of dictionaries with strong area information
        """
        try:
            # Get all topics the user has attempted
            attempted_topics = db.session.query(Topic.id, Topic.name, Subject.name.label('subject_name')).join(
                Question, Topic.id == Question.topic_id
            ).join(
                Part, Question.id == Part.question_id
            ).join(
                Submission, Part.id == Submission.part_id
            ).join(
                Subject, Topic.subject_id == Subject.id
            ).filter(
                Submission.user_id == user_id
            ).distinct().all()
            
            strong_areas = []
            
            for topic_id, topic_name, subject_name in attempted_topics:
                performance = PerformanceAnalysisEngine.calculate_user_performance_by_topic(user_id, topic_id)
                
                # Consider an area strong if success rate is above threshold and has enough attempts
                if (performance['success_rate'] >= PerformanceAnalysisEngine.STRONG_PERFORMANCE_THRESHOLD and 
                    performance['total_attempts'] >= PerformanceAnalysisEngine.MINIMUM_ATTEMPTS_FOR_ANALYSIS):
                    
                    strong_areas.append({
                        'topic_id': topic_id,
                        'topic_name': topic_name,
                        'subject_name': subject_name,
                        'success_rate': performance['success_rate'],
                        'total_attempts': performance['total_attempts'],
                        'performance_trend': performance['performance_trend'],
                        'mastery_score': PerformanceAnalysisEngine._calculate_mastery_score(performance)
                    })
            
            # Sort by mastery score (highest first)
            strong_areas.sort(key=lambda x: x['mastery_score'], reverse=True)
            
            return strong_areas
            
        except Exception as e:
            app_logger.error(f"Error identifying strong areas for user {user_id}: {e}")
            return []

    @staticmethod
    def _calculate_mastery_score(performance: Dict) -> float:
        """Calculate mastery score for a strong area"""
        mastery = 0.0
        
        # Factor 1: Success rate (higher = better mastery)
        mastery += performance['success_rate'] * 50
        
        # Factor 2: Consistency (more attempts with high success = better mastery)
        mastery += min(performance['total_attempts'] * 2, 30)
        
        # Factor 3: Learning velocity (positive = still improving)
        if performance['learning_velocity'] > 0:
            mastery += performance['learning_velocity'] * 10
        
        # Factor 4: Performance trend (improving = higher mastery potential)
        if performance['performance_trend'] == 'improving':
            mastery += 10
        elif performance['performance_trend'] == 'stable':
            mastery += 5
        
        return mastery

    @staticmethod
    def update_performance_analytics(user_id: int, topic_id: Optional[int] = None, 
                                   subject_id: Optional[int] = None):
        """
        Update or create performance analytics record
        
        Args:
            user_id: User ID
            topic_id: Topic ID (for topic-level analytics)
            subject_id: Subject ID (for subject-level analytics)
        """
        try:
            if topic_id:
                # Update topic-level analytics
                analytics = PerformanceAnalytics.query.filter_by(
                    user_id=user_id, topic_id=topic_id
                ).first()
                
                if not analytics:
                    analytics = PerformanceAnalytics(user_id=user_id, topic_id=topic_id)
                    db.session.add(analytics)
                
                performance = PerformanceAnalysisEngine.calculate_user_performance_by_topic(user_id, topic_id)
                
            elif subject_id:
                # Update subject-level analytics
                analytics = PerformanceAnalytics.query.filter_by(
                    user_id=user_id, subject_id=subject_id
                ).first()
                
                if not analytics:
                    analytics = PerformanceAnalytics(user_id=user_id, subject_id=subject_id)
                    db.session.add(analytics)
                
                performance = PerformanceAnalysisEngine.calculate_user_performance_by_subject(user_id, subject_id)
            
            else:
                raise ValueError("Either topic_id or subject_id must be provided")
            
            # Update analytics fields
            analytics.success_rate = performance['success_rate']
            analytics.total_attempts = performance['total_attempts']
            analytics.correct_attempts = int(performance['total_attempts'] * performance['success_rate'])
            analytics.average_score = performance['average_score']
            analytics.learning_velocity = performance['learning_velocity']
            analytics.last_calculated = datetime.now()
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            app_logger.error(f"Error updating performance analytics: {e}")

    @staticmethod
    def calculate_user_performance_by_subject(user_id: int, subject_id: int) -> Dict:
        """Calculate performance metrics for a user in a specific subject"""
        try:
            # Get all submissions for this user and subject
            submissions = db.session.query(Submission).join(Part).join(Question).join(Topic).filter(
                Submission.user_id == user_id,
                Topic.subject_id == subject_id
            ).all()
            
            if not submissions:
                return {
                    'total_attempts': 0,
                    'success_rate': 0.0,
                    'average_score': 0.0,
                    'learning_velocity': 0.0,
                    'recent_performance': 0.0,
                    'performance_trend': 'insufficient_data'
                }
            
            # Calculate metrics similar to topic-level analysis
            total_attempts = len(submissions)
            total_score = sum(s.score for s in submissions if s.score is not None)
            total_possible = sum(s.part.score for s in submissions)
            
            success_rate = total_score / total_possible if total_possible > 0 else 0.0
            average_score = total_score / total_attempts if total_attempts > 0 else 0.0
            learning_velocity = PerformanceAnalysisEngine._calculate_learning_velocity(submissions)
            
            # Calculate recent performance
            recent_cutoff = datetime.now() - timedelta(days=30)
            recent_submissions = [s for s in submissions if s.timestamp >= recent_cutoff]
            
            if recent_submissions:
                recent_total_score = sum(s.score for s in recent_submissions if s.score is not None)
                recent_total_possible = sum(s.part.score for s in recent_submissions)
                recent_performance = recent_total_score / recent_total_possible if recent_total_possible > 0 else 0.0
            else:
                recent_performance = success_rate
            
            performance_trend = PerformanceAnalysisEngine._determine_performance_trend(
                submissions, success_rate, recent_performance
            )
            
            return {
                'total_attempts': total_attempts,
                'success_rate': success_rate,
                'average_score': average_score,
                'learning_velocity': learning_velocity,
                'recent_performance': recent_performance,
                'performance_trend': performance_trend
            }
            
        except Exception as e:
            app_logger.error(f"Error calculating subject performance for user {user_id}, subject {subject_id}: {e}")
            return {
                'total_attempts': 0,
                'success_rate': 0.0,
                'average_score': 0.0,
                'learning_velocity': 0.0,
                'recent_performance': 0.0,
                'performance_trend': 'error'
            }

    @staticmethod
    def get_learning_insights(user_id: int) -> Dict:
        """
        Get comprehensive learning insights for a user
        
        Returns:
            Dictionary with learning insights and recommendations
        """
        try:
            weak_areas = PerformanceAnalysisEngine.identify_weak_areas(user_id)
            strong_areas = PerformanceAnalysisEngine.identify_strong_areas(user_id)
            
            # Calculate overall performance metrics
            total_submissions = Submission.query.filter_by(user_id=user_id).count()
            
            if total_submissions == 0:
                return {
                    'total_submissions': 0,
                    'weak_areas': [],
                    'strong_areas': [],
                    'overall_trend': 'insufficient_data',
                    'recommendations': ['Start practicing to build your learning profile!']
                }
            
            # Generate recommendations based on analysis
            recommendations = []
            
            if weak_areas:
                top_weak = weak_areas[0]
                recommendations.append(f"Focus on {top_weak['topic_name']} - your success rate is {top_weak['success_rate']:.1%}")
            
            if strong_areas:
                top_strong = strong_areas[0]
                recommendations.append(f"Great work on {top_strong['topic_name']} - maintain your {top_strong['success_rate']:.1%} success rate")
            
            if len(weak_areas) > len(strong_areas):
                recommendations.append("Consider reviewing fundamentals before tackling new topics")
            elif len(strong_areas) > len(weak_areas):
                recommendations.append("You're doing well! Try challenging yourself with harder problems")
            
            return {
                'total_submissions': total_submissions,
                'weak_areas': weak_areas[:5],  # Top 5 weak areas
                'strong_areas': strong_areas[:5],  # Top 5 strong areas
                'overall_trend': 'improving' if len(strong_areas) > len(weak_areas) else 'needs_focus',
                'recommendations': recommendations
            }
            
        except Exception as e:
            app_logger.error(f"Error getting learning insights for user {user_id}: {e}")
            return {
                'total_submissions': 0,
                'weak_areas': [],
                'strong_areas': [],
                'overall_trend': 'error',
                'recommendations': ['Unable to analyze performance at this time']
            }
