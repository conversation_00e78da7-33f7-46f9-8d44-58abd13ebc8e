from flask import request, jsonify, session, flash, redirect, url_for, render_template
from datetime import datetime
import json
from models import db, User
from .utils import login_required, error_logger, app_logger

def register_onboarding_routes(app, db, session):

    @app.route("/onboarding")
    @login_required
    def onboarding():
        """Display the onboarding form"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))

        # Check if this is a restart request
        restart = request.args.get('restart', 'false').lower() == 'true'

        # If user has already completed onboarding and not restarting, redirect to vault
        if user.onboarding_completed is True and not restart:
            return redirect(url_for('vault'))

        # If restarting, show a message
        if restart and user.onboarding_completed is True:
            flash('You can now update your subject and level preferences.', 'info')

        return render_template('onboarding.html', is_restart=restart)

    @app.route("/complete_onboarding", methods=['POST'])
    @login_required
    def complete_onboarding():
        """Complete the onboarding process"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))

        # If user has already completed onboarding, redirect to vault
        if user.onboarding_completed is True:
            return redirect(url_for('vault'))

        try:
            # Get form data
            grade_level = request.form.get('grade_level')
            chemistry_topics_struggle_json = request.form.get('chemistry_topics_struggle')

            # Validate required fields
            if not grade_level:
                flash('Please select your grade level.', 'error')
                return redirect(url_for('onboarding'))

            # Validate grade level (only J1 and J2 for chemistry pilot)
            if grade_level not in ['J1', 'J2']:
                flash('Invalid grade level selected. Currently only J1 and J2 are supported.', 'error')
                return redirect(url_for('onboarding'))

            # Parse chemistry topics data
            chemistry_topics_struggle = []
            if chemistry_topics_struggle_json:
                try:
                    chemistry_topics_struggle = json.loads(chemistry_topics_struggle_json)
                except json.JSONDecodeError:
                    flash('Invalid data format. Please try again.', 'error')
                    return redirect(url_for('onboarding'))

            # Validate chemistry topics based on grade level
            valid_topics = get_valid_chemistry_topics_for_grade(grade_level)
            invalid_topics = [t for t in chemistry_topics_struggle if t not in valid_topics]

            if invalid_topics:
                flash(f'Invalid topics selected: {", ".join(invalid_topics)}', 'error')
                return redirect(url_for('onboarding'))

            # Complete onboarding
            user.grade_level = grade_level
            user.subjects_taken = json.dumps(['h2-chemistry'])  # Default to H2 Chemistry for pilot
            user.chemistry_topics_struggle = json.dumps(chemistry_topics_struggle)
            user.onboarding_completed = True
            user.onboarding_completed_at = datetime.utcnow()

            db.session.commit()

            app_logger.info(f"User {user.username} completed onboarding - Grade: {grade_level}, Chemistry Topics: {chemistry_topics_struggle}")

            flash('Welcome to Vast! Your profile has been set up successfully.', 'success')

            # Redirect to vault with a flag to start the tour
            return redirect(url_for('vault', start_tour='true'))

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error completing onboarding for user {user_id}: {str(e)}")
            flash('An error occurred while setting up your profile. Please try again.', 'error')
            return redirect(url_for('onboarding'))

    @app.route("/api/onboarding/subjects/<grade>")
    @login_required
    def get_subjects_for_grade(grade):
        """API endpoint to get available subjects for a grade level"""
        try:
            subjects = get_valid_subjects_for_grade(grade)
            return jsonify({
                'status': 'success',
                'subjects': subjects
            })
        except Exception as e:
            error_logger.exception(f"Error getting subjects for grade {grade}: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to get subjects'
            }), 500

    @app.route("/restart_onboarding", methods=['POST'])
    @login_required
    def restart_onboarding():
        """Allow user to restart the onboarding process"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))

        try:
            # Reset onboarding status but keep the user's account
            user.onboarding_completed = False
            user.grade_level = None
            user.subjects_taken = None
            user.subject_confidence = None
            user.chemistry_topics_struggle = None
            user.onboarding_completed_at = None

            db.session.commit()

            app_logger.info(f"User {user.username} restarted onboarding process")
            flash('Your onboarding has been reset. Please complete the setup again.', 'info')

            return redirect(url_for('onboarding', restart='true'))

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error restarting onboarding for user {user_id}: {str(e)}")
            flash('An error occurred while restarting onboarding. Please try again.', 'error')
            return redirect(url_for('user_profile', username=user.username))

    @app.route("/api/reset-onboarding-for-tour", methods=['POST'])
    @login_required
    def reset_onboarding_for_tour():
        """Reset onboarding status to test the initial tour"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            return jsonify({'success': False, 'message': 'User not found'})

        try:
            # Reset onboarding status completely
            user.onboarding_completed = False
            user.grade_level = None
            user.subjects_taken = None
            user.subject_confidence = None
            user.chemistry_topics_struggle = None
            user.onboarding_completed_at = None

            db.session.commit()

            app_logger.info(f"User {user.username} reset onboarding for tour testing")

            return jsonify({'success': True, 'message': 'Onboarding reset successfully'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error resetting onboarding for tour for user {user_id}: {str(e)}")
            return jsonify({'success': False, 'message': 'An error occurred while resetting onboarding'})

    @app.route("/test-initial-tour")
    @login_required
    def test_initial_tour():
        """Quick URL to reset onboarding and start the initial tour"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))

        try:
            # Reset onboarding status completely
            user.onboarding_completed = False
            user.grade_level = None
            user.subjects_taken = None
            user.subject_confidence = None
            user.chemistry_topics_struggle = None
            user.onboarding_completed_at = None

            db.session.commit()

            app_logger.info(f"User {user.username} reset onboarding via test-initial-tour URL")
            flash('Onboarding reset! Starting fresh tour experience.', 'info')

            # Redirect to onboarding to start fresh
            return redirect(url_for('onboarding'))

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error resetting onboarding via test URL for user {user_id}: {str(e)}")
            flash('An error occurred while resetting onboarding.', 'error')
            return redirect(url_for('dashboard'))

def get_valid_subjects_for_grade(grade):
    """Get valid subjects for a given grade level"""
    subject_mapping = {
        'PSLE': [
            'psle-math',
            'psle-science',
            'psle-english',
            'psle-chinese'
        ],
        'J1': [
            'h2-physics',
            'h2-chemistry',
            'h2-mathematics',
            'h1-physics',
            'h1-chemistry',
            'h1-mathematics'
        ],
        'J2': [
            'h2-physics',
            'h2-chemistry',
            'h2-mathematics',
            'h1-physics',
            'h1-chemistry',
            'h1-mathematics'
        ]
    }

    return subject_mapping.get(grade, [])

def get_valid_chemistry_topics_for_grade(grade):
    """Get valid chemistry topics for a given grade level"""
    chemistry_topics_mapping = {
        'J1': [
            '1a', '1b', '2', '3', '4', '5a', '6', '7', '8', '9'
        ],
        'J2': [
            '1a', '1b', '2', '3', '4', '5a', '5b', '6', '7', '8', '9',
            '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
            '20', '21', '22a', '22b', '23'
        ]
    }

    return chemistry_topics_mapping.get(grade, [])

def check_onboarding_required(user):
    """Check if user needs to complete onboarding"""
    return not user.onboarding_completed if user else True
