from flask import render_template, request, redirect, url_for, flash, session, jsonify
from sqlalchemy import func, desc, and_
from datetime import datetime
import json

from models import db, User, Question, Part, Clarification, Submission
from .utils import login_required, admin_required, error_logger, app_logger


def register_clarification_routes(app, db, session):
    """Register clarification routes"""

    @app.route("/api/clarifications/send", methods=['POST'])
    @login_required
    def send_clarification():
        """Send a clarification request to teachers"""
        try:
            data = request.get_json()
            
            if not data:
                return jsonify({'status': 'error', 'message': 'No data provided'}), 400
            
            # Validate required fields
            required_fields = ['question_id', 'subject', 'message']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'status': 'error', 'message': f'{field} is required'}), 400
            
            question_id = data['question_id']
            part_id = data.get('part_id')  # Optional
            subject = data['subject'].strip()
            message = data['message'].strip()
            
            # Validate question exists
            question = Question.query.get(question_id)
            if not question:
                return jsonify({'status': 'error', 'message': 'Question not found'}), 404
            
            # Validate part if provided
            if part_id:
                part = Part.query.filter_by(id=part_id, question_id=question_id).first()
                if not part:
                    return jsonify({'status': 'error', 'message': 'Part not found'}), 404
            
            # Get user's latest submission for context
            context_data = {}
            if part_id:
                # Get latest submission for specific part
                latest_submission = Submission.query.filter_by(
                    user_id=session['user_id'],
                    question_id=question_id,
                    part_id=part_id
                ).order_by(desc(Submission.timestamp)).first()

                if latest_submission:
                    context_data = {
                        'user_answer': latest_submission.answer,
                        'score': latest_submission.score,
                        'submission_time': latest_submission.timestamp.isoformat(),
                        'part_specific': True
                    }
            else:
                # Get all submissions for this question if no specific part
                all_submissions = Submission.query.filter_by(
                    user_id=session['user_id'],
                    question_id=question_id
                ).order_by(desc(Submission.timestamp)).all()

                if all_submissions:
                    submissions_data = []
                    for sub in all_submissions[:5]:  # Limit to last 5 submissions
                        submissions_data.append({
                            'part_id': sub.part_id,
                            'answer': sub.answer,
                            'score': sub.score,
                            'submission_time': sub.timestamp.isoformat()
                        })

                    context_data = {
                        'all_submissions': submissions_data,
                        'part_specific': False,
                        'total_submissions': len(all_submissions)
                    }
            
            # Create clarification
            clarification = Clarification(
                student_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                subject=subject,
                message=message,
                context_data=json.dumps(context_data) if context_data else None,
                status='pending'
            )
            
            db.session.add(clarification)
            db.session.commit()
            
            app_logger.info(f"Clarification {clarification.id} sent by user {session['user_id']} for question {question_id}")
            
            return jsonify({
                'status': 'success',
                'message': 'Clarification sent successfully! Teachers will be notified.',
                'clarification_id': clarification.id
            })
            
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error sending clarification: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to send clarification'}), 500

    @app.route("/clarifications")
    @login_required
    def view_clarifications():
        """View clarifications - students see their own, teachers see all"""
        user = User.query.get(session['user_id'])

        if user.role == 'admin':
            # Teachers see all clarifications
            clarifications = Clarification.query.options(
                db.joinedload(Clarification.student),
                db.joinedload(Clarification.question),
                db.joinedload(Clarification.part),
                db.joinedload(Clarification.teacher)
            ).order_by(desc(Clarification.created_at)).all()

            return render_template("teacher_clarifications.html", clarifications=clarifications)
        else:
            # Students see only their own clarifications
            clarifications = Clarification.query.filter_by(
                student_id=session['user_id']
            ).options(
                db.joinedload(Clarification.question),
                db.joinedload(Clarification.part),
                db.joinedload(Clarification.teacher)
            ).order_by(desc(Clarification.created_at)).all()

            return render_template("student_clarifications.html", clarifications=clarifications)

    @app.route("/api/clarifications/<int:clarification_id>/respond", methods=['POST'])
    @admin_required
    def respond_to_clarification(clarification_id):
        """Teacher responds to a clarification"""
        try:
            data = request.get_json()
            
            if not data or not data.get('response'):
                return jsonify({'status': 'error', 'message': 'Response is required'}), 400
            
            clarification = Clarification.query.get(clarification_id)
            if not clarification:
                return jsonify({'status': 'error', 'message': 'Clarification not found'}), 404
            
            # Update clarification with teacher response
            clarification.teacher_response = data['response'].strip()
            clarification.teacher_id = session['user_id']
            clarification.status = 'answered'
            clarification.updated_at = datetime.now()
            
            db.session.commit()
            
            app_logger.info(f"Clarification {clarification_id} answered by teacher {session['user_id']}")
            
            return jsonify({
                'status': 'success',
                'message': 'Response sent successfully!'
            })
            
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error responding to clarification {clarification_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to send response'}), 500

    @app.route("/api/clarifications/<int:clarification_id>/close", methods=['POST'])
    @login_required
    def close_clarification(clarification_id):
        """Close a clarification (students can close their own, teachers can close any)"""
        try:
            clarification = Clarification.query.get(clarification_id)
            if not clarification:
                return jsonify({'status': 'error', 'message': 'Clarification not found'}), 404
            
            user = User.query.get(session['user_id'])
            
            # Check permissions
            if user.role != 'admin' and clarification.student_id != session['user_id']:
                return jsonify({'status': 'error', 'message': 'Permission denied'}), 403
            
            clarification.status = 'closed'
            clarification.updated_at = datetime.now()
            
            db.session.commit()
            
            app_logger.info(f"Clarification {clarification_id} closed by user {session['user_id']}")
            
            return jsonify({
                'status': 'success',
                'message': 'Clarification closed successfully!'
            })
            
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error closing clarification {clarification_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to close clarification'}), 500
