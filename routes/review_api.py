"""
API endpoints for the intelligent review system
"""

from flask import request, jsonify, session
from functools import wraps
from datetime import datetime, date
from models import db, User, Question, Part, Topic, Subject
from .recommendation_engine import RecommendationEngine
from .spaced_repetition import SpacedRepetitionEngine, get_todays_reviews
from .performance_analysis import PerformanceAnalysisEngine
from .utils import app_logger


def api_login_required(f):
    """Decorator for API endpoints that require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function


def register_review_api_routes(app):
    """Register all review API routes"""
    
    @app.route("/api/recommendations", methods=["GET"])
    @api_login_required
    def get_recommendations():
        """Get personalized problem recommendations for the current user"""
        try:
            user_id = session['user_id']
            limit = request.args.get('limit', 10, type=int)
            category = request.args.get('category', None)  # Optional: filter by recommendation type
            
            if category:
                # Get recommendations for specific category
                categories = RecommendationEngine.get_recommendation_categories(user_id)
                recommendations = categories.get(category, [])[:limit]
            else:
                # Get all recommendations
                recommendations = RecommendationEngine.generate_recommendations(user_id, limit)
            
            # Format recommendations for API response
            formatted_recommendations = []
            for rec in recommendations:
                formatted_rec = {
                    'id': f"{rec['question_id']}_{rec['part_id']}",
                    'type': rec['type'],
                    'question_id': rec['question_id'],
                    'part_id': rec['part_id'],
                    'priority_score': rec['priority_score'],
                    'reason': rec['reason'],
                    'estimated_difficulty': rec['estimated_difficulty'],
                    'question': {
                        'id': rec['question'].id,
                        'title': rec['question'].title,
                        'description': rec['question'].description,
                        'source': rec['question'].source
                    },
                    'part': {
                        'id': rec['part'].id,
                        'description': rec['part'].description,
                        'score': rec['part'].score,
                        'input_type': rec['part'].input_type
                    },
                    'topic': {
                        'id': rec['topic'].id,
                        'name': rec['topic'].name
                    } if rec['topic'] else None,
                    'subject': {
                        'id': rec['subject'].id,
                        'name': rec['subject'].name
                    } if rec['subject'] else None
                }
                formatted_recommendations.append(formatted_rec)
            
            return jsonify({
                'recommendations': formatted_recommendations,
                'total_count': len(formatted_recommendations),
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error getting recommendations for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to get recommendations'}), 500

    @app.route("/api/recommendations/categories", methods=["GET"])
    @api_login_required
    def get_recommendation_categories():
        """Get recommendations organized by category"""
        try:
            user_id = session['user_id']
            categories = RecommendationEngine.get_recommendation_categories(user_id)
            
            # Format categories for API response
            formatted_categories = {}
            for category_type, recommendations in categories.items():
                formatted_categories[category_type] = {
                    'count': len(recommendations),
                    'recommendations': [
                        {
                            'id': f"{rec['question_id']}_{rec['part_id']}",
                            'question_id': rec['question_id'],
                            'part_id': rec['part_id'],
                            'priority_score': rec['priority_score'],
                            'reason': rec['reason'],
                            'estimated_difficulty': rec['estimated_difficulty'],
                            'question_title': rec['question'].title,
                            'part_description': rec['part'].description[:100] + '...' if len(rec['part'].description) > 100 else rec['part'].description,
                            'topic_name': rec['topic'].name if rec['topic'] else None,
                            'subject_name': rec['subject'].name if rec['subject'] else None
                        }
                        for rec in recommendations[:5]  # Limit to top 5 per category
                    ]
                }
            
            return jsonify({
                'categories': formatted_categories,
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error getting recommendation categories for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to get recommendation categories'}), 500

    @app.route("/api/review-schedule", methods=["GET"])
    @api_login_required
    def get_review_schedule():
        """Get spaced repetition review schedule for the current user"""
        try:
            user_id = session['user_id']
            days_ahead = request.args.get('days_ahead', 7, type=int)
            
            # Get today's reviews
            todays_reviews = get_todays_reviews(user_id)
            
            # Get upcoming reviews
            upcoming_reviews = SpacedRepetitionEngine.get_upcoming_reviews(user_id, days_ahead)
            
            # Get review statistics
            stats = SpacedRepetitionEngine.get_review_statistics(user_id)
            
            # Format today's reviews
            formatted_todays_reviews = []
            for review_info in todays_reviews:
                formatted_review = {
                    'question_id': review_info['question'].id,
                    'part_id': review_info['part'].id,
                    'question_title': review_info['question'].title,
                    'part_description': review_info['part'].description,
                    'topic_name': review_info['topic'].name if review_info['topic'] else None,
                    'subject_name': review_info['subject'].name if review_info['subject'] else None,
                    'days_overdue': review_info['days_overdue'],
                    'priority_score': review_info['priority_score'],
                    'last_quality_score': review_info['review_schedule'].quality_score,
                    'ease_factor': review_info['review_schedule'].ease_factor
                }
                formatted_todays_reviews.append(formatted_review)
            
            # Format upcoming reviews
            formatted_upcoming_reviews = []
            for review in upcoming_reviews:
                formatted_review = {
                    'question_id': review.question_id,
                    'part_id': review.part_id,
                    'question_title': review.question.title,
                    'part_description': review.part.description,
                    'topic_name': review.question.topic.name if review.question.topic else None,
                    'subject_name': review.question.topic.subject.name if review.question.topic and review.question.topic.subject else None,
                    'next_review_date': review.next_review_date.isoformat(),
                    'days_until_review': (review.next_review_date - date.today()).days,
                    'ease_factor': review.ease_factor,
                    'repetition_number': review.repetition_number
                }
                formatted_upcoming_reviews.append(formatted_review)
            
            return jsonify({
                'todays_reviews': formatted_todays_reviews,
                'upcoming_reviews': formatted_upcoming_reviews,
                'statistics': stats,
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error getting review schedule for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to get review schedule'}), 500

    @app.route("/api/performance-analytics", methods=["GET"])
    @api_login_required
    def get_performance_analytics():
        """Get performance analytics and learning insights for the current user"""
        try:
            user_id = session['user_id']
            
            # Get learning insights
            insights = PerformanceAnalysisEngine.get_learning_insights(user_id)
            
            # Get weak and strong areas
            weak_areas = PerformanceAnalysisEngine.identify_weak_areas(user_id)
            strong_areas = PerformanceAnalysisEngine.identify_strong_areas(user_id)
            
            # Get recommendation effectiveness stats
            recommendation_stats = RecommendationEngine.get_recommendation_effectiveness_stats(user_id)
            
            return jsonify({
                'insights': insights,
                'weak_areas': weak_areas,
                'strong_areas': strong_areas,
                'recommendation_effectiveness': recommendation_stats,
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error getting performance analytics for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to get performance analytics'}), 500

    @app.route("/api/review-feedback", methods=["POST"])
    @api_login_required
    def submit_review_feedback():
        """Submit feedback on recommendation quality"""
        try:
            user_id = session['user_id']
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            question_id = data.get('question_id')
            part_id = data.get('part_id')
            was_helpful = data.get('was_helpful')
            score_achieved = data.get('score_achieved')
            
            if not all([question_id, part_id]) or was_helpful is None:
                return jsonify({'error': 'Missing required fields'}), 400
            
            # Update recommendation feedback
            RecommendationEngine.update_recommendation_feedback(
                user_id, question_id, part_id, was_helpful, score_achieved
            )
            
            return jsonify({
                'message': 'Feedback submitted successfully',
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error submitting review feedback for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to submit feedback'}), 500

    @app.route("/api/update-review-schedule", methods=["POST"])
    @api_login_required
    def update_review_schedule():
        """Update spaced repetition schedule after a submission"""
        try:
            user_id = session['user_id']
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            question_id = data.get('question_id')
            part_id = data.get('part_id')
            score = data.get('score')
            max_score = data.get('max_score')
            
            if not all([question_id, part_id]) or score is None or max_score is None:
                return jsonify({'error': 'Missing required fields'}), 400
            
            # Update review schedule
            schedule = SpacedRepetitionEngine.update_review_schedule(
                user_id, question_id, part_id, score, max_score
            )
            
            return jsonify({
                'message': 'Review schedule updated successfully',
                'next_review_date': schedule.next_review_date.isoformat(),
                'interval_days': schedule.interval_days,
                'ease_factor': schedule.ease_factor,
                'quality_score': schedule.quality_score,
                'user_id': user_id
            })
            
        except Exception as e:
            app_logger.error(f"Error updating review schedule for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to update review schedule'}), 500

    @app.route("/api/learning-insights", methods=["GET"])
    @api_login_required
    def get_learning_insights():
        """Get detailed learning pattern analysis and insights"""
        try:
            user_id = session['user_id']
            
            # Get comprehensive learning insights
            insights = PerformanceAnalysisEngine.get_learning_insights(user_id)
            
            # Get review statistics
            review_stats = SpacedRepetitionEngine.get_review_statistics(user_id)
            
            # Get recommendation effectiveness
            rec_effectiveness = RecommendationEngine.get_recommendation_effectiveness_stats(user_id)
            
            # Combine all insights
            combined_insights = {
                'learning_insights': insights,
                'review_statistics': review_stats,
                'recommendation_effectiveness': rec_effectiveness,
                'generated_at': datetime.now().isoformat(),
                'user_id': user_id
            }
            
            return jsonify(combined_insights)
            
        except Exception as e:
            app_logger.error(f"Error getting learning insights for user {session.get('user_id')}: {e}")
            return jsonify({'error': 'Failed to get learning insights'}), 500
