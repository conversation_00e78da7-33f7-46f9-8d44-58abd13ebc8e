import os
import re
import json
import markdown
from flask import render_template, request, redirect, url_for, flash, session, jsonify, send_from_directory
from sqlalchemy import func, and_
from datetime import datetime, timedelta

from models import db, Subject, Question, Topic, Part, Submission, User, DailyActivity, DailyActiveTime, Group, Post
from .utils import login_required, update_user_activity, error_logger, app_logger # Import necessary utilities/models

# Markdown extensions (consider moving initialization to app factory if shared)
from markdown.extensions.toc import TocExtension
from markdown.extensions.tables import TableExtension
from markdown_katex import KatexExtension

def load_problems(content_path, user_id):
    """Helper function to load problems for a topic and calculate status."""
    problems_path = os.path.join(content_path, "problems.json")
    if not os.path.exists(problems_path):
        return {}

    try:
        with open(problems_path, 'r') as f:
            problems_data = json.load(f)
    except (IOError, json.JSONDecodeError) as e:
        error_logger.error(f"Error reading or parsing problems file {problems_path}: {e}")
        return {}

    # Process problem statuses if user is logged in
    if user_id:
        for module_key, module_problems in problems_data.items():
            for problem in module_problems:
                # Ensure problem has an ID, try extracting from source URL if missing
                if 'id' not in problem:
                    source_url = problem.get('source', '')
                    match = re.search(r'/vault/(\d+)', source_url)
                    if match:
                        problem['id'] = int(match.group(1))
                    else:
                        problem['status'] = 0 # Cannot determine status without ID
                        continue # Skip status calculation if no ID

                question_id = problem['id']
                parts = Part.query.filter_by(question_id=question_id).all()
                if not parts:
                    problem['status'] = 0 # No parts, treat as not attempted
                    continue

                total_possible_score = sum(part.score for part in parts)
                total_score = 0
                has_submission = False

                for part in parts:
                    latest_submission = (
                        Submission.query.filter_by(user_id=user_id, part_id=part.id)
                        .order_by(Submission.timestamp.desc())
                        .first()
                    )
                    if latest_submission:
                        has_submission = True
                        if latest_submission.score is not None:
                            total_score += latest_submission.score

                # Determine status based on score and attempts
                if not has_submission:
                    problem['status'] = 0 # Not attempted
                elif total_possible_score == 0: # Handle questions with 0 possible score
                    problem['status'] = 2 if has_submission else 0 # Correct if attempted, else not attempted
                elif total_score == total_possible_score:
                    problem['status'] = 2 # Correct
                elif total_score > 0:
                    problem['status'] = 1 # Partial
                else: # total_score is 0, but has_submission is True
                    problem['status'] = 0 # Incorrect (attempted but got 0)

    else: # User not logged in
        for module_key, module_problems in problems_data.items():
            for problem in module_problems:
                problem['status'] = 0 # Default status for logged-out users

    return problems_data


def register_core_routes(app, db, session):

    # Add a template filter to format time in seconds to HH:MM:SS
    @app.template_filter('format_time')
    def format_time(seconds):
        if seconds is None:
            return "00:00:00"
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    @app.route("/")
    def index():
        # Check if legacy version is requested
        if request.args.get('legacy') == 'true':
            active_users = []
            if 'user_id' in session:
                # Update activity less frequently if needed
                # update_user_activity(session['user_id']) # Moved to before_request hook in utils

                # Fetch active users (e.g., active in the last 5 minutes)
                try:
                    five_minutes_ago = datetime.now() - timedelta(minutes=5)
                    active_users = User.query.filter(User.last_active >= five_minutes_ago).all()
                except Exception as e:
                     error_logger.error(f"Error fetching active users: {e}")
                     active_users = [] # Default to empty list on error

            # Fetch all users (consider pagination for large numbers)
            # users = User.query.all() # This might be too much data for the index page

            return render_template("index.html", active_users=active_users)

        # Default to simple version
        return render_template("index_simple.html")

    @app.route("/simple")
    def index_simple():
        """Simplified, conversion-focused landing page (same as default now)"""
        return render_template("index_simple.html")

    @app.route("/legacy")
    def index_legacy():
        """Legacy complex landing page"""
        active_users = []
        if 'user_id' in session:
            # Update activity less frequently if needed
            # update_user_activity(session['user_id']) # Moved to before_request hook in utils

            # Fetch active users (e.g., active in the last 5 minutes)
            try:
                five_minutes_ago = datetime.now() - timedelta(minutes=5)
                active_users = User.query.filter(User.last_active >= five_minutes_ago).all()
            except Exception as e:
                 error_logger.error(f"Error fetching active users: {e}")
                 active_users = [] # Default to empty list on error

        return render_template("index.html", active_users=active_users)

    @app.route("/profile/<string:username>", methods=["GET"])
    def user_profile(username):
        user = User.query.filter_by(username=username).first()
        if not user:
            flash(f"User '{username}' not found.", "danger")
            return redirect(url_for('index')) # Use correct endpoint name

        # Get today's active time
        today = datetime.now().date()
        daily_active_time_entry = DailyActiveTime.query.filter_by(user_id=user.id, date=today).first()
        active_time_today = daily_active_time_entry.active_time if daily_active_time_entry else 0

        # Get total active time across all days
        from sqlalchemy import func
        total_active_time_result = db.session.query(func.sum(DailyActiveTime.active_time)).filter(
            DailyActiveTime.user_id == user.id
        ).first()
        total_active_time = total_active_time_result[0] if total_active_time_result[0] else 0

        # Get recent submissions for the activity feed (limit results)
        recent_submissions = Submission.query.filter_by(user_id=user.id)\
                                          .order_by(Submission.timestamp.desc())\
                                          .limit(20).all() # Limit to recent activity

        # --- Calculate question status counts (Optimized) ---
        incorrect_count = 0
        partial_count = 0
        correct_count = 0
        attempted_question_ids = set()

        # Subquery to get the maximum score for each part the user submitted
        max_part_scores_sq = db.session.query(
            Submission.part_id,
            func.max(Submission.score).label('max_score')
        ).filter(
            Submission.user_id == user.id
        ).group_by(Submission.part_id).subquery()

        # Query to get the user's best total score for each question they attempted
        user_best_scores = db.session.query(
            Part.question_id,
            func.sum(max_part_scores_sq.c.max_score).label('user_total_score')
        ).join(
            max_part_scores_sq, Part.id == max_part_scores_sq.c.part_id
        ).group_by(Part.question_id).all()

        # Query to get the maximum possible score for all questions
        all_question_max_scores = db.session.query(
            Question.id,
            func.sum(Part.score).label('max_possible_score')
        ).join(Part, Question.id == Part.question_id)\
         .group_by(Question.id).all()

        # Convert results to dictionaries for easier lookup
        user_scores_dict = {qid: score for qid, score in user_best_scores}
        max_scores_dict = {qid: score for qid, score in all_question_max_scores}

        # Categorize questions based on user's best score vs max possible score
        for qid, max_possible in max_scores_dict.items():
            attempted_question_ids.add(qid) # Track all questions with scores defined
            user_score = user_scores_dict.get(qid) # Get user's best score for this question

            if user_score is None: # User hasn't attempted this question
                continue # Will be counted later as unattempted

            if max_possible is None or max_possible == 0: # Question has no score defined
                 continue # Skip questions with 0 max score from counts

            if user_score == max_possible:
                correct_count += 1
            elif user_score > 0:
                partial_count += 1
            else: # user_score is 0
                incorrect_count += 1

        # Calculate unattempted count
        total_questions_count = Question.query.count()
        # Unattempted = Total questions - questions user has scores for
        unattempted_count = total_questions_count - len(user_scores_dict)


        return render_template("profile.html",
                             user=user,
                             submissions=recent_submissions, # For activity feed
                             daily_active_time=active_time_today,
                             total_active_time=total_active_time,
                             incorrect_count=incorrect_count,
                             partial_count=partial_count,
                             correct_count=correct_count,
                             profile_owner=user, # Keep profile_owner
                             unattempted_count=unattempted_count)


    @app.route("/users", methods=['GET'])
    @login_required # Should listing all users require login? Maybe admin?
    def users():
        # Consider pagination if the user list is long
        all_users = User.query.order_by(User.username).all()
        return render_template("users.html", users=all_users)


    @app.route("/dojo")
    @login_required # Or allow public access?
    def dojo():
        subjects = Subject.query.order_by(Subject.name).all()
        return render_template("dojo.html", subjects=subjects)

    @app.route("/dojo/<topic_name>") # Use topic name for more readable URLs
    @login_required # Or allow public access?
    def load_dojo_questions(topic_name):
        # Find the topic by name (assuming names are unique, or handle ambiguity)
        topic = Topic.query.filter_by(name=topic_name).first()
        if not topic:
            flash(f"Topic '{topic_name}' not found.", "error")
            return redirect(url_for('dojo'))

        # Get all dojo questions for this topic with optimized query
        dojo_questions = Question.query.filter_by(
            topic_id=topic.id,
            is_dojo=True
        ).order_by(Question.title).all()

        if not dojo_questions:
            return render_template('dojo_questions.html',
                                   topic=topic,
                                   dojo_questions=[],
                                   user_progress={},
                                   prerequisite_data={})

        # Get user's submission data for progress tracking
        user_id = session.get('user_id')
        user_progress = {}

        if user_id:
            # Optimized query to get best scores for each question
            from sqlalchemy import func
            best_scores = db.session.query(
                Submission.question_id,
                func.max(Submission.score).label('best_score')
            ).filter(
                Submission.user_id == user_id,
                Submission.question_id.in_([q.id for q in dojo_questions])
            ).group_by(Submission.question_id).all()

            best_scores_dict = {q_id: score for q_id, score in best_scores}

            # Calculate progress for each question
            for question in dojo_questions:
                total_possible = sum(part.score for part in question.parts)
                best_score = best_scores_dict.get(question.id, 0)

                if best_score > 0:
                    progress_percentage = (best_score / total_possible * 100) if total_possible > 0 else 0
                    user_progress[question.id] = {
                        'attempted': True,
                        'best_score': best_score,
                        'total_possible': total_possible,
                        'percentage': progress_percentage,
                        'completed': progress_percentage >= 80
                    }
                else:
                    user_progress[question.id] = {
                        'attempted': False,
                        'best_score': 0,
                        'total_possible': total_possible,
                        'percentage': 0,
                        'completed': False
                    }

        # Build prerequisite data with completion status
        prerequisite_data = {}
        for question in dojo_questions:
            prerequisites = [p.prerequisite_question_id for p in question.prerequisites]

            # Check if prerequisites are met
            prerequisites_met = True
            if prerequisites:
                for prereq_id in prerequisites:
                    if not user_progress.get(prereq_id, {}).get('completed', False):
                        prerequisites_met = False
                        break

            prerequisite_data[question.id] = {
                'prerequisites': prerequisites,
                'prerequisites_met': prerequisites_met
            }

        return render_template('dojo_questions.html',
                               topic=topic,
                               dojo_questions=dojo_questions,
                               user_progress=user_progress,
                               prerequisite_data=prerequisite_data)


    @app.route("/review", methods=["GET"])
    @login_required
    def review():
        user_id = session['user_id']

        # Check if user wants the legacy view or intelligent recommendations
        view_mode = request.args.get('mode', 'intelligent')  # Default to intelligent

        if view_mode == 'legacy':
            # Keep the old filtering system for backward compatibility
            return review_legacy(user_id)

        # New intelligent review system
        try:
            from .recommendation_engine import RecommendationEngine
            from .spaced_repetition import get_todays_reviews, SpacedRepetitionEngine
            from .performance_analysis import PerformanceAnalysisEngine

            # Get personalized recommendations
            recommendations = RecommendationEngine.generate_recommendations(user_id, limit=15)

            # Get today's due reviews
            todays_reviews = get_todays_reviews(user_id)

            # Get performance insights
            learning_insights = PerformanceAnalysisEngine.get_learning_insights(user_id)

            # Get review statistics
            review_stats = SpacedRepetitionEngine.get_review_statistics(user_id)

            # Organize recommendations by category
            recommendation_categories = RecommendationEngine.get_recommendation_categories(user_id)

            # Get subjects for filtering (if user wants to filter)
            subjects = Subject.query.order_by(Subject.name).all()

            return render_template("intelligent_review.html",
                                 recommendations=recommendations,
                                 todays_reviews=todays_reviews,
                                 learning_insights=learning_insights,
                                 review_stats=review_stats,
                                 recommendation_categories=recommendation_categories,
                                 subjects=subjects,
                                 user_id=user_id)

        except Exception as e:
            # Fallback to legacy view if intelligent system fails
            flash("Intelligent review system temporarily unavailable. Showing legacy view.", "warning")
            return review_legacy(user_id)

    def review_legacy(user_id):
        """Legacy review function with filtering system"""
        # Base query for the latest submission per part for the logged-in user
        latest_submission_sq = (
            db.session.query(
                Submission.part_id,
                func.max(Submission.timestamp).label('max_timestamp')
            )
            .filter_by(user_id=user_id)
            .group_by(Submission.part_id)
            .subquery()
        )

        query = (
            db.session.query(Submission)
            .join(
                latest_submission_sq,
                and_(
                    Submission.part_id == latest_submission_sq.c.part_id,
                    Submission.timestamp == latest_submission_sq.c.max_timestamp
                )
            )
            .join(Part, Submission.part_id == Part.id)
            .join(Question, Submission.question_id == Question.id)
            .join(Topic, Question.topic_id == Topic.id)
            .join(Subject, Topic.subject_id == Subject.id)
            .filter(Submission.user_id == user_id) # Ensure we only get user's submissions
        )

        # --- Filtering ---
        subject_id = request.args.get('subject_id')
        status = request.args.get('status')

        if subject_id:
            try:
                query = query.filter(Subject.id == int(subject_id))
            except ValueError:
                flash("Invalid subject filter.", "warning")

        if status:
            if status == "correct":
                # Score must equal Part's score (handle Part.score being potentially None or 0)
                query = query.filter(Part.score > 0, Submission.score == Part.score)
            elif status == "partial":
                # Score must be greater than 0 AND less than Part's score
                query = query.filter(Submission.score > 0, Submission.score < Part.score)
            elif status == "incorrect":
                # Score must be 0 (or potentially None, depending on grading logic)
                query = query.filter(Submission.score == 0)
            elif status == "unattempted":
                 # This filter doesn't make sense here as we are querying submissions
                 # Unattempted questions wouldn't have submissions.
                 flash("Cannot filter by 'unattempted' in this view.", "info")

        # --- Fetching Data ---
        submissions = query.order_by(Submission.timestamp.desc()).all()
        subjects = Subject.query.order_by(Subject.name).all()

        return render_template("review.html",
                             submissions=submissions,
                             subjects=subjects,
                             selected_subject=subject_id,
                             selected_status=status,
                             legacy_mode=True)

    @app.route("/feed", methods=['GET'])
    @login_required
    def feed():
        """Display a notification-style feed of activity milestones for users in the same groups."""
        user_id = session['user_id']
        user = User.query.get(user_id)

        if not user:
            flash("User not found.", "error")
            return redirect(url_for('index'))

        # Get all groups the user is a member of
        user_groups = user.groups

        # If user has no groups, show empty feed
        if not user_groups:
            return render_template("feed.html",
                                  user_groups=[],
                                  activity_feed=[],
                                  selected_group=None)

        # If user has multiple groups, allow selection
        # Default to first group or use group_id from query param
        group_id = request.args.get('group_id')
        if group_id:
            selected_group = Group.query.get(group_id)
            # Verify user is in this group
            if not selected_group or selected_group not in user_groups:
                flash("Invalid group selected.", "error")
                selected_group = user_groups[0]
        else:
            selected_group = user_groups[0]

        # Get posts for the selected group
        posts = Post.query.filter_by(group_id=selected_group.id).order_by(Post.timestamp.desc()).all()

        # Create activity feed items from posts
        activity_feed = []

        for post in posts:
            # Get the user who created the post
            post_user = User.query.get(post.user_id)

            # Parse the JSON data if it exists
            post_data = {}
            if post.data:
                try:
                    post_data = json.loads(post.data)
                except:
                    post_data = {}

            # Create activity feed item
            activity_item = {
                'type': post.post_type,
                'user': post_user,
                'timestamp': post.timestamp,
                'title': post.title,
                'content': post.content,
                'data': post_data
            }

            activity_feed.append(activity_item)

        # Check if we need to generate new posts
        # This would typically be done by a background task, but for simplicity
        # we'll do it here for demonstration purposes
        if not activity_feed or request.args.get('refresh') == '1':
            # Use the utility function to generate posts for all group members
            from routes.feed_utils import generate_feed_posts_for_user

            for member in selected_group.members:
                try:
                    new_posts = generate_feed_posts_for_user(member.id, selected_group.id)
                    # Add new posts to activity feed
                    for post in new_posts:
                        post_data = {}
                        if post.data:
                            try:
                                post_data = json.loads(post.data)
                            except:
                                post_data = {}

                        activity_feed.append({
                            'type': post.post_type,
                            'user': member,
                            'timestamp': post.timestamp,
                            'title': post.title,
                            'content': post.content,
                            'data': post_data
                        })
                except Exception as e:
                    app_logger.warning(f"Failed to generate feed posts for user {member.id}: {e}")

            # Commit all new posts
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                app_logger.error(f"Failed to commit feed posts: {e}")

        # If we still don't have posts, try the old method as fallback
        if not activity_feed:
            # Get today's date and yesterday's date
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)

            # Loop through all members to generate activity items
            for member in selected_group.members:
                # Skip the current user if desired
                # if member.id == user_id:
                #     continue

                # Get daily active time for today
                today_time_entry = DailyActiveTime.query.filter_by(
                    user_id=member.id,
                    date=today
                ).first()
                today_time = today_time_entry.active_time if today_time_entry else 0

                # Get daily active time for yesterday
                yesterday_time_entry = DailyActiveTime.query.filter_by(
                    user_id=member.id,
                    date=yesterday
                ).first()
                yesterday_time = yesterday_time_entry.active_time if yesterday_time_entry else 0

                # Get member's daily time goal
                daily_goal = member.daily_time_goal or 3600  # Default to 1 hour if not set

                # Calculate streak
                streak = 0
                for i in range(100):  # Check up to 100 days back
                    check_date = today - timedelta(days=i)
                    time_entry = DailyActiveTime.query.filter_by(
                        user_id=member.id,
                        date=check_date
                    ).first()

                    if time_entry and time_entry.active_time > 0:
                        streak += 1
                    else:
                        break

                # Get topics practiced today
                today_submissions = Submission.query.filter(
                    Submission.user_id == member.id,
                    func.date(Submission.timestamp) == today
                ).all()

                # Extract unique topics from submissions
                topic_ids = set()
                for submission in today_submissions:
                    if submission.question and submission.question.topic_id:
                        topic_ids.add(submission.question.topic_id)

                # Get topic objects
                topics_practiced = Topic.query.filter(Topic.id.in_(topic_ids)).all() if topic_ids else []

                # Get total questions answered today
                questions_today = db.session.query(func.count(Submission.question_id.distinct())).filter(
                    Submission.user_id == member.id,
                    func.date(Submission.timestamp) == today
                ).scalar() or 0

                # Generate posts based on milestones

                # 1. Daily goal achievement
                if today_time >= daily_goal and daily_goal > 0:
                    # Check if this post already exists
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'goal_achieved',
                        func.date(Post.timestamp) == today
                    ).first()

                    if not existing_post:
                        # Create a new post
                        percentage = min(round((today_time / daily_goal) * 100), 100)
                        post_data = {
                            'time_spent': today_time,
                            'goal': daily_goal,
                            'percentage': percentage
                        }

                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='goal_achieved',
                            title=f"Daily Goal Achieved! 🎉",
                            content=f"Spent {today_time // 3600}h {(today_time % 3600) // 60}m studying today, reaching {percentage}% of their daily goal.",
                            data=json.dumps(post_data),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'goal_achieved',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': post_data
                        })

                # 2. Streak milestone (3, 5, 7, 10, 30, etc.)
                if streak in [3, 5, 7, 10, 14, 21, 30, 60, 90, 100]:
                    # Check if this post already exists for today
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'streak_milestone',
                        func.date(Post.timestamp) == today,
                        Post.data.like(f'%"streak": {streak}%')
                    ).first()

                    if not existing_post:
                        # Create a new post
                        post_data = {
                            'streak': streak
                        }

                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='streak_milestone',
                            title=f"{streak} Day Streak! 🔥",
                            content=f"Has been active for {streak} consecutive days. Amazing consistency!",
                            data=json.dumps(post_data),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'streak_milestone',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': post_data
                        })

                # 3. First activity of the day
                if today_time > 0 and yesterday_time == 0:
                    # Check if this post already exists
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'first_activity',
                        func.date(Post.timestamp) == today
                    ).first()

                    if not existing_post:
                        # Create a new post
                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='first_activity',
                            title="Started Studying Today",
                            content="Just began their study session for today. A great start!",
                            data=json.dumps({}),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'first_activity',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': {}
                        })

                # 4. Multiple topics practiced (3+)
                if len(topics_practiced) >= 3:
                    # Check if this post already exists
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'multiple_topics',
                        func.date(Post.timestamp) == today
                    ).first()

                    if not existing_post:
                        # Create a new post
                        topic_names = [topic.name for topic in topics_practiced]
                        post_data = {
                            'topics': topic_names,
                            'count': len(topics_practiced)
                        }

                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='multiple_topics',
                            title=f"Studied {len(topics_practiced)} Different Topics",
                            content=f"Practiced multiple subjects today. Great variety!",
                            data=json.dumps(post_data),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'multiple_topics',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': post_data
                        })

                # 5. Many questions answered (5+)
                if questions_today >= 5:
                    # Check if this post already exists
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'many_questions',
                        func.date(Post.timestamp) == today
                    ).first()

                    if not existing_post:
                        # Create a new post
                        post_data = {
                            'count': questions_today
                        }

                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='many_questions',
                            title=f"Answered {questions_today} Questions",
                            content=f"Completed {questions_today} different questions today. Great progress!",
                            data=json.dumps(post_data),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'many_questions',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': post_data
                        })

                # 6. Significant time spent (2+ hours)
                if today_time >= 7200:  # 2 hours in seconds
                    # Check if this post already exists
                    existing_post = Post.query.filter(
                        Post.user_id == member.id,
                        Post.group_id == selected_group.id,
                        Post.post_type == 'significant_time',
                        func.date(Post.timestamp) == today
                    ).first()

                    if not existing_post:
                        # Create a new post
                        hours = today_time // 3600
                        post_data = {
                            'time_spent': today_time,
                            'hours': hours
                        }

                        new_post = Post(
                            user_id=member.id,
                            group_id=selected_group.id,
                            post_type='significant_time',
                            title=f"{hours}+ Hour Study Session",
                            content=f"Spent over {hours} hours studying today. Impressive dedication!",
                            data=json.dumps(post_data),
                            timestamp=datetime.now()
                        )

                        db.session.add(new_post)

                        # Add to activity feed
                        activity_feed.append({
                            'type': 'significant_time',
                            'user': member,
                            'timestamp': new_post.timestamp,
                            'title': new_post.title,
                            'content': new_post.content,
                            'data': post_data
                        })

            # Commit all new posts to the database
            db.session.commit()

        # Sort activity feed by timestamp (newest first)
        activity_feed.sort(key=lambda x: x['timestamp'], reverse=True)

        return render_template("feed.html",
                              user_groups=user_groups,
                              activity_feed=activity_feed,
                              selected_group=selected_group)

    @app.route("/testing", methods=['GET', 'POST'])
    @login_required # Or remove login_required if it's a public test page
    def testing():
        # This seems like a development/test route. Keep it simple.
        if request.method == 'POST':
            # Log or print the JSON payload for debugging
            try:
                payload = request.get_json()
                app_logger.debug(f"Received JSON in /testing: {payload}")
                # You might return the received data or a success message
                return jsonify({"status": "received", "data": payload})
            except Exception as e:
                 app_logger.error(f"Error processing POST in /testing: {e}")
                 return jsonify({"status": "error", "message": str(e)}), 400

        # GET request renders the mathlive template
        return render_template("mathlive.html") # Ensure this template exists
