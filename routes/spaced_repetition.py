"""
Spaced Repetition Algorithm Implementation
Based on the SuperMemo SM-2 algorithm with adaptations for educational content.
"""

from datetime import datetime, date, timedelta
from typing import Optional, Tuple, List, Dict
from sqlalchemy import func, and_, or_
from models import db, ReviewSchedule, Submission, PerformanceAnalytics, Question, Part, Topic, Subject, User
from .utils import app_logger


class SpacedRepetitionEngine:
    """Core engine for spaced repetition algorithm"""
    
    # SM-2 Algorithm constants
    MIN_EASE_FACTOR = 1.3
    MAX_EASE_FACTOR = 2.5
    DEFAULT_EASE_FACTOR = 2.5
    
    # Quality score thresholds
    QUALITY_PERFECT = 5    # 100% score
    QUALITY_GOOD = 4       # 80-99% score
    QUALITY_FAIR = 3       # 60-79% score
    QUALITY_POOR = 2       # 40-59% score
    QUALITY_BAD = 1        # 20-39% score
    QUALITY_FAIL = 0       # 0-19% score

    @staticmethod
    def calculate_quality_score(score: float, max_score: float) -> int:
        """Convert submission score to quality score (0-5)"""
        if max_score == 0:
            return SpacedRepetitionEngine.QUALITY_FAIL
        
        percentage = (score / max_score) * 100
        
        if percentage >= 100:
            return SpacedRepetitionEngine.QUALITY_PERFECT
        elif percentage >= 80:
            return SpacedRepetitionEngine.QUALITY_GOOD
        elif percentage >= 60:
            return SpacedRepetitionEngine.QUALITY_FAIR
        elif percentage >= 40:
            return SpacedRepetitionEngine.QUALITY_POOR
        elif percentage >= 20:
            return SpacedRepetitionEngine.QUALITY_BAD
        else:
            return SpacedRepetitionEngine.QUALITY_FAIL

    @staticmethod
    def calculate_next_interval(repetition_number: int, ease_factor: float, quality: int) -> Tuple[int, float]:
        """
        Calculate next review interval and updated ease factor using SM-2 algorithm
        
        Returns:
            Tuple of (interval_days, new_ease_factor)
        """
        # Update ease factor based on quality
        new_ease_factor = ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
        new_ease_factor = max(SpacedRepetitionEngine.MIN_EASE_FACTOR, 
                             min(SpacedRepetitionEngine.MAX_EASE_FACTOR, new_ease_factor))
        
        # Calculate interval based on repetition number
        if quality < 3:  # Poor performance - reset to beginning
            interval = 1
            repetition_number = 0
        else:
            repetition_number += 1
            if repetition_number == 1:
                interval = 1
            elif repetition_number == 2:
                interval = 6
            else:
                # For subsequent repetitions, use ease factor
                previous_interval = SpacedRepetitionEngine._get_previous_interval(repetition_number - 1, ease_factor)
                interval = round(previous_interval * new_ease_factor)
        
        # Ensure minimum interval of 1 day
        interval = max(1, interval)
        
        return interval, new_ease_factor

    @staticmethod
    def _get_previous_interval(repetition_number: int, ease_factor: float) -> int:
        """Get the interval for a given repetition number"""
        if repetition_number == 1:
            return 1
        elif repetition_number == 2:
            return 6
        else:
            # Recursively calculate previous intervals
            prev_interval = SpacedRepetitionEngine._get_previous_interval(repetition_number - 1, ease_factor)
            return round(prev_interval * ease_factor)

    @staticmethod
    def update_review_schedule(user_id: int, question_id: int, part_id: int, 
                             score: float, max_score: float) -> ReviewSchedule:
        """
        Update or create review schedule based on submission performance
        
        Args:
            user_id: User ID
            question_id: Question ID
            part_id: Part ID
            score: Score achieved
            max_score: Maximum possible score
            
        Returns:
            Updated ReviewSchedule object
        """
        try:
            # Calculate quality score
            quality = SpacedRepetitionEngine.calculate_quality_score(score, max_score)
            
            # Get or create review schedule
            schedule = ReviewSchedule.query.filter_by(
                user_id=user_id,
                question_id=question_id,
                part_id=part_id
            ).first()
            
            if not schedule:
                # Create new schedule
                schedule = ReviewSchedule(
                    user_id=user_id,
                    question_id=question_id,
                    part_id=part_id,
                    ease_factor=SpacedRepetitionEngine.DEFAULT_EASE_FACTOR,
                    repetition_number=0,
                    interval_days=1,
                    next_review_date=date.today() + timedelta(days=1),
                    last_reviewed=date.today(),
                    quality_score=quality
                )
                db.session.add(schedule)
            
            # Calculate next interval and ease factor
            interval, new_ease_factor = SpacedRepetitionEngine.calculate_next_interval(
                schedule.repetition_number, schedule.ease_factor, quality
            )
            
            # Update schedule
            schedule.ease_factor = new_ease_factor
            schedule.repetition_number = schedule.repetition_number + 1 if quality >= 3 else 0
            schedule.interval_days = interval
            schedule.next_review_date = date.today() + timedelta(days=interval)
            schedule.last_reviewed = date.today()
            schedule.quality_score = quality
            schedule.updated_at = datetime.now()
            
            db.session.commit()
            
            app_logger.info(f"Updated review schedule for user {user_id}, question {question_id}, part {part_id}: "
                          f"quality={quality}, interval={interval}, ease_factor={new_ease_factor:.2f}")
            
            return schedule
            
        except Exception as e:
            db.session.rollback()
            app_logger.error(f"Error updating review schedule: {e}")
            raise

    @staticmethod
    def get_due_reviews(user_id: int, target_date: Optional[date] = None) -> List[ReviewSchedule]:
        """
        Get all reviews due for a user on or before the target date
        
        Args:
            user_id: User ID
            target_date: Date to check for due reviews (defaults to today)
            
        Returns:
            List of ReviewSchedule objects due for review
        """
        if target_date is None:
            target_date = date.today()
        
        try:
            due_reviews = ReviewSchedule.query.filter(
                ReviewSchedule.user_id == user_id,
                ReviewSchedule.next_review_date <= target_date
            ).order_by(ReviewSchedule.next_review_date.asc()).all()
            
            return due_reviews
            
        except Exception as e:
            app_logger.error(f"Error getting due reviews for user {user_id}: {e}")
            return []

    @staticmethod
    def get_overdue_reviews(user_id: int) -> List[ReviewSchedule]:
        """Get all overdue reviews for a user"""
        return SpacedRepetitionEngine.get_due_reviews(user_id, date.today() - timedelta(days=1))

    @staticmethod
    def get_upcoming_reviews(user_id: int, days_ahead: int = 7) -> List[ReviewSchedule]:
        """
        Get reviews coming up in the next N days
        
        Args:
            user_id: User ID
            days_ahead: Number of days to look ahead
            
        Returns:
            List of ReviewSchedule objects due in the next N days
        """
        try:
            end_date = date.today() + timedelta(days=days_ahead)
            
            upcoming_reviews = ReviewSchedule.query.filter(
                ReviewSchedule.user_id == user_id,
                ReviewSchedule.next_review_date > date.today(),
                ReviewSchedule.next_review_date <= end_date
            ).order_by(ReviewSchedule.next_review_date.asc()).all()
            
            return upcoming_reviews
            
        except Exception as e:
            app_logger.error(f"Error getting upcoming reviews for user {user_id}: {e}")
            return []

    @staticmethod
    def get_review_statistics(user_id: int) -> Dict:
        """
        Get review statistics for a user
        
        Returns:
            Dictionary with review statistics
        """
        try:
            today = date.today()
            
            # Count reviews by status
            total_reviews = ReviewSchedule.query.filter_by(user_id=user_id).count()
            due_today = ReviewSchedule.query.filter(
                ReviewSchedule.user_id == user_id,
                ReviewSchedule.next_review_date <= today
            ).count()
            overdue = ReviewSchedule.query.filter(
                ReviewSchedule.user_id == user_id,
                ReviewSchedule.next_review_date < today
            ).count()
            
            # Calculate average ease factor
            avg_ease_factor = db.session.query(func.avg(ReviewSchedule.ease_factor)).filter_by(
                user_id=user_id
            ).scalar() or SpacedRepetitionEngine.DEFAULT_EASE_FACTOR
            
            # Get retention rate (problems with quality >= 3 in last review)
            good_reviews = ReviewSchedule.query.filter(
                ReviewSchedule.user_id == user_id,
                ReviewSchedule.quality_score >= 3
            ).count()
            retention_rate = (good_reviews / total_reviews * 100) if total_reviews > 0 else 0
            
            return {
                'total_reviews': total_reviews,
                'due_today': due_today,
                'overdue': overdue,
                'upcoming_week': len(SpacedRepetitionEngine.get_upcoming_reviews(user_id, 7)),
                'average_ease_factor': round(avg_ease_factor, 2),
                'retention_rate': round(retention_rate, 1)
            }
            
        except Exception as e:
            app_logger.error(f"Error getting review statistics for user {user_id}: {e}")
            return {
                'total_reviews': 0,
                'due_today': 0,
                'overdue': 0,
                'upcoming_week': 0,
                'average_ease_factor': SpacedRepetitionEngine.DEFAULT_EASE_FACTOR,
                'retention_rate': 0.0
            }


def initialize_review_schedule_for_submission(user_id: int, question_id: int, part_id: int, 
                                            score: float, max_score: float):
    """
    Initialize or update review schedule when a submission is made
    This function should be called after every submission
    """
    return SpacedRepetitionEngine.update_review_schedule(user_id, question_id, part_id, score, max_score)


def get_todays_reviews(user_id: int) -> List[Dict]:
    """
    Get today's due reviews with question and part information
    
    Returns:
        List of dictionaries with review and question information
    """
    try:
        due_reviews = SpacedRepetitionEngine.get_due_reviews(user_id)
        
        reviews_with_info = []
        for review in due_reviews:
            review_info = {
                'review_schedule': review,
                'question': review.question,
                'part': review.part,
                'topic': review.question.topic,
                'subject': review.question.topic.subject if review.question.topic else None,
                'days_overdue': (date.today() - review.next_review_date).days,
                'priority_score': calculate_review_priority(review)
            }
            reviews_with_info.append(review_info)
        
        # Sort by priority score (highest first)
        reviews_with_info.sort(key=lambda x: x['priority_score'], reverse=True)
        
        return reviews_with_info
        
    except Exception as e:
        app_logger.error(f"Error getting today's reviews for user {user_id}: {e}")
        return []


def calculate_review_priority(review: ReviewSchedule) -> float:
    """
    Calculate priority score for a review based on multiple factors
    Higher score = higher priority
    """
    priority = 0.0
    
    # Factor 1: How overdue the review is
    days_overdue = (date.today() - review.next_review_date).days
    if days_overdue > 0:
        priority += days_overdue * 10  # 10 points per day overdue
    
    # Factor 2: Quality of last attempt (lower quality = higher priority)
    if review.quality_score is not None:
        priority += (5 - review.quality_score) * 5  # Up to 25 points for poor performance
    
    # Factor 3: Ease factor (lower ease = higher priority)
    priority += (SpacedRepetitionEngine.MAX_EASE_FACTOR - review.ease_factor) * 10
    
    # Factor 4: Repetition number (lower repetition = higher priority for new material)
    if review.repetition_number < 3:
        priority += (3 - review.repetition_number) * 3
    
    return priority
