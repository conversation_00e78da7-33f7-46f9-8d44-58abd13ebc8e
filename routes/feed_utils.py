"""
Utility functions for automatic feed generation.
"""

import json
from datetime import datetime, timedelta
from sqlalchemy import func
from models import db, Post, User, DailyActiveTime, Submission, Topic


def generate_feed_posts_for_user(user_id, group_id=None):
    """
    Generate feed posts for a specific user based on their recent activity.
    
    Args:
        user_id (int): The ID of the user to generate posts for
        group_id (int, optional): Specific group to generate posts for. If None, generates for all user's groups.
    
    Returns:
        list: List of newly created Post objects
    """
    user = User.query.get(user_id)
    if not user:
        return []
    
    new_posts = []
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    # Get user's groups
    groups_to_process = [group for group in user.groups if group_id is None or group.id == group_id]
    
    for group in groups_to_process:
        # Get daily active time for today and yesterday
        today_time_entry = DailyActiveTime.query.filter_by(user_id=user_id, date=today).first()
        today_time = today_time_entry.active_time if today_time_entry else 0
        
        yesterday_time_entry = DailyActiveTime.query.filter_by(user_id=user_id, date=yesterday).first()
        yesterday_time = yesterday_time_entry.active_time if yesterday_time_entry else 0
        
        # Get user's daily time goal
        daily_goal = user.daily_time_goal or 3600  # Default to 1 hour
        
        # Calculate streak
        streak = calculate_user_streak(user_id, today)
        
        # Get topics practiced today
        topics_practiced = get_topics_practiced_today(user_id, today)
        
        # Get total questions answered today
        questions_today = get_questions_answered_today(user_id, today)
        
        # Generate posts based on milestones
        new_posts.extend(_generate_goal_achievement_post(user, group, today_time, daily_goal, today))
        new_posts.extend(_generate_streak_milestone_post(user, group, streak, today))
        new_posts.extend(_generate_first_activity_post(user, group, today_time, yesterday_time, today))
        new_posts.extend(_generate_multiple_topics_post(user, group, topics_practiced, today))
        new_posts.extend(_generate_many_questions_post(user, group, questions_today, today))
        new_posts.extend(_generate_significant_time_post(user, group, today_time, today))
    
    return new_posts


def calculate_user_streak(user_id, today):
    """Calculate the current streak for a user."""
    streak = 0
    for i in range(100):  # Check up to 100 days back
        check_date = today - timedelta(days=i)
        time_entry = DailyActiveTime.query.filter_by(user_id=user_id, date=check_date).first()
        
        if time_entry and time_entry.active_time > 0:
            streak += 1
        else:
            break
    
    return streak


def get_topics_practiced_today(user_id, today):
    """Get list of topics practiced by user today."""
    today_submissions = Submission.query.filter(
        Submission.user_id == user_id,
        func.date(Submission.timestamp) == today
    ).all()
    
    # Extract unique topics from submissions
    topic_ids = set()
    for submission in today_submissions:
        if submission.question and submission.question.topic_id:
            topic_ids.add(submission.question.topic_id)
    
    return Topic.query.filter(Topic.id.in_(topic_ids)).all() if topic_ids else []


def get_questions_answered_today(user_id, today):
    """Get count of unique questions answered by user today."""
    return db.session.query(func.count(Submission.question_id.distinct())).filter(
        Submission.user_id == user_id,
        func.date(Submission.timestamp) == today
    ).scalar() or 0


def _generate_goal_achievement_post(user, group, today_time, daily_goal, today):
    """Generate goal achievement post if applicable."""
    if today_time >= daily_goal and daily_goal > 0:
        # Check if this post already exists
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'goal_achieved',
            func.date(Post.timestamp) == today
        ).first()
        
        if not existing_post:
            percentage = min(round((today_time / daily_goal) * 100), 100)
            post_data = {
                'time_spent': today_time,
                'goal': daily_goal,
                'percentage': percentage
            }
            
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='goal_achieved',
                title=f"Daily Goal Achieved! 🎉",
                content=f"Spent {today_time // 3600}h {(today_time % 3600) // 60}m studying today, reaching {percentage}% of their daily goal.",
                data=json.dumps(post_data),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def _generate_streak_milestone_post(user, group, streak, today):
    """Generate streak milestone post if applicable."""
    if streak in [3, 5, 7, 10, 14, 21, 30, 60, 90, 100]:
        # Check if this post already exists for today
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'streak_milestone',
            func.date(Post.timestamp) == today,
            Post.data.like(f'%"streak": {streak}%')
        ).first()
        
        if not existing_post:
            post_data = {'streak': streak}
            
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='streak_milestone',
                title=f"{streak} Day Streak! 🔥",
                content=f"Has been active for {streak} consecutive days. Amazing consistency!",
                data=json.dumps(post_data),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def _generate_first_activity_post(user, group, today_time, yesterday_time, today):
    """Generate first activity post if applicable."""
    if today_time > 0 and yesterday_time == 0:
        # Check if this post already exists
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'first_activity',
            func.date(Post.timestamp) == today
        ).first()
        
        if not existing_post:
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='first_activity',
                title="Started Studying Today",
                content="Just began their study session for today. A great start!",
                data=json.dumps({}),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def _generate_multiple_topics_post(user, group, topics_practiced, today):
    """Generate multiple topics post if applicable."""
    if len(topics_practiced) >= 3:
        # Check if this post already exists
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'multiple_topics',
            func.date(Post.timestamp) == today
        ).first()
        
        if not existing_post:
            topic_names = [topic.name for topic in topics_practiced]
            post_data = {
                'topics': topic_names,
                'count': len(topics_practiced)
            }
            
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='multiple_topics',
                title=f"Studied {len(topics_practiced)} Different Topics",
                content=f"Practiced multiple subjects today. Great variety!",
                data=json.dumps(post_data),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def _generate_many_questions_post(user, group, questions_today, today):
    """Generate many questions post if applicable."""
    if questions_today >= 5:
        # Check if this post already exists
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'many_questions',
            func.date(Post.timestamp) == today
        ).first()
        
        if not existing_post:
            post_data = {'count': questions_today}
            
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='many_questions',
                title=f"Answered {questions_today} Questions",
                content=f"Completed {questions_today} different questions today. Great progress!",
                data=json.dumps(post_data),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def _generate_significant_time_post(user, group, today_time, today):
    """Generate significant time post if applicable."""
    if today_time >= 7200:  # 2 hours in seconds
        # Check if this post already exists
        existing_post = Post.query.filter(
            Post.user_id == user.id,
            Post.group_id == group.id,
            Post.post_type == 'significant_time',
            func.date(Post.timestamp) == today
        ).first()
        
        if not existing_post:
            hours = today_time // 3600
            post_data = {
                'time_spent': today_time,
                'hours': hours
            }
            
            new_post = Post(
                user_id=user.id,
                group_id=group.id,
                post_type='significant_time',
                title=f"{hours}+ Hour Study Session",
                content=f"Spent over {hours} hours studying today. Impressive dedication!",
                data=json.dumps(post_data),
                timestamp=datetime.now()
            )
            
            db.session.add(new_post)
            return [new_post]
    
    return []


def trigger_feed_generation_for_user(user_id):
    """
    Trigger feed generation for a user and commit to database.
    This is the main function to call when a user performs an action.
    """
    try:
        new_posts = generate_feed_posts_for_user(user_id)
        if new_posts:
            db.session.commit()
            return len(new_posts)
        return 0
    except Exception as e:
        db.session.rollback()
        raise e
