#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to automatically update the question.html template with the latest
unsubmittable part IDs from the database.
"""

import os
import sys
import re
from models import db, Part

def get_unsubmittable_parts():
    """Get list of unsubmittable part IDs from database."""
    diagram_patterns = [
        r'diagram\s+required',
        r'draw\s+a?\s*diagram',
        r'sketch\s+required',
        r'drawing\s+required',
        r'figure\s+required',
        r'graph\s+required',
        r'chart\s+required',
        r'illustration\s+required',
        r'visual\s+representation',
        r'cannot\s+be\s+answered\s+in\s+text',
        r'requires?\s+visual',
        r'needs?\s+diagram',
        r'must\s+draw',
        r'show\s+graphically',
        r'plot\s+required',
        r'construct\s+diagram',
        r'draw\s+and\s+label',
        r'sketch\s+and\s+label',
    ]
    
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in diagram_patterns]
    unsubmittable_part_ids = []
    
    all_parts = Part.query.all()
    for part in all_parts:
        if part.answer:
            answer_text = part.answer.strip()
            for pattern in compiled_patterns:
                if pattern.search(answer_text):
                    unsubmittable_part_ids.append(part.id)
                    break
    
    return sorted(unsubmittable_part_ids)

def update_template(unsubmittable_parts):
    """Update the question.html template with new unsubmittable parts list."""
    template_path = 'templates/question.html'
    
    if not os.path.exists(template_path):
        print(f"Template file {template_path} not found!")
        return False
    
    # Read the template file
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to find the unsubmittableParts array
    pattern = r'const unsubmittableParts = \[[\d,\s]*\];'
    replacement = f'const unsubmittableParts = {unsubmittable_parts};'
    
    # Replace the array
    new_content = re.sub(pattern, replacement, content)
    
    if new_content == content:
        print("No changes needed - unsubmittableParts array not found or already up to date.")
        return False
    
    # Write back to file
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Updated {template_path} with {len(unsubmittable_parts)} unsubmittable parts.")
    return True

def main():
    """Main function to update template with latest unsubmittable parts."""
    try:
        from app import app
        with app.app_context():
            # Get current unsubmittable parts from database
            unsubmittable_parts = get_unsubmittable_parts()
            print(f"Found {len(unsubmittable_parts)} unsubmittable parts: {unsubmittable_parts}")
            
            # Update the template
            if update_template(unsubmittable_parts):
                print("Template updated successfully!")
            else:
                print("Template update not needed.")
                
            # Also update the standalone files
            with open('unsubmittable.txt', 'w') as f:
                for part_id in unsubmittable_parts:
                    f.write(f"{part_id}\n")
            
            with open('unsubmittable_parts.js', 'w') as f:
                f.write(f"const unsubmittableParts = {unsubmittable_parts};")
            
            print("Updated unsubmittable.txt and unsubmittable_parts.js")
            
    except ImportError:
        print("Could not import Flask app. Make sure you're running this from the correct directory.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
