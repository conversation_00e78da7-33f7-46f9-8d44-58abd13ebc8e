#!/usr/bin/env python3
"""
Chemistry Notes Chunking Script

This script processes chemistry notes markdown files and chunks them based on their sections.
Each section becomes a separate chunk with metadata for efficient RAG retrieval.
"""

import os
import sys
import re
import hashlib
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, NotesChunk, NotesEmbedding

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NotesChunker:
    def __init__(self, notes_dir: str = "chemistry_notes_markdown"):
        """
        Initialize the notes chunker.
        
        Args:
            notes_dir: Directory containing markdown notes files
        """
        self.notes_dir = notes_dir
        self.chunks = []
        
    def extract_sections_from_markdown(self, content: str, filename: str) -> List[Dict]:
        """
        Extract sections from markdown content based on headers.
        
        Args:
            content: Markdown content
            filename: Name of the source file
            
        Returns:
            List of section dictionaries with metadata
        """
        sections = []
        lines = content.split('\n')
        current_section = {
            'title': '',
            'content': '',
            'level': 0,
            'start_line': 0,
            'end_line': 0,
            'filename': filename,
            'chapter_id': filename.replace('.md', ''),
            'section_id': '',
            'parent_sections': []
        }
        
        section_stack = []  # Stack to track parent sections
        line_num = 0
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check if line is a header
            header_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
            
            if header_match:
                # Save previous section if it has content
                if current_section['content'].strip():
                    current_section['end_line'] = line_num - 1
                    current_section['content'] = current_section['content'].strip()
                    sections.append(current_section.copy())
                
                # Start new section
                level = len(header_match.group(1))
                title = header_match.group(2).strip()
                
                # Update section stack based on level
                while section_stack and section_stack[-1]['level'] >= level:
                    section_stack.pop()
                
                # Create section ID from title
                section_id = self.create_section_id(title)
                
                # Get parent sections for hierarchy
                parent_sections = [s['title'] for s in section_stack]
                
                current_section = {
                    'title': title,
                    'content': '',
                    'level': level,
                    'start_line': line_num,
                    'end_line': line_num,
                    'filename': filename,
                    'chapter_id': filename.replace('.md', ''),
                    'section_id': section_id,
                    'parent_sections': parent_sections.copy()
                }
                
                # Add to section stack
                section_stack.append({
                    'title': title,
                    'level': level,
                    'section_id': section_id
                })
                
            else:
                # Add line to current section content
                current_section['content'] += line + '\n'
        
        # Don't forget the last section
        if current_section['content'].strip():
            current_section['end_line'] = line_num
            current_section['content'] = current_section['content'].strip()
            sections.append(current_section)
        
        return sections
    
    def create_section_id(self, title: str) -> str:
        """
        Create a URL-safe section ID from title.

        Args:
            title: Section title

        Returns:
            URL-safe section ID
        """
        # Handle special mathematical symbols first
        title = title.replace('°', 'circ')
        # Remove special characters and convert to lowercase
        section_id = re.sub(r'[^\w\s-]', '', title).strip().lower()
        # Replace spaces with hyphens
        section_id = re.sub(r'[-\s]+', '-', section_id)
        return section_id
    
    def clean_content(self, content: str) -> str:
        """
        Clean markdown content for better text processing.
        
        Args:
            content: Raw markdown content
            
        Returns:
            Cleaned content
        """
        # Remove image references
        content = re.sub(r'!\[.*?\]\(.*?\)', '', content)
        
        # Remove excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # Remove markdown formatting but keep the text
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # Bold
        content = re.sub(r'\*(.*?)\*', r'\1', content)      # Italic
        content = re.sub(r'`(.*?)`', r'\1', content)        # Code
        
        return content.strip()
    
    def create_chunk_hash(self, content: str, metadata: Dict) -> str:
        """
        Create a unique hash for the chunk based on content and metadata.
        
        Args:
            content: Chunk content
            metadata: Chunk metadata
            
        Returns:
            SHA256 hash string
        """
        hash_input = f"{content}{metadata['filename']}{metadata['section_id']}{metadata['title']}"
        return hashlib.sha256(hash_input.encode()).hexdigest()
    
    def process_file(self, filepath: str) -> List[Dict]:
        """
        Process a single markdown file and extract chunks.
        
        Args:
            filepath: Path to the markdown file
            
        Returns:
            List of chunk dictionaries
        """
        filename = os.path.basename(filepath)
        logger.info(f"Processing file: {filename}")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract sections
            sections = self.extract_sections_from_markdown(content, filename)
            
            chunks = []
            for section in sections:
                # Skip very short sections (less than 50 characters)
                if len(section['content']) < 50:
                    continue
                
                # Clean content
                cleaned_content = self.clean_content(section['content'])
                
                if len(cleaned_content) < 30:  # Skip if still too short after cleaning
                    continue
                
                # Create chunk
                chunk = {
                    'content': cleaned_content,
                    'title': section['title'],
                    'filename': section['filename'],
                    'chapter_id': section['chapter_id'],
                    'section_id': section['section_id'],
                    'level': section['level'],
                    'start_line': section['start_line'],
                    'end_line': section['end_line'],
                    'parent_sections': section['parent_sections'],
                    'word_count': len(cleaned_content.split()),
                    'char_count': len(cleaned_content),
                    'created_at': datetime.utcnow(),
                    'chunk_hash': ''
                }
                
                # Generate hash
                chunk['chunk_hash'] = self.create_chunk_hash(cleaned_content, chunk)
                
                chunks.append(chunk)
            
            logger.info(f"Extracted {len(chunks)} chunks from {filename}")
            return chunks
            
        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            return []
    
    def process_all_files(self, use_parallel: bool = True, max_workers: int = None) -> List[Dict]:
        """
        Process all markdown files in the notes directory.
        
        Args:
            use_parallel: Whether to use parallel processing
            max_workers: Maximum number of worker processes
            
        Returns:
            List of all chunks from all files
        """
        if not os.path.exists(self.notes_dir):
            logger.error(f"Notes directory not found: {self.notes_dir}")
            return []
        
        # Get all markdown files
        markdown_files = []
        for filename in os.listdir(self.notes_dir):
            if filename.endswith('.md'):
                filepath = os.path.join(self.notes_dir, filename)
                markdown_files.append(filepath)
        
        logger.info(f"Found {len(markdown_files)} markdown files to process")
        
        all_chunks = []
        
        if use_parallel and len(markdown_files) > 1:
            # Use parallel processing
            if max_workers is None:
                max_workers = min(32, multiprocessing.cpu_count())
            
            logger.info(f"Using parallel processing with {max_workers} workers")
            
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {executor.submit(self.process_file, filepath): filepath 
                                for filepath in markdown_files}
                
                for future in as_completed(future_to_file):
                    filepath = future_to_file[future]
                    try:
                        chunks = future.result()
                        all_chunks.extend(chunks)
                    except Exception as e:
                        logger.error(f"Error processing {filepath}: {e}")
        else:
            # Sequential processing
            for filepath in markdown_files:
                chunks = self.process_file(filepath)
                all_chunks.extend(chunks)
        
        logger.info(f"Total chunks extracted: {len(all_chunks)}")
        return all_chunks
    
    def save_chunks_to_json(self, chunks: List[Dict], output_file: str = "chemistry_notes_chunks.json"):
        """
        Save chunks to a JSON file for inspection.
        
        Args:
            chunks: List of chunk dictionaries
            output_file: Output JSON file path
        """
        import json
        
        # Convert datetime objects to strings for JSON serialization
        serializable_chunks = []
        for chunk in chunks:
            serializable_chunk = chunk.copy()
            if 'created_at' in serializable_chunk:
                serializable_chunk['created_at'] = serializable_chunk['created_at'].isoformat()
            serializable_chunks.append(serializable_chunk)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_chunks, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Chunks saved to {output_file}")

    def save_chunks_to_database(self, chunks: List[Dict]) -> int:
        """
        Save chunks to the database.

        Args:
            chunks: List of chunk dictionaries

        Returns:
            Number of chunks saved
        """
        import json

        with app.app_context():
            saved_count = 0
            skipped_count = 0

            for chunk in chunks:
                try:
                    # Check if chunk already exists (by hash)
                    existing_chunk = NotesChunk.query.filter_by(chunk_hash=chunk['chunk_hash']).first()

                    if existing_chunk:
                        logger.debug(f"Chunk already exists: {chunk['title'][:50]}...")
                        skipped_count += 1
                        continue

                    # Create new chunk
                    notes_chunk = NotesChunk(
                        content=chunk['content'],
                        title=chunk['title'],
                        filename=chunk['filename'],
                        chapter_id=chunk['chapter_id'],
                        section_id=chunk['section_id'],
                        level=chunk['level'],
                        parent_sections=json.dumps(chunk['parent_sections']),
                        start_line=chunk['start_line'],
                        end_line=chunk['end_line'],
                        word_count=chunk['word_count'],
                        char_count=chunk['char_count'],
                        chunk_hash=chunk['chunk_hash']
                    )

                    db.session.add(notes_chunk)
                    saved_count += 1

                    if saved_count % 100 == 0:
                        logger.info(f"Saved {saved_count} chunks so far...")
                        db.session.commit()  # Commit in batches

                except Exception as e:
                    logger.error(f"Error saving chunk {chunk.get('title', 'Unknown')}: {e}")
                    db.session.rollback()
                    continue

            # Final commit
            try:
                db.session.commit()
                logger.info(f"Successfully saved {saved_count} chunks to database")
                if skipped_count > 0:
                    logger.info(f"Skipped {skipped_count} existing chunks")
            except Exception as e:
                logger.error(f"Error committing chunks to database: {e}")
                db.session.rollback()
                return 0

            return saved_count

def main():
    """Main function to run the chunking process."""
    logger.info("Starting chemistry notes chunking process...")
    
    # Initialize chunker
    chunker = NotesChunker()
    
    # Process all files
    chunks = chunker.process_all_files(use_parallel=True, max_workers=32)
    
    if not chunks:
        logger.error("No chunks were extracted!")
        return
    
    # Save to JSON for inspection
    chunker.save_chunks_to_json(chunks)

    # Save to database
    logger.info("Saving chunks to database...")
    saved_count = chunker.save_chunks_to_database(chunks)

    if saved_count == 0:
        logger.error("No chunks were saved to database!")
        return

    # Print summary statistics
    total_chunks = len(chunks)
    total_words = sum(chunk['word_count'] for chunk in chunks)
    total_chars = sum(chunk['char_count'] for chunk in chunks)
    avg_words_per_chunk = total_words / total_chunks if total_chunks > 0 else 0
    
    logger.info(f"Chunking completed successfully!")
    logger.info(f"Total chunks: {total_chunks}")
    logger.info(f"Total words: {total_words:,}")
    logger.info(f"Total characters: {total_chars:,}")
    logger.info(f"Average words per chunk: {avg_words_per_chunk:.1f}")
    
    # Show distribution by level
    level_counts = {}
    for chunk in chunks:
        level = chunk['level']
        level_counts[level] = level_counts.get(level, 0) + 1
    
    logger.info("Distribution by header level:")
    for level in sorted(level_counts.keys()):
        logger.info(f"  Level {level}: {level_counts[level]} chunks")

if __name__ == "__main__":
    main()
