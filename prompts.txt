Can you make it such that for the "Explain Answer" feature, it will be a STREAM of text, instead of waiting for the entire response to be generated before displaying it? Also, can you make use prompts to ensure that the format of the response is compatible with the frontend? Currently the llm still does things like writing things within ** which is not even changing the display in frontend
Can you fix the feed function? it is currently not even working, doesn't generate any feed. i want the code to generate feed automatically when a relevant action is taken. 
Can you refactor review so that it will be more useful? It should recommend problems for the user to review, based on their performance, not just a filtering system. also, can you make an algorithm that uses spaced repetition to recommend users to try old problems again? 
Can you make the vault interface better? Instead of using a filtering system, can you use buttons for each subject instead? Make it have less friction for the user.
Can you make it such that submissions are now public instead of private? 
For onboarding, can you make it such the there's a way for the user to restart the onboarding process to change their subject or level.  