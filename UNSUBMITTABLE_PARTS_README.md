# Unsubmittable Parts Implementation

This implementation automatically identifies and blurs out parts in the database that require diagrams or visual representations, making them unsubmittable through the online interface.

## Overview

The system works by:
1. Scanning the database for parts with answers containing phrases like "Diagram required"
2. Automatically blurring out the submission interface for these parts
3. Displaying a warning message to users
4. Preventing form submission for these parts

## Files Created/Modified

### Core Implementation Files

1. **`find_unsubmittable_parts.py`** - Initial script to scan database and identify unsubmittable parts
2. **`update_unsubmittable_template.py`** - Maintenance script to keep the template updated
3. **`templates/question.html`** - Modified to include blurring functionality
4. **`unsubmittable.txt`** - Text file containing list of unsubmittable part IDs
5. **`unsubmittable_parts.js`** - JavaScript array of unsubmittable part IDs
6. **`test_blur_demo.html`** - Demo page showing the functionality

### Current Unsubmittable Parts

The following part IDs are currently marked as unsubmittable:
```
3, 10, 12, 24, 26, 28, 30, 33, 34, 39, 44, 45
```

## How It Works

### 1. Database Scanning

The system looks for parts with answers containing these patterns (case-insensitive):
- "diagram required"
- "draw a diagram" / "draw diagram"
- "sketch required"
- "drawing required"
- "figure required"
- "graph required"
- "chart required"
- "illustration required"
- "visual representation"
- "cannot be answered in text"
- "requires visual" / "require visual"
- "needs diagram" / "need diagram"
- "must draw"
- "show graphically"
- "plot required"
- "construct diagram"
- "draw and label"
- "sketch and label"

### 2. Frontend Implementation

When a question page loads, JavaScript automatically:
- Checks if any parts on the page are in the unsubmittable list
- Adds CSS classes to blur the submission elements
- Disables form inputs and buttons
- Shows a warning message above the form

### 3. Visual Effects

Unsubmittable parts display:
- ⚠️ Warning banner: "This part requires a diagram and cannot be submitted online"
- Blurred text areas and input fields
- Disabled and blurred submit buttons with "Diagram Required" text
- Blurred MCQ options (if applicable)
- Blurred image upload sections
- Blurred calculator interfaces

## Usage

### Running the Initial Scan

```bash
python3 find_unsubmittable_parts.py
```

This will:
- Scan all parts in the database
- Create `unsubmittable.txt` with part IDs
- Create `unsubmittable_parts.js` with JavaScript array
- Display found parts and their details

### Updating the Template

```bash
python3 update_unsubmittable_template.py
```

This will:
- Re-scan the database for unsubmittable parts
- Update the JavaScript array in `templates/question.html`
- Update `unsubmittable.txt` and `unsubmittable_parts.js`

### Testing the Implementation

Open `test_blur_demo.html` in a browser to see a demonstration of how the blurring works.

## Maintenance

### Adding New Patterns

To detect new types of diagram requirements, edit the `diagram_patterns` list in either script:

```python
diagram_patterns = [
    r'diagram\s+required',
    r'your_new_pattern_here',
    # ... existing patterns
]
```

### Manual Part Management

You can manually add or remove part IDs by:
1. Editing `unsubmittable.txt`
2. Running `update_unsubmittable_template.py` to sync changes
3. Or directly editing the JavaScript array in `templates/question.html`

### Automated Updates

Consider setting up a cron job to run the update script periodically:

```bash
# Run daily at 2 AM
0 2 * * * cd /path/to/vast && python3 update_unsubmittable_template.py
```

## CSS Classes

The implementation uses these CSS classes:

- `.unsubmittable-part` - Applied to form containers, adds warning banner
- `.unsubmittable-blur` - Applied to input elements, adds blur effect
- `.unsubmittable-button` - Applied to submit buttons, changes appearance

## JavaScript Functions

- `blurUnsubmittableParts()` - Main function that applies blurring
- Called automatically on `DOMContentLoaded` event

## Security Notes

- Parts are only visually disabled on the frontend
- Backend validation should also check for unsubmittable parts
- The part IDs are publicly visible in the JavaScript

## Future Enhancements

Possible improvements:
1. Backend API endpoint to get unsubmittable parts dynamically
2. Admin interface to manage unsubmittable parts
3. More sophisticated pattern matching
4. Integration with question editing interface
5. Audit logging for unsubmittable part changes
