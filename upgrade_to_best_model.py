#!/usr/bin/env python3
"""
Upgrade script to migrate from all-MiniLM-L6-v2 to all-mpnet-base-v2 (best model).

This script will:
1. Delete old embeddings from the inferior model
2. Generate new embeddings using the state-of-the-art all-mpnet-base-v2 model
3. Build a new FAISS index
4. Test the improved system
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, NotesChunk, NotesEmbedding
from notes_rag_system import NotesRAGSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_old_embeddings():
    """Remove old embeddings from the inferior model."""
    logger.info("🧹 Cleaning up old embeddings...")
    
    with app.app_context():
        # Delete embeddings from the old model
        old_model_embeddings = NotesEmbedding.query.filter_by(model_name="all-MiniLM-L6-v2").all()
        
        if old_model_embeddings:
            logger.info(f"Found {len(old_model_embeddings)} old embeddings to remove")
            
            for embedding in old_model_embeddings:
                db.session.delete(embedding)
            
            db.session.commit()
            logger.info("✅ Old embeddings removed successfully")
        else:
            logger.info("No old embeddings found")

def generate_new_embeddings():
    """Generate new embeddings using the best model."""
    logger.info("🚀 Generating new embeddings with all-mpnet-base-v2 (best model)...")
    
    # Initialize RAG system with the best model
    rag_system = NotesRAGSystem(model_name="all-mpnet-base-v2")
    
    # Create embeddings for all chunks
    rag_system.create_embeddings_for_chunks(batch_size=16, force_recreate=True)  # Smaller batch for larger model
    
    logger.info("✅ New embeddings generated successfully")

def build_new_index():
    """Build new FAISS index with the improved embeddings."""
    logger.info("🔧 Building new FAISS index...")
    
    rag_system = NotesRAGSystem(model_name="all-mpnet-base-v2")
    
    if rag_system.build_faiss_index():
        logger.info("✅ New FAISS index built successfully")
        return rag_system
    else:
        logger.error("❌ Failed to build FAISS index")
        return None

def test_improved_system(rag_system):
    """Test the improved system with sample queries."""
    logger.info("🧪 Testing improved system...")
    
    test_queries = [
        "organic chemistry functional groups",
        "chemical bonding molecular orbital theory", 
        "acid base equilibrium pH calculations",
        "electrochemistry galvanic cells",
        "reaction kinetics rate laws"
    ]
    
    logger.info("Comparing search quality with the new model:")
    
    for query in test_queries:
        logger.info(f"\n🔍 Query: '{query}'")
        
        try:
            results = rag_system.search_similar_chunks(query, top_k=3, min_score=0.3)
            
            if results:
                logger.info(f"Found {len(results)} results:")
                for i, result in enumerate(results, 1):
                    chunk = result['chunk']
                    score = result['similarity_score']
                    relevance = result['relevance_type']
                    logger.info(f"  {i}. {chunk['title']} (score: {score:.3f}, {relevance})")
            else:
                logger.info("  No results found")
                
        except Exception as e:
            logger.error(f"  Error: {e}")

def compare_models():
    """Compare the old and new models side by side."""
    logger.info("\n📊 Model Comparison:")
    logger.info("=" * 60)
    
    models_info = {
        "all-MiniLM-L6-v2": {
            "description": "Previous model (lightweight)",
            "dimensions": 384,
            "parameters": "22M",
            "speed": "Fast",
            "accuracy": "Good"
        },
        "all-mpnet-base-v2": {
            "description": "New model (state-of-the-art)",
            "dimensions": 768,
            "parameters": "109M", 
            "speed": "Moderate",
            "accuracy": "Excellent"
        }
    }
    
    for model_name, info in models_info.items():
        logger.info(f"\n🤖 {model_name}:")
        logger.info(f"  📝 Description: {info['description']}")
        logger.info(f"  📐 Dimensions: {info['dimensions']}")
        logger.info(f"  ⚙️  Parameters: {info['parameters']}")
        logger.info(f"  ⚡ Speed: {info['speed']}")
        logger.info(f"  🎯 Accuracy: {info['accuracy']}")

def get_system_stats():
    """Get statistics about the current system."""
    with app.app_context():
        chunk_count = NotesChunk.query.count()
        embedding_count = NotesEmbedding.query.count()
        
        # Get model distribution
        model_stats = db.session.query(
            NotesEmbedding.model_name,
            db.func.count(NotesEmbedding.id)
        ).group_by(NotesEmbedding.model_name).all()
        
        logger.info(f"\n📈 System Statistics:")
        logger.info(f"  📄 Total chunks: {chunk_count:,}")
        logger.info(f"  🧠 Total embeddings: {embedding_count:,}")
        logger.info(f"  🤖 Models in use:")
        
        for model_name, count in model_stats:
            logger.info(f"    - {model_name}: {count:,} embeddings")

def main():
    """Main upgrade function."""
    logger.info("🚀 Starting upgrade to best embedding model")
    logger.info("=" * 60)
    
    start_time = datetime.now()
    
    try:
        # Show current stats
        get_system_stats()
        
        # Show model comparison
        compare_models()
        
        # Confirm upgrade
        logger.info("\n⚠️  This will replace all existing embeddings with higher-quality ones.")
        logger.info("The process may take several minutes but will significantly improve search accuracy.")
        
        # Step 1: Cleanup old embeddings
        cleanup_old_embeddings()
        
        # Step 2: Generate new embeddings
        generate_new_embeddings()
        
        # Step 3: Build new index
        rag_system = build_new_index()
        
        if rag_system:
            # Step 4: Test improved system
            test_improved_system(rag_system)
            
            # Show final stats
            logger.info("\n" + "=" * 60)
            get_system_stats()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info(f"\n🎉 Upgrade completed successfully!")
            logger.info(f"⏱️  Total time: {duration}")
            logger.info(f"🎯 Your RAG system now uses the best available model for maximum accuracy!")
            
        else:
            logger.error("❌ Upgrade failed during index building")
            
    except Exception as e:
        logger.error(f"❌ Upgrade failed: {e}")
        raise

if __name__ == "__main__":
    main()
