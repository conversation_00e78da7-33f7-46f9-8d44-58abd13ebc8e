import os
from datetime import timedelta

class Config:
    """Base configuration"""
    # Secret key handling
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        SECRET_KEY_PATH = os.path.join(os.path.abspath(os.path.dirname(__file__)), 
                                      'instance', 'secret_key.txt')
        if os.path.exists(SECRET_KEY_PATH):
            with open(SECRET_KEY_PATH, 'rb') as f:
                SECRET_KEY = f.read()
        else:
            SECRET_KEY = os.urandom(24)
            os.makedirs(os.path.dirname(SECRET_KEY_PATH), exist_ok=True)
            with open(SECRET_KEY_PATH, 'wb') as f:
                f.write(SECRET_KEY)
    
    # Database
    INSTANCE_PATH = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'instance')
    DEFAULT_DB_PATH = f'sqlite:///{os.path.join(INSTANCE_PATH, "database.db")}'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', DEFAULT_DB_PATH)
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # File uploads
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', os.path.join(os.getcwd(), 'data/uploads'))
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    
    # Session settings
    SESSION_TYPE = 'filesystem'
    SESSION_FILE_DIR = os.path.join(INSTANCE_PATH, 'flask_session')
    SESSION_USE_SIGNER = True
    SESSION_PERMANENT = True
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    SESSION_COOKIE_SAMESITE = 'Lax'


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SESSION_COOKIE_SECURE = False


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    SESSION_COOKIE_SECURE = False
    # Use in-memory database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True
    
    # Production might use different database URL
    # SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    
    # Consider using Redis for session storage in production
    # SESSION_TYPE = 'redis'
    # SESSION_REDIS = Redis.from_url(os.environ.get('REDIS_URL'))


# Default to development config
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}