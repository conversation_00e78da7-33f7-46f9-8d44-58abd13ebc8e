# Pilot Testing Analysis Summary
## July 24-25, 2025

### Overview
This analysis covers the pilot testing data from July 24-25, 2025, with comprehensive statistics and semantic analysis of student submissions.

### Data Extraction Details
- **Date Range**: July 24-25, 2025
- **Total Submissions Found**: 830 (before exclusions)
- **Excluded Users**: 24ywuyi736j, 24ykang458b
- **Final Dataset**: 807 submissions
- **Unique Students**: 45
- **Unique Parts**: 127

### Key Statistics

#### Overall Performance
- **Overall Acceptance Rate**: 38.90%
- **Average Tries to Get Correct**: 1.13
- **Semantic Clusters Identified**: 206 (across all questions)

#### Data Distribution
- **Submissions per Student**: Average of ~18 submissions per student
- **Parts Coverage**: 127 different question parts attempted
- **Success Rate**: Approximately 39% of submissions were correct

### Excel Report Contents

The generated Excel file (`pilot_testing_report_2025-07-24_to_2025-07-25.xlsx`) contains four comprehensive worksheets:

#### 1. Summary Statistics Sheet
Contains detailed statistics for each part including:
- Part ID and description
- Question and topic information
- Total submissions and unique users
- Correct submissions count
- **Acceptance rate (% correct answers/total submissions)**
- **Average number of tries to get correct**
- Average score

#### 2. Detailed Submissions Sheet
Contains all individual submissions with:
- **Part ID**
- **Student Answer** (username)
- **Student Answer Text** (actual answer content)
- **Score** and **Max Score**
- **Marking Point Evaluation Feedback** (detailed feedback from AI grading)
- Timestamp
- Part and question descriptions
- Topic and subject information
- Semantic cluster assignment

#### 3. Question Semantic Analysis Sheet
Contains question-specific clustered submissions:
- **Question ID** and **Description**
- **Topic** and **Subject** information
- **Cluster ID**: Identifier for each semantic group within the question
- **Cluster Size**: Number of submissions in each cluster
- **Representative Answer**: Most typical answer in the group
- **Part IDs**: Which parts this cluster appears in
- **Average Score**: Performance of this answer pattern
- **Acceptance Rate**: Percentage of correct answers in this cluster
- **Correct Submissions**: Count of correct answers in this cluster

#### 4. Question Summary Sheet
Contains high-level question performance:
- **Question ID** and **Description**
- **Topic** and **Subject** information
- **Total Submissions**: All submissions for this question
- **Total Correct**: Number of correct submissions
- **Overall Acceptance Rate**: Question-level success rate
- **Number of Clusters**: How many answer patterns were identified
- **Average Cluster Size**: Typical size of answer groups

### Semantic Analysis Results

The semantic analysis successfully identified **206 distinct clusters** across all questions, providing question-specific insights:

#### Question-Level Analysis Benefits:
1. **Question-Specific Patterns**: Each question's answer patterns are analyzed separately
2. **Targeted Misconception Identification**: See exactly which misconceptions occur for each question
3. **Answer Approach Diversity**: Understand the range of student approaches per question
4. **Performance by Answer Type**: See which answer patterns lead to success or failure
5. **Question Difficulty Assessment**: Questions with many clusters may indicate complexity or ambiguity

#### Key Insights:
- **Average Clusters per Question**: ~3-4 distinct answer patterns per question
- **Pattern Recognition**: Similar incorrect approaches can be identified and addressed
- **Success Patterns**: High-scoring clusters show effective answer strategies
- **Misconception Patterns**: Low-scoring clusters reveal common student errors

### Technical Implementation

The analysis used:
- **Database**: SQLite database at `instance/database.db`
- **Text Analysis**: TF-IDF vectorization with scikit-learn
- **Clustering**: K-means clustering for semantic grouping
- **Export Format**: Excel with formatted sheets and proper headers

### Files Generated

1. **`pilot_testing_report_2025-07-24_to_2025-07-25.xlsx`**: Main Excel report
2. **`pilot_data_extractor.py`**: Python script for data extraction and analysis
3. **`pilot_testing_analysis_summary.md`**: This summary document

### Usage Instructions

1. Open the Excel file to explore the data
2. Use the "Summary Statistics" sheet for part-level analysis
3. Use the "Detailed Submissions" sheet for individual submission review
4. Use the "Question Semantic Analysis" sheet to understand answer patterns for each question
5. Use the "Question Summary" sheet for high-level question performance overview
6. Filter and sort data as needed for specific insights

#### Recommended Analysis Workflow:
1. **Start with Question Summary**: Identify questions with low acceptance rates
2. **Drill into Question Semantic Analysis**: Examine answer patterns for problematic questions
3. **Review Detailed Submissions**: Look at specific examples from clusters of interest
4. **Use Summary Statistics**: Understand part-level performance within questions

### Key Insights for Review

1. **Performance Variation**: With a 38.90% acceptance rate, there's significant room for improvement
2. **Quick Learning**: Average of 1.13 tries suggests students either get it right quickly or struggle significantly
3. **Answer Diversity**: 206 semantic clusters across all questions indicate rich variety in student approaches
4. **Scale**: 807 submissions across 127 parts shows comprehensive testing coverage
5. **Question-Specific Patterns**: Each question shows unique answer patterns and misconceptions

### Next Steps

1. **Prioritize Low-Performing Questions**: Use the Question Summary sheet to identify questions with low acceptance rates
2. **Analyze Question-Specific Misconceptions**: Review clusters with low scores in the Question Semantic Analysis sheet
3. **Develop Targeted Interventions**: Create specific feedback for common incorrect answer patterns
4. **Review High-Performing Patterns**: Study successful answer clusters to understand effective approaches
5. **Adjust Question Design**: Consider revising questions with too many clusters (may indicate ambiguity)
6. **Create Rubric Improvements**: Use marking point feedback to refine grading criteria
