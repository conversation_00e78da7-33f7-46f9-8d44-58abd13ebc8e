#!/usr/bin/env python3
"""
Script to populate question-notes relevance relationships using the RAG system.
This will automatically find and create static relationships between questions and relevant notes.
"""

import os
import sys
import logging

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Question, NotesChunk, QuestionNotesRelevance
from routes.api import _get_relevant_notes_sections

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_question_text(question):
    """Extract searchable text from a question."""
    parts = []
    
    if question.title:
        parts.append(question.title)
    
    if question.description:
        parts.append(question.description)
    
    # Add part descriptions
    for part in question.parts:
        if part.description:
            parts.append(part.description)
    
    return ' '.join(parts).strip()

def populate_relevance_for_question(question, max_notes=3):
    """Populate notes relevance for a single question using RAG."""
    logger.info(f"Processing question {question.id}: {question.title}")
    
    # Get question text
    question_text = get_question_text(question)
    
    if len(question_text) < 20:  # Skip questions with minimal text
        logger.info(f"  Skipping question {question.id} - insufficient text")
        return 0
    
    logger.info(f"  Question text: {question_text[:150]}...")
    
    # Use RAG to find relevant notes
    try:
        relevant_sections = _get_relevant_notes_sections(question_text, max_sections=max_notes)
        
        if not relevant_sections:
            logger.info(f"  No relevant notes found for question {question.id}")
            return 0
        
        # Clear existing relevance relationships
        QuestionNotesRelevance.query.filter_by(question_id=question.id).delete()
        db.session.flush()  # Ensure deletions are committed before inserts

        # Create new relationships
        created_count = 0
        for section in relevant_sections:
            # Find the notes chunk by chapter_id and section_id
            chunk = NotesChunk.query.filter_by(
                chapter_id=section['chapter_id'],
                section_id=section['section_id']
            ).first()

            if chunk:
                # Check if relationship already exists (extra safety)
                existing = QuestionNotesRelevance.query.filter_by(
                    question_id=question.id,
                    notes_chunk_id=chunk.id
                ).first()

                if existing:
                    logger.info(f"    Skipping existing: {chunk.title}")
                    continue

                # Determine relevance type based on similarity score
                if section['similarity_score'] >= 0.7:
                    relevance_type = 'concept_explanation'
                    strength = 5
                elif section['similarity_score'] >= 0.6:
                    relevance_type = 'related_topic'
                    strength = 4
                elif section['similarity_score'] >= 0.5:
                    relevance_type = 'background_knowledge'
                    strength = 3
                else:
                    relevance_type = 'general'
                    strength = 2

                # Create relevance relationship
                try:
                    relevance = QuestionNotesRelevance(
                        question_id=question.id,
                        notes_chunk_id=chunk.id,
                        relevance_type=relevance_type,
                        strength=strength
                    )

                    db.session.add(relevance)
                    db.session.flush()  # Check for constraint violations immediately
                    created_count += 1

                    logger.info(f"    Added: {chunk.title} (score: {section['similarity_score']:.3f}, type: {relevance_type})")

                except Exception as e:
                    logger.warning(f"    Failed to add {chunk.title}: {e}")
                    db.session.rollback()
                    continue
            else:
                logger.warning(f"    Could not find chunk for {section['chapter_id']}#{section['section_id']}")

        db.session.commit()
        logger.info(f"  Created {created_count} relevance relationships for question {question.id}")
        return created_count
        
    except Exception as e:
        logger.error(f"  Error processing question {question.id}: {e}")
        try:
            db.session.rollback()
        except:
            pass  # Session might already be rolled back
        return 0

def populate_all_questions(limit=None, vault_only=True):
    """Populate notes relevance for all questions."""
    logger.info("🚀 Starting notes relevance population")
    
    with app.app_context():
        # Get questions to process
        query = Question.query
        
        if vault_only:
            query = query.filter_by(is_dojo=False)  # Only vault questions
            logger.info("Processing vault questions only")
        
        if limit:
            query = query.limit(limit)
            logger.info(f"Limited to {limit} questions")
        
        questions = query.all()
        logger.info(f"Found {len(questions)} questions to process")
        
        if not questions:
            logger.warning("No questions found to process")
            return
        
        # Process each question
        total_relationships = 0
        processed_count = 0
        
        for question in questions:
            try:
                count = populate_relevance_for_question(question, max_notes=3)
                total_relationships += count
                processed_count += 1
                
                if processed_count % 5 == 0:
                    logger.info(f"Progress: {processed_count}/{len(questions)} questions processed")
                    
            except Exception as e:
                logger.error(f"Failed to process question: {e}")
                try:
                    db.session.rollback()
                except:
                    pass  # Session might already be rolled back
                continue
        
        logger.info(f"✅ Completed! Processed {processed_count} questions")
        logger.info(f"📊 Created {total_relationships} total relevance relationships")
        logger.info(f"📈 Average {total_relationships/processed_count:.1f} notes per question")

def show_sample_results(limit=5):
    """Show sample results of the populated data."""
    logger.info("📋 Sample Results:")
    
    with app.app_context():
        questions_with_notes = Question.query.filter(
            Question.relevant_notes.any()
        ).limit(limit).all()
        
        for question in questions_with_notes:
            logger.info(f"\n📝 Question: {question.title}")
            logger.info(f"   ID: {question.id}")
            
            for relevance in question.relevant_notes:
                chunk = relevance.notes_chunk
                logger.info(f"   📖 {chunk.title}")
                logger.info(f"      File: {chunk.filename}")
                logger.info(f"      Type: {relevance.relevance_type}")
                logger.info(f"      Strength: {'★' * relevance.strength}{'☆' * (5-relevance.strength)}")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Populate question-notes relevance relationships')
    parser.add_argument('--limit', type=int, help='Limit number of questions to process')
    parser.add_argument('--all', action='store_true', help='Process both dojo and vault questions')
    parser.add_argument('--sample', action='store_true', help='Show sample results only')
    
    args = parser.parse_args()
    
    if args.sample:
        show_sample_results()
        return
    
    vault_only = not args.all
    populate_all_questions(limit=args.limit, vault_only=vault_only)
    
    # Show sample results
    logger.info("\n" + "="*60)
    show_sample_results()

if __name__ == "__main__":
    main()
