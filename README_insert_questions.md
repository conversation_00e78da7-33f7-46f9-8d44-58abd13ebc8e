# Question Insertion Script

This script (`insert_questions.py`) automatically inserts questions from `new_question.json` into the database.

## Features

- Inserts questions with single parts (as specified)
- Creates individual marking points for each answer item
- Assigns all questions to subject_id=3 (h2-chemistry)
- Creates new topics if they don't exist
- Provides verification functionality
- Handles errors gracefully with rollback

## Usage

### Insert Questions
```bash
python insert_questions.py
```

### Verify Existing Data Only
```bash
python insert_questions.py --verify-only
```

## JSON Format Expected

The script expects `new_question.json` with the following structure:

```json
{
  "topic-name": [
    {
      "question": "Question text here",
      "answer": [
        "First marking point",
        "Second marking point",
        "Third marking point"
      ]
    }
  ]
}
```

## Database Structure Created

For each question, the script creates:

1. **Question** record with:
   - Title: "Question from {topic_name}"
   - Description: The question text
   - Topic ID: Linked to the appropriate chemistry topic
   - Subject ID: 3 (h2-chemistry)

2. **Part** record with:
   - Description: Same as question text
   - Score: Number of marking points
   - Input type: "saq" (short answer question)

3. **MarkingPoint** records with:
   - Description: Each answer item
   - Score: 1.0 per marking point
   - Order: Sequential numbering

## Results from Last Run

✅ **Successfully inserted 132 questions with 278 total marking points**

### Breakdown by Topic:
- atomic-structure: 13 questions
- chemical-bonding-1: 17 questions  
- gaseous-state: 3 questions
- energetics: 6 questions
- kinetics: 9 questions
- equilibria: 8 questions
- chem-bonding-2: 5 questions (new topic created)
- organic-chemistry: 4 questions
- alkanes: 5 questions (skipped 1 with no answers)
- alkenes: 7 questions (skipped 1 with no answers)
- arenes: 6 questions (skipped 1 with no answers)
- periodic-table: 13 questions
- periodic-table-2: 8 questions
- alcohols: 6 questions
- carbonyl-compounds: 3 questions (skipped 1 with no answers)
- carboxylic-acids: 4 questions
- nitrogen-compounds: 5 questions
- solubility: 4 questions
- redox: 6 questions

### Notes:
- 4 questions were skipped because they had empty answer arrays
- 1 new topic "chem-bonding-2" was created
- All questions are now available in the chemistry question bank
