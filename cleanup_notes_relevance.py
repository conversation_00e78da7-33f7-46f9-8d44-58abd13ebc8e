#!/usr/bin/env python3
"""
Script to clean up any duplicate or problematic notes relevance data.
"""

import os
import sys
import logging

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, QuestionNotesRelevance

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def cleanup_duplicates():
    """Remove any duplicate notes relevance relationships."""
    logger.info("🧹 Cleaning up duplicate notes relevance relationships...")
    
    with app.app_context():
        # Get all relevance relationships
        all_relevance = QuestionNotesRelevance.query.all()
        logger.info(f"Found {len(all_relevance)} total relevance relationships")
        
        # Track seen combinations
        seen_combinations = set()
        duplicates_to_remove = []
        
        for relevance in all_relevance:
            combination = (relevance.question_id, relevance.notes_chunk_id)
            
            if combination in seen_combinations:
                duplicates_to_remove.append(relevance)
                logger.info(f"Found duplicate: Q{relevance.question_id} -> Chunk{relevance.notes_chunk_id}")
            else:
                seen_combinations.add(combination)
        
        # Remove duplicates
        if duplicates_to_remove:
            logger.info(f"Removing {len(duplicates_to_remove)} duplicate relationships...")
            
            for duplicate in duplicates_to_remove:
                db.session.delete(duplicate)
            
            db.session.commit()
            logger.info("✅ Duplicates removed successfully")
        else:
            logger.info("✅ No duplicates found")
        
        # Show final stats
        final_count = QuestionNotesRelevance.query.count()
        logger.info(f"📊 Final count: {final_count} unique relevance relationships")

def clear_all_relevance():
    """Clear all notes relevance relationships (for fresh start)."""
    logger.info("🗑️  Clearing all notes relevance relationships...")
    
    with app.app_context():
        count = QuestionNotesRelevance.query.count()
        logger.info(f"Found {count} relationships to remove")
        
        if count > 0:
            QuestionNotesRelevance.query.delete()
            db.session.commit()
            logger.info("✅ All relationships cleared")
        else:
            logger.info("✅ No relationships to clear")

def show_stats():
    """Show current statistics."""
    logger.info("📊 Current Statistics:")
    
    with app.app_context():
        total_relevance = QuestionNotesRelevance.query.count()
        
        # Count by relevance type
        type_counts = db.session.query(
            QuestionNotesRelevance.relevance_type,
            db.func.count(QuestionNotesRelevance.id)
        ).group_by(QuestionNotesRelevance.relevance_type).all()
        
        # Count by strength
        strength_counts = db.session.query(
            QuestionNotesRelevance.strength,
            db.func.count(QuestionNotesRelevance.id)
        ).group_by(QuestionNotesRelevance.strength).all()
        
        logger.info(f"  📄 Total relationships: {total_relevance}")
        logger.info(f"  📝 By type:")
        for rel_type, count in type_counts:
            logger.info(f"    - {rel_type}: {count}")
        logger.info(f"  ⭐ By strength:")
        for strength, count in strength_counts:
            logger.info(f"    - {strength} stars: {count}")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Clean up notes relevance data')
    parser.add_argument('--clear-all', action='store_true', help='Clear all relevance relationships')
    parser.add_argument('--cleanup-duplicates', action='store_true', help='Remove duplicate relationships')
    parser.add_argument('--stats', action='store_true', help='Show current statistics')
    
    args = parser.parse_args()
    
    if args.clear_all:
        clear_all_relevance()
    elif args.cleanup_duplicates:
        cleanup_duplicates()
    elif args.stats:
        show_stats()
    else:
        # Default: cleanup duplicates and show stats
        cleanup_duplicates()
        show_stats()

if __name__ == "__main__":
    main()
