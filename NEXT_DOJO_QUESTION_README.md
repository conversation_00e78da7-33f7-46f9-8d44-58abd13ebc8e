# Next DOJO Question Button Implementation

This feature adds an enticing button that allows users to navigate to the next question in the same DOJO topic, enhancing the learning flow and engagement.

## Overview

The "Next DOJO Question" button appears on question pages for DOJO questions and provides:
- Seamless navigation between questions in the same topic
- Visual progress indication (current position out of total questions)
- Enticing design with animations and hover effects
- Automatic wrapping to the first question when reaching the end

## Features

### 🎯 **Smart Navigation**
- Only appears on DOJO questions (`is_dojo=True`)
- Finds the next question in the same topic
- Orders questions alphabetically by title
- Wraps around to the first question when reaching the end

### 🎨 **Enticing Design**
- Gradient purple background with subtle pulsing animation
- Martial arts emoji (🥋) to reinforce DOJO theme
- Hover effects with scaling and shimmer animation
- Clear progress indication ("Question X of Y")
- Special indicator when wrapping back to start

### ⚡ **Smooth Experience**
- Loads next question info asynchronously
- Shows loading states during navigation
- Graceful error handling
- No page refresh needed for button updates

## Implementation Details

### Files Modified/Created

1. **`routes/api.py`** - Added API endpoint `/api/get-next-dojo-question/<question_id>`
2. **`templates/question.html`** - Added button HTML, CSS, and JavaScript
3. **`test_next_dojo_demo.html`** - Demo page showing the functionality

### API Endpoint

```
GET /api/get-next-dojo-question/<int:current_question_id>
```

**Response Format:**
```json
{
    "success": true,
    "next_question": {
        "id": 61,
        "title": "Beryllium and Magnesium First Ionization Energy Differences",
        "topic_name": "atomic-structure",
        "topic_display_name": "Atomic Structure",
        "is_wrapped": false,
        "position": 5,
        "total_questions": 14
    }
}
```

### HTML Structure

The button appears in a gradient container with:
- Header with emoji and motivational text
- Main button showing next question title
- Progress indicator below the button
- Special styling for wrapped questions

### CSS Classes

- `.next-dojo-container` - Main container with gradient and animations
- `.next-dojo-btn` - Button with hover effects and shimmer animation
- `@keyframes subtle-pulse` - Subtle pulsing animation for the container

### JavaScript Functions

- `loadNextDojoQuestionInfo()` - Fetches next question data from API
- `loadNextDojoQuestion()` - Handles navigation to next question

## Usage

### For DOJO Questions

The button automatically appears on any question page where `question.is_dojo` is `True`. It:

1. Loads on page ready via `DOMContentLoaded` event
2. Fetches next question info from the API
3. Updates button text and progress indicator
4. Handles click events to navigate to next question

### Button States

1. **Loading**: "Loading next question..." (initial state)
2. **Ready**: "Next: [Question Title]" with arrow
3. **Navigating**: "Loading..." with spinner icon
4. **Wrapped**: Shows "Back to start!" indicator

## Visual Design

### Color Scheme
- **Primary**: Purple gradient (`#7c3aed` to `#4f46e5`)
- **Button**: White background with purple text
- **Hover**: Subtle scaling and enhanced shadows

### Animations
- **Container**: Subtle pulsing shadow effect (3s cycle)
- **Button Hover**: Scale up, translate up, shimmer effect
- **Loading**: Spinning icon during navigation

### Typography
- **Title**: "Ready for the Next Challenge?" (bold, large)
- **Subtitle**: Topic name with encouraging text
- **Button**: Next question title with arrow
- **Progress**: "Question X of Y" format

## Error Handling

The implementation gracefully handles:
- Questions with no topic
- Single question topics
- API failures
- Network errors
- Invalid question IDs

When errors occur, the button simply doesn't appear, maintaining a clean user experience.

## Testing

### Demo Page
Open `test_next_dojo_demo.html` to see:
- Visual design of the button
- Hover and click animations
- API testing interface
- Example with real question data

### Manual Testing
1. Navigate to any DOJO question page
2. Complete or scroll to bottom of question
3. Observe the "Next DOJO Question" button
4. Click to navigate to next question
5. Verify progress indicator updates

### API Testing
```bash
# Test with a known DOJO question ID
curl http://localhost:5000/api/get-next-dojo-question/1
```

## Configuration

### Customizing Question Order
Questions are currently ordered by `Question.title`. To change this:

1. Modify the `order_by()` clause in the API endpoint
2. Consider adding a `display_order` field to the Question model
3. Update both the API and any admin interfaces

### Styling Customization
Key CSS variables for easy customization:
- Gradient colors in `.next-dojo-container`
- Animation duration in `@keyframes subtle-pulse`
- Hover effects in `.next-dojo-btn:hover`

## Future Enhancements

Possible improvements:
1. **Smart Ordering**: Order by difficulty or prerequisites
2. **Progress Tracking**: Show completion status for each question
3. **Keyboard Navigation**: Add keyboard shortcuts (e.g., Ctrl+→)
4. **Recommendations**: Suggest questions based on performance
5. **Bookmarking**: Allow users to bookmark favorite questions
6. **Topic Completion**: Celebrate when finishing all questions in a topic

## Performance Notes

- API calls are made asynchronously to avoid blocking page load
- Button only loads for DOJO questions to minimize unnecessary requests
- Minimal DOM manipulation for smooth performance
- CSS animations use transform properties for optimal performance
