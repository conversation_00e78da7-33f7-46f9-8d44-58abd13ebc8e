#!/usr/bin/env python3
"""
Chemistry Notes RAG (Retrieval-Augmented Generation) System

This script creates embeddings for notes chunks and provides a RAG system
for retrieving relevant note sections based on queries.
"""

import os
import sys
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime
import json

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, NotesChunk, NotesEmbedding

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from sentence_transformers import SentenceTransformer
    import faiss
    from sklearn.preprocessing import normalize
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    logger.error(f"Required dependencies not installed: {e}")
    logger.error("Please install: pip install sentence-transformers faiss-cpu scikit-learn")
    DEPENDENCIES_AVAILABLE = False


class NotesRAGSystem:
    def __init__(self, model_name: str = "all-mpnet-base-v2"):
        """
        Initialize the Notes RAG system.
        
        Args:
            model_name: Sentence transformer model to use for embeddings
        """
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError("Required dependencies not available")
            
        self.model_name = model_name
        self.model = None
        self.faiss_index = None
        self.chunk_id_to_index = {}  # Map chunk IDs to FAISS indices
        self.index_to_chunk_id = {}  # Map FAISS indices to chunk IDs
        
        logger.info(f"Initializing RAG system with model: {model_name}")
    
    def load_model(self):
        """Load the sentence transformer model."""
        logger.info(f"Loading sentence transformer model: {self.model_name}")
        self.model = SentenceTransformer(self.model_name)
        logger.info("Model loaded successfully")
    
    def create_embeddings_for_chunks(self, batch_size: int = 32, force_recreate: bool = False):
        """
        Create embeddings for all chunks that don't have them yet.
        
        Args:
            batch_size: Number of chunks to process in each batch
            force_recreate: Whether to recreate existing embeddings
        """
        if not self.model:
            self.load_model()
        
        with app.app_context():
            # Get chunks that need embeddings
            if force_recreate:
                chunks_query = NotesChunk.query.all()
                logger.info(f"Force recreating embeddings for {len(chunks_query)} chunks")
            else:
                # Get chunks without embeddings for this model
                chunks_query = db.session.query(NotesChunk).outerjoin(
                    NotesEmbedding, 
                    (NotesChunk.id == NotesEmbedding.chunk_id) & 
                    (NotesEmbedding.model_name == self.model_name)
                ).filter(NotesEmbedding.id.is_(None)).all()
                logger.info(f"Found {len(chunks_query)} chunks without embeddings")
            
            if not chunks_query:
                logger.info("No chunks need embeddings")
                return
            
            # Process chunks in batches
            total_chunks = len(chunks_query)
            processed = 0
            
            for i in range(0, total_chunks, batch_size):
                batch_chunks = chunks_query[i:i + batch_size]
                
                # Prepare texts for embedding
                texts = []
                chunk_ids = []
                
                for chunk in batch_chunks:
                    # Combine title and content for better embeddings
                    text = f"{chunk.title}\n\n{chunk.content}"
                    texts.append(text)
                    chunk_ids.append(chunk.id)
                
                # Generate embeddings
                logger.info(f"Generating embeddings for batch {i//batch_size + 1}/{(total_chunks + batch_size - 1)//batch_size}")
                embeddings = self.model.encode(texts, show_progress_bar=True)
                
                # Normalize embeddings for cosine similarity
                embeddings = normalize(embeddings, norm='l2')
                
                # Save embeddings to database
                for chunk_id, embedding in zip(chunk_ids, embeddings):
                    try:
                        # Delete existing embedding if force_recreate
                        if force_recreate:
                            existing = NotesEmbedding.query.filter_by(
                                chunk_id=chunk_id, 
                                model_name=self.model_name
                            ).first()
                            if existing:
                                db.session.delete(existing)
                        
                        # Create new embedding
                        notes_embedding = NotesEmbedding(
                            chunk_id=chunk_id,
                            model_name=self.model_name,
                            vector_dimension=len(embedding)
                        )
                        notes_embedding.set_vector(embedding)
                        
                        db.session.add(notes_embedding)
                        processed += 1
                        
                    except Exception as e:
                        logger.error(f"Error saving embedding for chunk {chunk_id}: {e}")
                        db.session.rollback()
                        continue
                
                # Commit batch
                try:
                    db.session.commit()
                    logger.info(f"Saved embeddings for {len(batch_chunks)} chunks")
                except Exception as e:
                    logger.error(f"Error committing embeddings batch: {e}")
                    db.session.rollback()
            
            logger.info(f"Successfully created embeddings for {processed} chunks")
    
    def build_faiss_index(self):
        """Build FAISS index from existing embeddings."""
        logger.info("Building FAISS index from existing embeddings...")
        
        with app.app_context():
            # Get all embeddings for this model
            embeddings_query = NotesEmbedding.query.filter_by(model_name=self.model_name).all()
            
            if not embeddings_query:
                logger.error("No embeddings found for this model")
                return False
            
            logger.info(f"Found {len(embeddings_query)} embeddings")
            
            # Prepare vectors and mappings
            vectors = []
            chunk_ids = []
            
            for embedding in embeddings_query:
                vector = embedding.get_vector()
                vectors.append(vector)
                chunk_ids.append(embedding.chunk_id)
            
            # Convert to numpy array
            vectors = np.array(vectors).astype('float32')
            
            # Create FAISS index
            dimension = vectors.shape[1]
            self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
            self.faiss_index.add(vectors)
            
            # Create mappings
            self.chunk_id_to_index = {chunk_id: i for i, chunk_id in enumerate(chunk_ids)}
            self.index_to_chunk_id = {i: chunk_id for i, chunk_id in enumerate(chunk_ids)}
            
            logger.info(f"FAISS index built with {len(vectors)} vectors")
            return True
    
    def search_similar_chunks(self, query: str, top_k: int = 5, min_score: float = 0.3) -> List[Dict]:
        """
        Search for chunks similar to the query.
        
        Args:
            query: Search query
            top_k: Number of top results to return
            min_score: Minimum similarity score threshold
            
        Returns:
            List of dictionaries with chunk information and scores
        """
        if not self.model:
            self.load_model()
        
        if not self.faiss_index:
            if not self.build_faiss_index():
                return []
        
        # Generate query embedding
        query_embedding = self.model.encode([query])
        query_embedding = normalize(query_embedding, norm='l2').astype('float32')
        
        # Search FAISS index
        scores, indices = self.faiss_index.search(query_embedding, top_k * 2)  # Get extra to filter
        
        results = []
        with app.app_context():
            for score, index in zip(scores[0], indices[0]):
                if score < min_score:
                    continue
                
                chunk_id = self.index_to_chunk_id.get(index)
                if not chunk_id:
                    continue
                
                # Get chunk from database
                chunk = NotesChunk.query.get(chunk_id)
                if not chunk:
                    continue
                
                result = {
                    'chunk': chunk.to_dict(),
                    'similarity_score': float(score),
                    'relevance_type': self._get_relevance_type(score)
                }
                results.append(result)
        
        # Sort by score and limit results
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return results[:top_k]
    
    def _get_relevance_type(self, score: float) -> str:
        """
        Determine relevance type based on similarity score.
        
        Args:
            score: Similarity score (0-1)
            
        Returns:
            Relevance type string
        """
        if score >= 0.8:
            return "highly_relevant"
        elif score >= 0.6:
            return "relevant"
        elif score >= 0.4:
            return "somewhat_relevant"
        else:
            return "marginally_relevant"
    
    def get_chunk_context(self, chunk_id: int, include_siblings: bool = True) -> Dict:
        """
        Get contextual information for a chunk including parent and sibling sections.
        
        Args:
            chunk_id: ID of the chunk
            include_siblings: Whether to include sibling sections
            
        Returns:
            Dictionary with chunk and context information
        """
        with app.app_context():
            chunk = NotesChunk.query.get(chunk_id)
            if not chunk:
                return {}
            
            context = {
                'chunk': chunk.to_dict(),
                'parent_sections': [],
                'sibling_sections': []
            }
            
            # Get parent sections (sections with lower level in same file)
            if chunk.level > 1:
                parent_chunks = NotesChunk.query.filter(
                    NotesChunk.filename == chunk.filename,
                    NotesChunk.level < chunk.level,
                    NotesChunk.start_line < chunk.start_line
                ).order_by(NotesChunk.start_line.desc()).limit(3).all()
                
                context['parent_sections'] = [c.to_dict() for c in parent_chunks]
            
            # Get sibling sections (same level in same file)
            if include_siblings:
                sibling_chunks = NotesChunk.query.filter(
                    NotesChunk.filename == chunk.filename,
                    NotesChunk.level == chunk.level,
                    NotesChunk.id != chunk.id
                ).order_by(NotesChunk.start_line).limit(5).all()
                
                context['sibling_sections'] = [c.to_dict() for c in sibling_chunks]
            
            return context


def main():
    """Main function to set up the RAG system."""
    if not DEPENDENCIES_AVAILABLE:
        logger.error("Cannot run RAG system without required dependencies")
        return
    
    logger.info("Starting Notes RAG system setup...")
    
    # Initialize RAG system
    rag_system = NotesRAGSystem()
    
    # Create embeddings for all chunks
    logger.info("Creating embeddings for chunks...")
    rag_system.create_embeddings_for_chunks(batch_size=32)
    
    # Build FAISS index
    logger.info("Building FAISS index...")
    if rag_system.build_faiss_index():
        logger.info("RAG system setup completed successfully!")
        
        # Test the system
        test_query = "organic chemistry functional groups"
        logger.info(f"Testing with query: '{test_query}'")
        results = rag_system.search_similar_chunks(test_query, top_k=3)
        
        logger.info(f"Found {len(results)} relevant chunks:")
        for i, result in enumerate(results, 1):
            chunk = result['chunk']
            score = result['similarity_score']
            logger.info(f"  {i}. {chunk['title']} (score: {score:.3f})")
    else:
        logger.error("Failed to build FAISS index")


if __name__ == "__main__":
    main()
