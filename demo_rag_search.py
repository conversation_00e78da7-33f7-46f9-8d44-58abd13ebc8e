#!/usr/bin/env python3
"""
Demo script for the Chemistry Notes RAG system.

This script demonstrates how to use the RAG system to search for relevant note sections.
"""

import os
import sys

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from notes_rag_system import NotesRAGSystem

def demo_search():
    """Demonstrate the RAG search functionality."""
    print("🧪 Chemistry Notes RAG System Demo")
    print("=" * 50)
    
    # Initialize RAG system with the best model
    print("Initializing RAG system with all-mpnet-base-v2 (best accuracy model)...")
    rag_system = NotesRAGSystem()
    
    # Sample queries to demonstrate different types of searches
    sample_queries = [
        "organic chemistry functional groups",
        "chemical bonding molecular orbital theory",
        "acid base equilibrium pH calculations",
        "electrochemistry galvanic cells",
        "reaction kinetics rate laws",
        "periodic table electron configuration",
        "thermodynamics enthalpy entropy",
        "alkenes addition reactions",
        "transition metals coordination compounds",
        "carboxylic acids derivatives"
    ]
    
    print(f"\nTesting {len(sample_queries)} sample queries...\n")
    
    for i, query in enumerate(sample_queries, 1):
        print(f"🔍 Query {i}: '{query}'")
        print("-" * 60)
        
        try:
            # Search for relevant chunks
            results = rag_system.search_similar_chunks(query, top_k=3, min_score=0.3)
            
            if results:
                print(f"Found {len(results)} relevant sections:")
                for j, result in enumerate(results, 1):
                    chunk = result['chunk']
                    score = result['similarity_score']
                    relevance = result['relevance_type']
                    
                    print(f"  {j}. {chunk['title']}")
                    print(f"     📁 File: {chunk['filename']}")
                    print(f"     📊 Similarity: {score:.3f} ({relevance})")
                    print(f"     📝 Preview: {chunk['content'][:100]}...")
                    print()
            else:
                print("  No relevant sections found.")
                print()
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
            print()
    
    # Interactive search
    print("\n" + "=" * 50)
    print("🔍 Interactive Search")
    print("=" * 50)
    print("Enter your own search queries (type 'quit' to exit):")
    
    while True:
        try:
            query = input("\n🔍 Search query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
                
            if not query:
                continue
            
            print(f"\nSearching for: '{query}'")
            print("-" * 40)
            
            results = rag_system.search_similar_chunks(query, top_k=5, min_score=0.2)
            
            if results:
                print(f"Found {len(results)} relevant sections:")
                for i, result in enumerate(results, 1):
                    chunk = result['chunk']
                    score = result['similarity_score']
                    relevance = result['relevance_type']
                    
                    print(f"\n{i}. {chunk['title']}")
                    print(f"   📁 {chunk['filename']} (Level {chunk['level']})")
                    print(f"   📊 Similarity: {score:.3f} ({relevance})")
                    print(f"   📝 {chunk['content'][:200]}...")
                    
                    # Show parent sections for context
                    if chunk['parent_sections']:
                        print(f"   🔗 Context: {' > '.join(chunk['parent_sections'])}")
            else:
                print("No relevant sections found. Try a different query.")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n👋 Thanks for using the Chemistry Notes RAG system!")

def demo_context():
    """Demonstrate getting contextual information for a chunk."""
    print("\n🔗 Context Demo")
    print("=" * 30)
    
    with app.app_context():
        from models import NotesChunk
        
        # Get a sample chunk
        sample_chunk = NotesChunk.query.first()
        
        if sample_chunk:
            print(f"Getting context for: {sample_chunk.title}")
            
            rag_system = NotesRAGSystem()
            context = rag_system.get_chunk_context(sample_chunk.id)
            
            if context:
                chunk_info = context['chunk']
                print(f"\n📄 Main Section: {chunk_info['title']}")
                print(f"📁 File: {chunk_info['filename']}")
                print(f"📊 Level: {chunk_info['level']}")
                
                if context['parent_sections']:
                    print(f"\n👆 Parent Sections:")
                    for parent in context['parent_sections']:
                        print(f"  - {parent['title']}")
                
                if context['sibling_sections']:
                    print(f"\n👥 Sibling Sections:")
                    for sibling in context['sibling_sections'][:3]:  # Show first 3
                        print(f"  - {sibling['title']}")
        else:
            print("No chunks found in database.")

def main():
    """Main function."""
    try:
        with app.app_context():
            demo_search()
            demo_context()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
