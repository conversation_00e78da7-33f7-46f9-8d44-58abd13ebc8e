# CSV Upload Guide for Bulk Question Upload

## Overview

The CSV upload feature allows administrators to bulk upload questions and their parts using a structured CSV or Excel file format. This is ideal for importing large numbers of questions efficiently.

## Getting Started

1. **Access the Bulk Upload Page**: Navigate to the admin panel and click on "Bulk Upload Questions"
2. **Select CSV/Excel Upload Tab**: Choose the "CSV/Excel Upload" tab (default)
3. **Download Template**: Click "Download Template" to get the sample CSV file
4. **Prepare Your Data**: Fill in your questions using the template format
5. **Upload and Submit**: Upload your file and submit

## CSV File Format

### Required Columns

| Column Name | Description | Required | Example |
|-------------|-------------|----------|---------|
| `question_title` | Title of the question | Yes | "Differentiation" |
| `question_description` | Detailed description of the question | No | "Find the derivative of the following function." |
| `question_source` | Source or reference for the question | No | "Calculus Textbook" |
| `part_description` | Description of this specific part | Yes | "Find the derivative of f(x) = x^2 + 3x + 2." |
| `part_answer` | Expected answer for this part | Yes | "f'(x) = 2x + 3" |
| `part_score` | Points awarded for this part | Yes | "2" |
| `marking_points` | Detailed marking scheme (optional) | No | "Apply power rule:1;Simplify:1" |

### File Structure Rules

1. **Headers**: First row must contain the column headers exactly as shown above
2. **Question Separation**: Use empty rows to separate different questions
3. **Multiple Parts**: Questions can have multiple parts by using multiple consecutive rows
4. **Encoding**: Save files in UTF-8 encoding to support special characters

### Marking Points Format

Marking points should be formatted as: `"description:score;description:score"`

**Example**: `"Apply power rule to x^2:1;Apply coefficient rule to 3x:1"`

- Each marking point consists of a description and score separated by a colon `:`
- Multiple marking points are separated by semicolons `;`
- Total marking points score should typically equal the part score

## Example CSV Structure

```csv
question_title,question_description,question_source,part_description,part_answer,part_score,marking_points
"Differentiation","Find the derivative","Textbook","Find d/dx of x^2 + 3x","2x + 3","2","Power rule:1;Linear term:1"

"Integration","Compute integrals","Textbook","Evaluate ∫x^2 dx","x^3/3 + C","2","Increase exponent:1;Divide by new exponent:1"
"Integration","Compute integrals","Textbook","Evaluate ∫sin(x) dx","-cos(x) + C","2","Standard integral:1;Add constant:1"

"New Question","Another question","Source","Part description","Answer","1","Marking:1"
```

## Best Practices

### Data Preparation
- **Use the template**: Always start with the downloaded template
- **Check encoding**: Ensure your file is saved in UTF-8 format
- **Validate data**: Review all entries before uploading
- **Test with small batches**: Start with a few questions to test the format

### Question Organization
- **Logical grouping**: Group related questions together
- **Clear descriptions**: Write clear, concise part descriptions
- **Consistent formatting**: Use consistent formatting for mathematical expressions
- **Appropriate scoring**: Ensure part scores reflect question difficulty

### Error Prevention
- **Required fields**: Ensure all required fields are filled
- **Numeric scores**: Part scores must be valid integers
- **Empty row separation**: Use completely empty rows between questions
- **Quote handling**: Use double quotes for text containing commas

## Supported File Formats

- **CSV (.csv)**: Comma-separated values
- **Excel (.xlsx)**: Modern Excel format
- **Excel (.xls)**: Legacy Excel format

## Troubleshooting

### Common Issues

1. **"Error processing data file"**
   - Check that all required columns are present
   - Verify that part_score contains valid integers
   - Ensure proper UTF-8 encoding

2. **Questions not separated properly**
   - Make sure empty rows between questions are completely empty
   - Check that there are no hidden characters in empty rows

3. **Marking points not created**
   - Verify the format: "description:score;description:score"
   - Ensure scores are valid integers
   - Check for proper semicolon separation

4. **File upload fails**
   - Verify file format is CSV, XLSX, or XLS
   - Check file size limitations
   - Ensure you have admin permissions

### Tips for Success

- **Preview before upload**: Use Excel or a text editor to review your file
- **Start small**: Test with 2-3 questions first
- **Backup data**: Keep a backup of your original data
- **Check results**: Review uploaded questions in the admin panel

## Advanced Features

### Multiple Choice Questions
Currently, the CSV upload creates short answer questions (SAQ) by default. For multiple choice questions, you'll need to edit them after upload through the question editor.

### Question Prerequisites
Question relationships and prerequisites need to be set up after upload through the dojo management interface.

### Attachments
File attachments are not supported through CSV upload. Add attachments after upload through the question editor.

## Support

If you encounter issues with CSV upload:
1. Check this guide for common solutions
2. Verify your CSV format against the template
3. Test with a smaller subset of data
4. Contact the system administrator for technical support
