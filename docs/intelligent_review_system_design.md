# Intelligent Review System Design

## Overview
This document outlines the design for an intelligent review system that replaces the current filtering-based approach with a performance-based recommendation engine using spaced repetition algorithms.

## Current System Analysis

### Current Review System Issues
1. **Passive Filtering**: Users manually filter by subject, status, and difficulty
2. **No Learning Science**: No consideration of memory retention or optimal review timing
3. **No Performance Analysis**: Doesn't identify weak areas or learning patterns
4. **Static Recommendations**: Same problems shown regardless of user progress

### Current Data Structure
- **Submissions**: user_id, question_id, part_id, answer, score, timestamp
- **Questions**: id, title, description, topic_id, source
- **Parts**: id, description, answer, score, question_id, input_type
- **Topics**: id, name, subject_id
- **Subjects**: id, name, syllabus
- **Users**: onboarding data, subjects_taken, subject_confidence

## Intelligent Review System Design

### Core Components

#### 1. Spaced Repetition Algorithm
Based on the SuperMemo SM-2 algorithm with adaptations for educational content:

**Key Principles:**
- **Ease Factor (EF)**: Measures how easy a problem is for the user (1.3 to 2.5)
- **Repetition Number**: How many times the user has reviewed this problem
- **Interval**: Days until next review (starts at 1, then 6, then calculated)
- **Quality of Response**: 0-5 scale based on performance

**Interval Calculation:**
```
If n = 1: interval = 1
If n = 2: interval = 6
If n > 2: interval = previous_interval * ease_factor

Ease Factor Update:
EF = EF + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
```

#### 2. Performance Analysis Engine

**Metrics to Track:**
- **Success Rate**: Percentage of correct answers per topic/subject
- **Learning Velocity**: Rate of improvement over time
- **Retention Rate**: Performance on repeated problems
- **Difficulty Progression**: Ability to handle harder problems
- **Time Patterns**: When user performs best/worst

**Analysis Functions:**
- Identify weak topics (success rate < 70%)
- Detect learning plateaus (no improvement over time)
- Find optimal difficulty level (challenge without frustration)
- Predict knowledge decay (when to review)

#### 3. Recommendation Categories

**Priority-Based Recommendations:**

1. **Critical Review** (Highest Priority)
   - Problems with low success rate (<50%)
   - Recently failed problems (within 3 days)
   - Spaced repetition due today

2. **Reinforcement Practice** (High Priority)
   - Problems with moderate success rate (50-80%)
   - Topics with declining performance
   - Spaced repetition due within 2 days

3. **Mastery Maintenance** (Medium Priority)
   - Well-performed problems due for review
   - Long-term retention checks
   - Spaced repetition due within a week

4. **New Challenges** (Low Priority)
   - Unattempted problems in strong areas
   - Progressive difficulty increase
   - Exploration of new topics

#### 4. Adaptive Difficulty System

**Difficulty Scoring:**
- Combine user confidence levels with actual performance
- Adjust based on success rates and time taken
- Consider topic complexity and prerequisites

**Dynamic Adjustment:**
- Increase difficulty after consistent success (>80% for 5+ problems)
- Decrease difficulty after repeated failures (<40% for 3+ problems)
- Maintain current level for moderate performance (40-80%)

### Database Schema Extensions

#### New Tables Required:

```sql
-- Track spaced repetition data for each user-problem combination
CREATE TABLE review_schedule (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    question_id INTEGER REFERENCES questions(id),
    part_id INTEGER REFERENCES parts(id),
    ease_factor REAL DEFAULT 2.5,
    repetition_number INTEGER DEFAULT 0,
    interval_days INTEGER DEFAULT 1,
    next_review_date DATE,
    last_reviewed DATE,
    quality_score INTEGER, -- 0-5 based on last performance
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Track user performance analytics
CREATE TABLE performance_analytics (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    topic_id INTEGER REFERENCES topics(id),
    subject_id INTEGER REFERENCES subjects(id),
    success_rate REAL,
    total_attempts INTEGER,
    correct_attempts INTEGER,
    average_score REAL,
    learning_velocity REAL, -- Rate of improvement
    last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Track recommendation history and effectiveness
CREATE TABLE recommendation_history (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    question_id INTEGER REFERENCES questions(id),
    part_id INTEGER REFERENCES parts(id),
    recommendation_type VARCHAR(50), -- 'critical', 'reinforcement', 'mastery', 'new'
    recommended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempted_at TIMESTAMP,
    score_achieved REAL,
    was_helpful BOOLEAN -- User feedback on recommendation quality
);
```

### API Endpoints

#### New Endpoints:
1. `GET /api/recommendations` - Get personalized problem recommendations
2. `POST /api/review-feedback` - Submit feedback on recommendation quality
3. `GET /api/performance-analytics` - Get user performance insights
4. `POST /api/update-review-schedule` - Update spaced repetition schedule
5. `GET /api/learning-insights` - Get learning pattern analysis

### User Interface Design

#### Main Review Dashboard:
- **Today's Reviews**: Problems due for review today
- **Recommended Practice**: AI-suggested problems based on performance
- **Progress Insights**: Visual analytics of learning progress
- **Subject Breakdown**: Performance by topic with recommendations

#### Recommendation Cards:
Each recommendation includes:
- Problem preview
- Reason for recommendation
- Expected difficulty
- Last attempt date and score
- Estimated time to complete

### Implementation Strategy

#### Phase 1: Data Models and Core Algorithm
1. Create new database tables
2. Implement spaced repetition algorithm
3. Build performance analysis functions
4. Create data migration scripts

#### Phase 2: Recommendation Engine
1. Build recommendation categorization logic
2. Implement adaptive difficulty system
3. Create recommendation scoring algorithm
4. Add user feedback collection

#### Phase 3: API and Backend Integration
1. Create new API endpoints
2. Refactor existing review route
3. Integrate with submission tracking
4. Add background jobs for analytics

#### Phase 4: Frontend Redesign
1. Design new review interface
2. Create recommendation components
3. Add progress visualization
4. Implement user feedback mechanisms

### Success Metrics

#### Learning Effectiveness:
- Improved retention rates (measured by repeat performance)
- Faster skill acquisition (time to mastery)
- Better long-term knowledge retention
- Increased user engagement with review system

#### System Performance:
- Recommendation accuracy (user satisfaction ratings)
- Reduced time to find relevant practice problems
- Increased completion rates for recommended problems
- Positive user feedback on recommendation quality

### Future Enhancements

1. **Machine Learning Integration**: Use ML models to improve recommendation accuracy
2. **Collaborative Filtering**: Recommend problems based on similar users' success
3. **Adaptive Testing**: Dynamic difficulty adjustment during problem sessions
4. **Predictive Analytics**: Forecast learning outcomes and identify at-risk areas
5. **Gamification**: Achievement systems based on review consistency and improvement
