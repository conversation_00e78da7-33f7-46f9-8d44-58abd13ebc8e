# Intelligent Review System - Setup Guide

## Overview

The Intelligent Review System transforms the basic filtering-based review functionality into a sophisticated learning platform that uses spaced repetition, performance analysis, and adaptive difficulty to provide personalized problem recommendations.

## Features

### 🧠 Spaced Repetition Algorithm
- **SuperMemo SM-2 Algorithm**: Proven spaced repetition algorithm with educational adaptations
- **Quality Score Mapping**: Automatic conversion of performance scores to quality ratings (0-5 scale)
- **Adaptive Intervals**: Dynamic review scheduling based on user performance
- **Ease Factor Management**: Personalized difficulty adjustments (1.3-2.5 range)

### 📊 Performance Analytics
- **Topic-Level Analysis**: Success rates, learning velocity, and retention tracking
- **Subject-Level Insights**: Comprehensive performance across subjects
- **Weak Area Identification**: Automatic detection of areas needing improvement
- **Strong Area Recognition**: Identification of mastered topics for maintenance

### 🎯 Intelligent Recommendations
- **Critical Review**: Overdue and due spaced repetition items
- **Reinforcement Practice**: Problems in weak areas requiring attention
- **Mastery Maintenance**: Review of strong areas to prevent forgetting
- **New Challenges**: Unexplored problems in areas of strength

### 📈 Learning Insights
- **Progress Tracking**: Comprehensive learning analytics and trends
- **Recommendation Effectiveness**: Tracking of recommendation quality and user feedback
- **Adaptive Learning**: System learns from user behavior to improve recommendations

## Installation

### Step 1: Database Migration

Run the migration script to create the new database tables:

```bash
# Full setup (recommended for new installations)
python migrations/add_intelligent_review_tables.py --full-setup

# Or run individual steps:
python migrations/add_intelligent_review_tables.py --create-tables
python migrations/add_intelligent_review_tables.py --init-analytics
python migrations/add_intelligent_review_tables.py --init-schedules
python migrations/add_intelligent_review_tables.py --verify
```

### Step 2: Verify Installation

Check that all components are working:

```bash
python migrations/add_intelligent_review_tables.py --verify
```

Expected output:
```
✓ review_schedule: X records
✓ performance_analytics: X records  
✓ recommendation_history: X records
✓ Spaced repetition engine working
✓ Performance analysis engine working
✓ Recommendation engine working
```

### Step 3: Update Submission Tracking

The system automatically tracks new submissions, but you may want to ensure existing submission endpoints trigger the analytics updates. The key integration points are:

1. **After each submission**: Update review schedule and performance analytics
2. **Periodic maintenance**: Run analytics updates for data consistency

## API Endpoints

### Core Recommendation API

#### `GET /api/recommendations`
Get personalized problem recommendations.

**Parameters:**
- `limit` (optional): Maximum number of recommendations (default: 10)
- `category` (optional): Filter by recommendation type

**Response:**
```json
{
  "recommendations": [
    {
      "id": "123_456",
      "type": "critical",
      "question_id": 123,
      "part_id": 456,
      "priority_score": 85.5,
      "reason": "Review overdue by 2 days",
      "estimated_difficulty": "medium",
      "question": {...},
      "part": {...},
      "topic": {...},
      "subject": {...}
    }
  ],
  "total_count": 10,
  "user_id": 789
}
```

#### `GET /api/recommendations/categories`
Get recommendations organized by category.

#### `GET /api/review-schedule`
Get spaced repetition schedule and due reviews.

#### `GET /api/performance-analytics`
Get comprehensive performance analytics and learning insights.

#### `POST /api/review-feedback`
Submit feedback on recommendation quality.

**Request Body:**
```json
{
  "question_id": 123,
  "part_id": 456,
  "was_helpful": true,
  "score_achieved": 85.5
}
```

#### `POST /api/update-review-schedule`
Update spaced repetition schedule after submission.

**Request Body:**
```json
{
  "question_id": 123,
  "part_id": 456,
  "score": 85,
  "max_score": 100
}
```

## Database Schema

### ReviewSchedule
Tracks spaced repetition data for each user-problem combination.

```sql
CREATE TABLE review_schedule (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    question_id INTEGER NOT NULL,
    part_id INTEGER NOT NULL,
    ease_factor FLOAT DEFAULT 2.5,
    repetition_number INTEGER DEFAULT 0,
    interval_days INTEGER DEFAULT 1,
    next_review_date DATE NOT NULL,
    quality_score INTEGER,
    last_reviewed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, question_id, part_id)
);
```

### PerformanceAnalytics
Stores aggregated performance metrics by topic and subject.

```sql
CREATE TABLE performance_analytics (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    topic_id INTEGER,
    subject_id INTEGER,
    total_attempts INTEGER DEFAULT 0,
    successful_attempts INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0.0,
    average_score FLOAT DEFAULT 0.0,
    learning_velocity FLOAT DEFAULT 0.0,
    last_attempt_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, topic_id, subject_id)
);
```

### RecommendationHistory
Records recommendation history for effectiveness tracking.

```sql
CREATE TABLE recommendation_history (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    question_id INTEGER NOT NULL,
    part_id INTEGER NOT NULL,
    recommendation_type VARCHAR(50) NOT NULL,
    recommendation_reason TEXT,
    priority_score FLOAT,
    recommended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempted_at TIMESTAMP,
    was_helpful BOOLEAN,
    score_achieved FLOAT
);
```

## Configuration

### Algorithm Parameters

The system uses several configurable parameters in `routes/spaced_repetition.py`:

```python
# Quality score thresholds
QUALITY_THRESHOLDS = {
    5: 1.0,    # Perfect (100%)
    4: 0.8,    # Good (80-99%)
    3: 0.6,    # Fair (60-79%)
    2: 0.4,    # Poor (40-59%)
    1: 0.2,    # Bad (20-39%)
    0: 0.0     # Fail (0-19%)
}

# Ease factor bounds
MIN_EASE_FACTOR = 1.3
MAX_EASE_FACTOR = 2.5

# Performance thresholds
WEAK_AREA_THRESHOLD = 0.5    # <50% success rate
STRONG_AREA_THRESHOLD = 0.8  # >80% success rate
MIN_ATTEMPTS_FOR_ANALYSIS = 3
```

### Recommendation Weights

Priority calculation weights in `routes/recommendation_engine.py`:

```python
WEIGHTS = {
    'spaced_repetition': 0.4,      # 40% weight for spaced repetition
    'performance_weakness': 0.3,    # 30% weight for weak areas
    'learning_velocity': 0.2,       # 20% weight for learning speed
    'user_preference': 0.1          # 10% weight for user preferences
}
```

## Usage Examples

### Basic Integration

```python
from routes.recommendation_engine import RecommendationEngine
from routes.spaced_repetition import SpacedRepetitionEngine
from routes.performance_analysis import PerformanceAnalysisEngine

# Get recommendations for a user
recommendations = RecommendationEngine.generate_recommendations(user_id=123, limit=10)

# Update review schedule after submission
SpacedRepetitionEngine.update_review_schedule(
    user_id=123,
    question_id=456,
    part_id=789,
    score=85,
    max_score=100
)

# Get learning insights
insights = PerformanceAnalysisEngine.get_learning_insights(user_id=123)
```

### Frontend Integration

The new review interface is available at `/review` (intelligent mode) with fallback to legacy mode:

```html
<!-- Intelligent Review (default) -->
<a href="/review">Review Problems</a>

<!-- Legacy Review (fallback) -->
<a href="/review?mode=legacy">Legacy Review</a>
```

## Monitoring and Maintenance

### Performance Monitoring

Monitor the system performance using the built-in analytics:

```python
# Get recommendation effectiveness stats
stats = RecommendationEngine.get_recommendation_effectiveness_stats(user_id)

# Monitor review statistics
review_stats = SpacedRepetitionEngine.get_review_statistics(user_id)
```

### Data Maintenance

Run periodic maintenance to ensure data consistency:

```python
# Update analytics for all users (run periodically)
from routes.performance_analysis import PerformanceAnalysisEngine

for user_id in active_user_ids:
    PerformanceAnalysisEngine.update_all_analytics(user_id)
```

### Troubleshooting

Common issues and solutions:

1. **No recommendations appearing**: Check if user has sufficient submission history
2. **Incorrect difficulty estimates**: Verify performance analytics are up to date
3. **API errors**: Check database connections and model imports
4. **Missing review schedules**: Run the initialization script for existing data

## Next Steps

1. **Test the system** with real user data
2. **Monitor recommendation effectiveness** and adjust algorithms as needed
3. **Gather user feedback** on recommendation quality
4. **Implement additional features** like streak tracking or achievement systems
5. **Optimize performance** for large user bases

## Support

For issues or questions about the intelligent review system:

1. Check the verification script output
2. Review the application logs for errors
3. Ensure all database migrations completed successfully
4. Verify that all required imports are available
