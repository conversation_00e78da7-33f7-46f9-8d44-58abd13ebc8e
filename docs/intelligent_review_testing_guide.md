# Intelligent Review System - Testing Guide

## Overview

This guide provides comprehensive testing procedures for the Intelligent Review System to ensure all components work correctly and provide accurate recommendations.

## Pre-Testing Setup

### 1. Database Verification

Before testing, ensure the database migration completed successfully:

```bash
python migrations/add_intelligent_review_tables.py --verify
```

Expected output should show all tables created and engines working.

### 2. Test Data Requirements

For meaningful testing, you need:
- At least 2-3 test users with different performance patterns
- Multiple subjects and topics with questions
- Historical submission data (or create test submissions)

## Testing Scenarios

### Scenario 1: New User (No History)

**Test User**: Fresh account with no submissions

**Expected Behavior**:
- No critical reviews (empty spaced repetition schedule)
- No reinforcement recommendations (no weak areas identified)
- No mastery maintenance (no strong areas)
- New challenge recommendations based on user's selected subjects
- Empty performance analytics

**Test Steps**:
1. Create a new user account
2. Navigate to `/review`
3. Verify the interface shows "No recommendations available"
4. Check API endpoint `/api/recommendations` returns empty arrays
5. Verify `/api/performance-analytics` shows minimal data

### Scenario 2: User with Poor Performance

**Test User**: Account with multiple failed attempts in specific topics

**Setup**:
```python
# Create test submissions with low scores
from models import Submission, db
from datetime import datetime, timedelta

user_id = 123  # Test user
question_id = 456  # Test question
part_id = 789  # Test part

# Create multiple failed attempts
for i in range(5):
    submission = Submission(
        user_id=user_id,
        question_id=question_id,
        part_id=part_id,
        score=0,  # Failed attempts
        timestamp=datetime.now() - timedelta(days=i)
    )
    db.session.add(submission)
db.session.commit()

# Update analytics
from routes.performance_analysis import PerformanceAnalysisEngine
PerformanceAnalysisEngine.update_performance_analytics(user_id, topic_id=topic.id)
```

**Expected Behavior**:
- Reinforcement recommendations for weak topics
- Critical reviews for overdue items
- Lower estimated difficulty for recommended problems
- Performance analytics showing weak areas

**Test Steps**:
1. Login as test user
2. Navigate to `/review`
3. Verify "Reinforcement Practice" section appears
4. Check that weak topics are identified in performance analytics
5. Verify API returns appropriate recommendation types

### Scenario 3: User with Strong Performance

**Test User**: Account with high success rates in multiple topics

**Setup**:
```python
# Create test submissions with high scores
for i in range(10):
    submission = Submission(
        user_id=user_id,
        question_id=question_id + i,
        part_id=part_id + i,
        score=95,  # High scores
        timestamp=datetime.now() - timedelta(days=i)
    )
    db.session.add(submission)
db.session.commit()
```

**Expected Behavior**:
- Mastery maintenance recommendations
- New challenge recommendations in strong areas
- Higher estimated difficulty for new problems
- Strong areas identified in analytics

### Scenario 4: Spaced Repetition Testing

**Test User**: Account with items due for review

**Setup**:
```python
from routes.spaced_repetition import SpacedRepetitionEngine
from datetime import date, timedelta

# Create overdue review schedule
schedule = ReviewSchedule(
    user_id=user_id,
    question_id=question_id,
    part_id=part_id,
    next_review_date=date.today() - timedelta(days=2),  # 2 days overdue
    ease_factor=2.0,
    repetition_number=3,
    interval_days=7,
    quality_score=2  # Poor previous performance
)
db.session.add(schedule)
db.session.commit()
```

**Expected Behavior**:
- Critical review recommendations for overdue items
- High priority scores for overdue items
- Proper calculation of days overdue
- Review statistics showing overdue count

## API Testing

### 1. Recommendations API

```bash
# Test basic recommendations
curl -X GET "http://localhost:5000/api/recommendations?limit=5" \
  -H "Cookie: session=your_session_cookie"

# Test category filtering
curl -X GET "http://localhost:5000/api/recommendations?category=critical" \
  -H "Cookie: session=your_session_cookie"

# Test recommendation categories
curl -X GET "http://localhost:5000/api/recommendations/categories" \
  -H "Cookie: session=your_session_cookie"
```

### 2. Review Schedule API

```bash
# Test review schedule
curl -X GET "http://localhost:5000/api/review-schedule?days_ahead=7" \
  -H "Cookie: session=your_session_cookie"
```

### 3. Performance Analytics API

```bash
# Test performance analytics
curl -X GET "http://localhost:5000/api/performance-analytics" \
  -H "Cookie: session=your_session_cookie"
```

### 4. Feedback API

```bash
# Test feedback submission
curl -X POST "http://localhost:5000/api/review-feedback" \
  -H "Content-Type: application/json" \
  -H "Cookie: session=your_session_cookie" \
  -d '{
    "question_id": 123,
    "part_id": 456,
    "was_helpful": true,
    "score_achieved": 85.5
  }'
```

### 5. Schedule Update API

```bash
# Test schedule update
curl -X POST "http://localhost:5000/api/update-review-schedule" \
  -H "Content-Type: application/json" \
  -H "Cookie: session=your_session_cookie" \
  -d '{
    "question_id": 123,
    "part_id": 456,
    "score": 85,
    "max_score": 100
  }'
```

## Algorithm Testing

### 1. Spaced Repetition Algorithm

Test the SM-2 algorithm implementation:

```python
from routes.spaced_repetition import SpacedRepetitionEngine

# Test quality score calculation
test_cases = [
    (100, 100, 5),  # Perfect score -> Quality 5
    (85, 100, 4),   # Good score -> Quality 4
    (65, 100, 3),   # Fair score -> Quality 3
    (45, 100, 2),   # Poor score -> Quality 2
    (25, 100, 1),   # Bad score -> Quality 1
    (0, 100, 0),    # Fail -> Quality 0
]

for score, max_score, expected_quality in test_cases:
    quality = SpacedRepetitionEngine.calculate_quality_score(score, max_score)
    assert quality == expected_quality, f"Expected {expected_quality}, got {quality}"

# Test interval calculation
intervals = []
for quality in range(6):
    interval, ease = SpacedRepetitionEngine.calculate_next_interval(0, 2.5, quality)
    intervals.append((quality, interval, ease))
    print(f"Quality {quality}: Interval {interval} days, Ease {ease:.2f}")
```

### 2. Performance Analysis

Test performance calculation accuracy:

```python
from routes.performance_analysis import PerformanceAnalysisEngine

# Test with known data
user_id = 123
topic_id = 456

# Create controlled test data
test_submissions = [
    (90, 100),  # 90% success
    (80, 100),  # 80% success
    (95, 100),  # 95% success
    (0, 100),   # 0% success
    (100, 100), # 100% success
]

# Calculate expected success rate: 4/5 = 80%
performance = PerformanceAnalysisEngine.calculate_user_performance_by_topic(user_id, topic_id)
expected_success_rate = 0.8
assert abs(performance['success_rate'] - expected_success_rate) < 0.01
```

### 3. Recommendation Priority

Test recommendation priority calculation:

```python
from routes.recommendation_engine import RecommendationEngine

# Test with different user scenarios
recommendations = RecommendationEngine.generate_recommendations(user_id, limit=10)

# Verify recommendations are sorted by priority
priorities = [rec['priority_score'] for rec in recommendations]
assert priorities == sorted(priorities, reverse=True), "Recommendations not sorted by priority"

# Verify critical items have higher priority than others
critical_recs = [rec for rec in recommendations if rec['type'] == 'critical']
other_recs = [rec for rec in recommendations if rec['type'] != 'critical']

if critical_recs and other_recs:
    min_critical_priority = min(rec['priority_score'] for rec in critical_recs)
    max_other_priority = max(rec['priority_score'] for rec in other_recs)
    assert min_critical_priority >= max_other_priority, "Critical items should have higher priority"
```

## UI Testing

### 1. Interface Functionality

**Manual Testing Checklist**:

- [ ] Review page loads without errors
- [ ] Statistics cards display correct numbers
- [ ] Recommendation cards show proper information
- [ ] Category filtering works (buttons 1-4)
- [ ] Refresh button works (keyboard shortcut 'r')
- [ ] Links to questions work correctly
- [ ] Difficulty badges display appropriate colors
- [ ] Progress bar updates correctly
- [ ] Mode toggle between intelligent/legacy works

### 2. Responsive Design

Test on different screen sizes:
- [ ] Desktop (1920x1080)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)

### 3. Accessibility

- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets standards
- [ ] ARIA labels are present

## Performance Testing

### 1. Database Performance

Test with large datasets:

```python
import time
from routes.recommendation_engine import RecommendationEngine

# Test recommendation generation time
start_time = time.time()
recommendations = RecommendationEngine.generate_recommendations(user_id, limit=20)
end_time = time.time()

generation_time = end_time - start_time
print(f"Recommendation generation took {generation_time:.2f} seconds")

# Should complete in under 2 seconds for typical datasets
assert generation_time < 2.0, f"Recommendation generation too slow: {generation_time:.2f}s"
```

### 2. API Response Times

Test API endpoint performance:

```bash
# Use curl with timing
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:5000/api/recommendations"

# curl-format.txt content:
#     time_namelookup:  %{time_namelookup}\n
#     time_connect:     %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#     time_pretransfer: %{time_pretransfer}\n
#     time_redirect:    %{time_redirect}\n
#     time_starttransfer: %{time_starttransfer}\n
#     ----------\n
#     time_total:       %{time_total}\n
```

## Error Handling Testing

### 1. Database Errors

Test system behavior when database is unavailable:
- [ ] Graceful fallback to legacy mode
- [ ] Appropriate error messages
- [ ] No system crashes

### 2. Invalid Data

Test with invalid inputs:
- [ ] Non-existent user IDs
- [ ] Invalid question/part IDs
- [ ] Malformed API requests
- [ ] SQL injection attempts

### 3. Edge Cases

- [ ] User with no submissions
- [ ] User with only perfect scores
- [ ] User with only failed attempts
- [ ] Questions with no topic/subject
- [ ] Empty database tables

## Validation Criteria

### Success Criteria

The system passes testing if:

1. **Functionality**: All core features work as designed
2. **Performance**: API responses under 2 seconds
3. **Accuracy**: Recommendations match expected patterns
4. **Reliability**: No crashes or data corruption
5. **Usability**: Interface is intuitive and responsive

### Failure Indicators

The system needs fixes if:

1. **Critical errors**: System crashes or data loss
2. **Incorrect recommendations**: Algorithm produces wrong results
3. **Poor performance**: Slow response times (>5 seconds)
4. **UI issues**: Interface broken or unusable
5. **Data inconsistency**: Analytics don't match actual performance

## Reporting Issues

When reporting issues, include:

1. **Environment**: Browser, OS, database version
2. **Steps to reproduce**: Exact sequence of actions
3. **Expected vs actual behavior**: What should happen vs what happened
4. **Error messages**: Full error text and stack traces
5. **Test data**: User IDs, question IDs, and relevant data

## Continuous Testing

Set up automated testing:

1. **Unit tests**: Test individual functions
2. **Integration tests**: Test component interactions
3. **End-to-end tests**: Test complete user workflows
4. **Performance monitoring**: Track response times
5. **Data validation**: Verify recommendation quality over time
