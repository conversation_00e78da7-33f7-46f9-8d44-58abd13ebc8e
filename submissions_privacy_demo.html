<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submissions Privacy Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .demo-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        
        .before {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .before h3 {
            color: #dc2626;
            margin-top: 0;
        }
        
        .after h3 {
            color: #16a34a;
            margin-top: 0;
        }
        
        .submission-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 10px;
            margin: 8px 0;
            font-size: 0.875rem;
        }
        
        .user-badge {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-right: 8px;
        }
        
        .current-user {
            background: #16a34a;
        }
        
        .other-user {
            background: #dc2626;
        }
        
        .demo-info {
            background-color: #e0f2fe;
            border: 1px solid #0284c7;
            color: #0c4a6e;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .check {
            color: #16a34a;
            margin-right: 8px;
        }
        
        .cross {
            color: #dc2626;
            margin-right: 8px;
        }
        
        .route-demo {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.875rem;
            margin: 10px 0;
        }
        
        .route-demo .comment {
            color: #9ca3af;
        }
        
        .route-demo .highlight {
            background: #374151;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>Submissions Privacy Implementation Demo</h1>
    
    <div class="demo-info">
        <strong>Privacy Enhancement:</strong> This demo shows how the submissions system has been modified to ensure users can only view their own submissions, improving privacy and reducing academic dishonesty risks.
    </div>

    <!-- Route Changes -->
    <div class="demo-container">
        <h2>1. Route-Level Changes</h2>
        
        <h3>Before: Public Submissions</h3>
        <div class="route-demo">
<span class="comment"># Old /submissions route - showed all users' submissions</span>
@app.route("/submissions")
def submissions():
    query = Submission.query.join(Part, User)
    <span class="highlight">submissions = query.all()  # All submissions visible</span>
    return render_template("submissions.html", submissions=submissions)
        </div>
        
        <h3>After: Private Submissions</h3>
        <div class="route-demo">
<span class="comment"># New /submissions route - only current user's submissions</span>
@app.route("/submissions")
@login_required
def submissions():
    current_user_id = session['user_id']
    query = Submission.query.join(Part, User)
    <span class="highlight">query = query.filter(Submission.user_id == current_user_id)</span>
    submissions = query.all()
    return render_template("submissions.html", submissions=submissions)
        </div>
    </div>

    <!-- Template Changes -->
    <div class="demo-container">
        <h2>2. Question Page "Show All" Functionality</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ Before: All Users' Submissions</h3>
                <p><strong>Past Submissions</strong></p>
                <div class="submission-item">
                    <span class="user-badge other-user">alice</span>
                    "The answer is 42 because..."
                </div>
                <div class="submission-item">
                    <span class="user-badge current-user">you</span>
                    "I think the answer is 24..."
                </div>
                <div class="submission-item">
                    <span class="user-badge other-user">bob</span>
                    "Based on the formula..."
                </div>
                <div class="submission-item">
                    <span class="user-badge other-user">charlie</span>
                    "The correct approach is..."
                </div>
                <p><small>Users could see everyone's work</small></p>
            </div>
            
            <div class="after">
                <h3>✅ After: Only Your Submissions</h3>
                <p><strong>Your Past Submissions</strong></p>
                <div class="submission-item">
                    <span class="user-badge current-user">you</span>
                    "I think the answer is 24..."
                </div>
                <div class="submission-item">
                    <span class="user-badge current-user">you</span>
                    "Actually, let me recalculate..."
                </div>
                <p><small>Users only see their own work</small></p>
            </div>
        </div>
    </div>

    <!-- Access Control -->
    <div class="demo-container">
        <h2>3. Submission Details Access Control</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ Before: Public Access</h3>
                <div class="route-demo">
@app.route("/submission/&lt;int:id&gt;")
def submission_details(id):
    submission = Submission.query.get(id)
    <span class="highlight">return render_template("details.html", submission=submission)</span>
    <span class="comment"># Anyone could view any submission</span>
                </div>
            </div>
            
            <div class="after">
                <h3>✅ After: Private Access</h3>
                <div class="route-demo">
@app.route("/submission/&lt;int:id&gt;")
@login_required
def submission_details(id):
    submission = Submission.query.get(id)
    <span class="highlight">if submission.user_id != session['user_id']:
        flash('You can only view your own submissions.', 'error')
        return redirect(url_for('submissions'))</span>
    return render_template("details.html", submission=submission)
                </div>
            </div>
        </div>
    </div>

    <!-- Feature Comparison -->
    <div class="demo-container">
        <h2>4. Feature Comparison</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>Before: Public System</h3>
                <ul class="feature-list">
                    <li><span class="cross">✗</span>Users could see all submissions</li>
                    <li><span class="cross">✗</span>Search by username functionality</li>
                    <li><span class="cross">✗</span>Public submission details</li>
                    <li><span class="cross">✗</span>Potential for copying answers</li>
                    <li><span class="cross">✗</span>Privacy concerns</li>
                    <li><span class="cross">✗</span>Social pressure from comparisons</li>
                </ul>
            </div>
            
            <div class="after">
                <h3>After: Private System</h3>
                <ul class="feature-list">
                    <li><span class="check">✓</span>Users only see own submissions</li>
                    <li><span class="check">✓</span>Clean, focused interface</li>
                    <li><span class="check">✓</span>Protected submission details</li>
                    <li><span class="check">✓</span>Prevents academic dishonesty</li>
                    <li><span class="check">✓</span>Enhanced privacy protection</li>
                    <li><span class="check">✓</span>Reduced social pressure</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Benefits -->
    <div class="demo-container">
        <h2>5. Benefits of Privacy Implementation</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div>
                <h3>🛡️ Security & Privacy</h3>
                <ul>
                    <li>Student work remains private</li>
                    <li>Prevents unauthorized access</li>
                    <li>Complies with educational privacy standards</li>
                    <li>Reduces data exposure risks</li>
                </ul>
            </div>
            
            <div>
                <h3>🎯 Learning Experience</h3>
                <ul>
                    <li>Focus on personal progress</li>
                    <li>Reduced comparison anxiety</li>
                    <li>Encourages original thinking</li>
                    <li>Cleaner, less cluttered interface</li>
                </ul>
            </div>
            
            <div>
                <h3>⚡ Performance</h3>
                <ul>
                    <li>Faster query execution</li>
                    <li>Reduced data transfer</li>
                    <li>Better database performance</li>
                    <li>Simplified caching strategies</li>
                </ul>
            </div>
            
            <div>
                <h3>🔧 Maintenance</h3>
                <ul>
                    <li>Simplified code logic</li>
                    <li>Fewer edge cases</li>
                    <li>Easier to understand</li>
                    <li>Reduced support requests</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Implementation Summary -->
    <div class="demo-container">
        <h2>6. Implementation Summary</h2>
        
        <div class="route-demo">
<span class="comment"># Key changes made:</span>

1. <span class="highlight">routes/vault.py</span>
   - Added user_id filtering to /submissions route
   - Added access control to /submission/&lt;id&gt; route
   - Removed search functionality

2. <span class="highlight">templates/question.html</span>
   - Added Jinja2 filter for user submissions only
   - Updated section title to "Your Past Submissions"

3. <span class="highlight">templates/submissions.html</span>
   - Removed search form
   - Updated header to "Your Submissions"
   - Simplified filter structure

<span class="comment"># Result: Complete privacy protection with improved UX</span>
        </div>
    </div>
</body>
</html>
