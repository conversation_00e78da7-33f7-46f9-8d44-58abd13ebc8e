#!/usr/bin/env python3
"""
Interactive Grading Method Tester
Allows testing different prompts and rubrics against specific questions and parts
"""

import json
import os
import time
from datetime import datetime
from app import app
from models import db, Question, Part, Topic, Subject, MarkingPoint, Submission
import google.generativeai as genai
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Google's Gemini API
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

class GradingTimer:
    """Timer class for tracking grading performance"""
    def __init__(self):
        self.start_time = time.time()
        self.steps = []
        self.current_step_start = None
        self.current_step_name = None

    def start_step(self, step_name):
        if self.current_step_start is not None:
            self.end_current_step()
        
        self.current_step_name = step_name
        self.current_step_start = time.time()

    def end_current_step(self):
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None
            self.current_step_name = None

    def get_summary(self):
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

def get_available_questions():
    """Get all available chemistry questions (SAQ) with their parts"""
    with app.app_context():
        # Find chemistry subject
        chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
        if not chemistry_subject:
            return []

        # Get all chemistry questions (SAQ type)
        questions = db.session.query(Question).join(Topic).filter(
            Topic.subject_id == chemistry_subject.id,
            # Question.parts.any(Part.input_type == 'saq')
        ).all()

        question_data = []
        for question in questions:
            parts_data = []
            for part in question.parts:
                submission_count = Submission.query.filter_by(part_id=part.id).count()
                parts_data.append({
                    'id': part.id,
                    'description': part.description[:100] + "..." if len(part.description) > 100 else part.description,
                    'score': part.score,
                    'submission_count': submission_count,
                    'marking_points': len(part.marking_points)
                })

            # Include all chemistry SAQ questions regardless of submission count
            question_data.append({
                'id': question.id,
                'title': question.title,
                'parts': parts_data
            })

        return question_data

def get_sample_submissions(part_id, limit=5):
    """Get sample submissions for a specific part"""
    with app.app_context():
        submissions = Submission.query.filter_by(
            part_id=part_id
        ).filter(
            Submission.answer.isnot(None),
            Submission.answer != ''
        ).limit(limit).all()
        
        return [{
            'id': sub.id,
            'answer': sub.answer,
            'original_score': sub.score,
            'user_id': sub.user_id
        } for sub in submissions]

def test_current_method_with_custom_prompt(part_id, student_answer, custom_prompt_template):
    """Test current method with a custom prompt template"""
    with app.app_context():
        part = Part.query.get(part_id)
        if not part:
            return {'error': 'Part not found'}
        
        timer = GradingTimer()
        timer.start_step("Custom Current Method Test")
        
        try:
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part.marking_points]
            
            total_score = 0
            evaluated_points = []
            
            for mp_data in marking_points_data:
                # Use custom prompt template
                prompt = custom_prompt_template.format(
                    marking_point=mp_data['description'],
                    other_marking_points=', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']]),
                    student_answer=student_answer
                )
                
                # Call LLM
                generation_config = {
                    "temperature": 0.3, "top_p": 0.95, "top_k": 40, "max_output_tokens": 4096,
                }
                safety_settings = [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                ]
                gemini_model = genai.GenerativeModel('gemini-2.5-flash')
                response_text = gemini_model.generate_content(
                    prompt,
                    generation_config=generation_config,
                    safety_settings=safety_settings
                ).text.strip()
                
                # Process response
                response_upper = response_text.upper()
                is_correct = 'YES' in response_upper and not 'PARTIAL' in response_upper
                is_partial = 'PARTIAL' in response_upper
                
                # Extract evidence
                evidence = None
                if is_correct or is_partial:
                    if 'EVIDENCE:' in response_text:
                        evidence_parts = response_text.split('EVIDENCE:', 1)
                        if len(evidence_parts) > 1:
                            evidence = evidence_parts[1].strip()
                
                # Calculate score
                point_score = 0
                if is_correct:
                    point_score = mp_data['score']
                elif is_partial:
                    point_score = mp_data['score'] * 0.5
                
                total_score += point_score
                evaluated_points.append({
                    'description': mp_data['description'],
                    'achieved': is_correct,
                    'partial': is_partial,
                    'achieved_score': point_score,
                    'max_score': mp_data['score'],
                    'evidence': evidence,
                    'raw_response': response_text
                })
            
            timing_summary = timer.get_summary()
            return {
                'score': total_score,
                'max_score': part.score,
                'evaluated_points': evaluated_points,
                'timing': timing_summary,
                'success': True
            }
            
        except Exception as e:
            timing_summary = timer.get_summary()
            return {
                'error': str(e),
                'timing': timing_summary,
                'success': False
            }

def test_new_method_with_custom_rubric(part_description, student_answer, custom_rubric):
    """Test new method with a custom rubric"""
    timer = GradingTimer()
    timer.start_step("Custom New Method Test")
    
    try:
        prompt = f"""You are an objective grader. Your task is to evaluate a student's answer based on the provided examination question and the detailed objective rubric.

Strictly adhere to the rubric for scoring. Do not introduce external knowledge or subjective judgment.

---
Examination Question:
{part_description}

---
Objective Rubric:
{custom_rubric}

---
Student's Answer:
{student_answer}

---
Please provide your grading output in JSON format with two keys:
1. `score`: An integer representing the total score obtained by the student.
2. `comments`: A string containing the point breakdown for each criterion and a brief explanation for the points awarded or deducted.

Example JSON output:
{{
  "score": 5,
  "comments": "Criterion 1: 2/2 points (correct definition). Criterion 2: 3/3 points (identified all items)."
}}"""

        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(
            prompt,
            generation_config={
                'response_mime_type': 'application/json'
            }
        )
        
        result = json.loads(response.text)
        timing_summary = timer.get_summary()
        
        return {
            'score': result.get('score', 0),
            'comments': result.get('comments', ''),
            'timing': timing_summary,
            'success': True
        }
        
    except Exception as e:
        timing_summary = timer.get_summary()
        return {
            'error': str(e),
            'timing': timing_summary,
            'success': False
        }

def display_questions_menu():
    """Display available questions and parts"""
    questions = get_available_questions()
    if not questions:
        print("❌ No chemistry questions with submissions found.")
        return None, None
    
    print("\n📚 Available Chemistry Questions:")
    print("=" * 60)
    
    for i, question in enumerate(questions, 1):
        print(f"{i}. {question['title']} (ID: {question['id']})")
        for j, part in enumerate(question['parts'], 1):
            print(f"   {i}.{j} Part {part['id']}: {part['description']}")
            print(f"       Score: {part['score']}, Submissions: {part['submission_count']}, Marking Points: {part['marking_points']}")
    
    try:
        choice = input(f"\nSelect question (1-{len(questions)}): ").strip()
        question_idx = int(choice) - 1
        
        if 0 <= question_idx < len(questions):
            selected_question = questions[question_idx]
            
            if len(selected_question['parts']) == 1:
                return selected_question['id'], selected_question['parts'][0]['id']
            else:
                print(f"\nParts for '{selected_question['title']}':")
                for j, part in enumerate(selected_question['parts'], 1):
                    print(f"{j}. Part {part['id']}: {part['description']}")
                
                part_choice = input(f"Select part (1-{len(selected_question['parts'])}): ").strip()
                part_idx = int(part_choice) - 1
                
                if 0 <= part_idx < len(selected_question['parts']):
                    return selected_question['id'], selected_question['parts'][part_idx]['id']
        
        print("❌ Invalid selection.")
        return None, None
        
    except (ValueError, IndexError):
        print("❌ Invalid input.")
        return None, None

def main():
    """Main interactive testing program"""
    print("🧪 Interactive Grading Method Tester")
    print("=" * 50)
    print("Test different prompts and rubrics against specific questions and parts")
    
    while True:
        print("\n" + "=" * 50)
        
        # Step 1: Select question and part
        question_id, part_id = display_questions_menu()
        if not question_id or not part_id:
            continue
        
        # Step 2: Get sample submissions
        print(f"\n📝 Sample submissions for Part {part_id}:")
        submissions = get_sample_submissions(part_id, 5)
        
        if not submissions:
            print("❌ No submissions found for this part.")
            continue
        
        for i, sub in enumerate(submissions, 1):
            print(f"{i}. (ID: {sub['id']}, Original Score: {sub['original_score']})")
            print(f"   Answer: {sub['answer'][:150]}{'...' if len(sub['answer']) > 150 else ''}")
        
        # Step 3: Select submission or enter custom answer
        print(f"\nSelect submission (1-{len(submissions)}) or enter 'c' for custom answer:")
        choice = input("Choice: ").strip().lower()
        
        if choice == 'c':
            student_answer = input("\nEnter custom student answer: ").strip()
        else:
            try:
                sub_idx = int(choice) - 1
                if 0 <= sub_idx < len(submissions):
                    student_answer = submissions[sub_idx]['answer']
                    print(f"\nSelected answer: {student_answer[:200]}{'...' if len(student_answer) > 200 else ''}")
                else:
                    print("❌ Invalid selection.")
                    continue
            except ValueError:
                print("❌ Invalid input.")
                continue
        
        # Step 4: Test methods
        print("\n🔬 Testing Methods:")
        print("-" * 30)
        
        # Test current method
        print("\n1️⃣ Current Method Test")
        print("Enter custom prompt template (use {marking_point}, {other_marking_points}, {student_answer} as placeholders):")
        print("Or press Enter for default prompt:")
        
        custom_prompt = input().strip()
        if not custom_prompt:
            custom_prompt = """You are an expert examiner evaluating a student's answer against a specific marking point.
TASK:
1. Determine if the student's answer demonstrates understanding of the marking point
2. Classify the answer and provide structured feedback
3. For FULLY or PARTIALLY correct answers, identify the exact text from the student's answer that provides evidence

RESPONSE FORMAT:
You must respond in one of these formats ONLY:

Format 1 - If the marking point is FULLY addressed:
YES
EVIDENCE: <exact text from student's answer>

Format 2 - If the marking point was PARTIALLY addressed:
PARTIAL
EVIDENCE: <exact text from student's answer>

Format 3 - If the marking point is NOT addressed:
NO

MARKING POINT: {marking_point}
OTHER MARKING POINTS (exclude these from your evaluation): {other_marking_points}
STUDENT'S ANSWER: {student_answer}"""
        
        current_result = test_current_method_with_custom_prompt(part_id, student_answer, custom_prompt)
        
        print(f"\n📊 Current Method Results:")
        if current_result.get('success'):
            print(f"   Score: {current_result['score']}/{current_result['max_score']}")
            print(f"   Time: {current_result['timing']['total_duration_s']}s")
            print(f"   Marking Points:")
            for point in current_result['evaluated_points']:
                status = "✅" if point['achieved'] else "🔶" if point['partial'] else "❌"
                print(f"     {status} {point['description']}: {point['achieved_score']}/{point['max_score']}")
                if point['evidence']:
                    print(f"        Evidence: {point['evidence']}")
        else:
            print(f"   ❌ Error: {current_result.get('error', 'Unknown error')}")
        
        # Test new method
        print("\n2️⃣ New Method Test")
        print("Enter custom rubric:")
        
        lines = []
        print("(Enter your rubric, press Enter twice when done)")
        while True:
            line = input()
            if line == "" and lines and lines[-1] == "":
                break
            lines.append(line)
        
        custom_rubric = "\n".join(lines[:-1])  # Remove the last empty line
        
        if custom_rubric.strip():
            with app.app_context():
                part = Part.query.get(part_id)
                new_result = test_new_method_with_custom_rubric(part.description, student_answer, custom_rubric)
                
                print(f"\n📊 New Method Results:")
                if new_result.get('success'):
                    print(f"   Score: {new_result['score']}")
                    print(f"   Time: {new_result['timing']['total_duration_s']}s")
                    print(f"   Comments: {new_result['comments']}")
                else:
                    print(f"   ❌ Error: {new_result.get('error', 'Unknown error')}")
        else:
            print("   ⚠️ No rubric provided, skipping new method test.")
        
        # Continue or exit
        print("\n" + "=" * 50)
        continue_choice = input("Test another configuration? (y/n): ").strip().lower()
        if continue_choice != 'y':
            break
    
    print("\n👋 Thanks for using the Interactive Grading Tester!")

if __name__ == "__main__":
    main()
