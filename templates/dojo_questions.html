{% extends "base.html" %}

{% block title %}{{ topic.name }} - <PERSON><PERSON>{% endblock %}

{% block head %}
{{ super() }}
<style>
    .question-row {
        transition: all 0.2s ease;
    }

    .question-row:hover {
        background-color: #f8fafc;
        transform: translateX(4px);
    }

    .question-row.completed {
        background-color: #f0fdf4;
        border-left: 4px solid #10b981;
    }

    .question-row.attempted {
        background-color: #fffbeb;
        border-left: 4px solid #f59e0b;
    }

    .question-row.locked {
        opacity: 0.6;
        background-color: #f9fafb;
        border-left: 4px solid #d1d5db;
    }

    .progress-bar {
        height: 4px;
        background-color: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        transition: width 0.3s ease;
    }

    .topic-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .stats-card {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }

    .prerequisite-count {
        background-color: #e0e7ff;
        color: #3730a3;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Topic Header -->
    <div class="topic-header rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex justify-between items-start">
                <div class="text-white">
                    <h1 class="text-3xl font-bold tracking-tight mb-2">{{ topic.name }}</h1>
                    <p class="text-lg opacity-90">{{ topic.subject.name }} - Dojo Questions</p>
                </div>
                <a href="{{ url_for('dojo') }}" 
                   class="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dojo
                </a>
            </div>
        </div>
    </div>

    {% if dojo_questions %}
    <!-- Progress Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {% set total_questions = dojo_questions|length %}
        {% set completed_questions = user_progress.values()|selectattr('completed')|list|length %}
        {% set attempted_questions = user_progress.values()|selectattr('attempted')|list|length %}
        {% set remaining_questions = total_questions - attempted_questions %}

        <div class="stats-card rounded-lg p-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ total_questions }}</div>
                <div class="text-sm text-gray-600">Total Questions</div>
            </div>
        </div>
        <div class="stats-card rounded-lg p-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ completed_questions }}</div>
                <div class="text-sm text-gray-600">Completed</div>
            </div>
        </div>
        <div class="stats-card rounded-lg p-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ attempted_questions - completed_questions }}</div>
                <div class="text-sm text-gray-600">In Progress</div>
            </div>
        </div>
        <div class="stats-card rounded-lg p-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-600">{{ remaining_questions }}</div>
                <div class="text-sm text-gray-600">Not Started</div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Questions</h2>
        </div>

        <div class="divide-y divide-gray-200">
            {% for question in dojo_questions %}
            {% set progress = user_progress.get(question.id, {'attempted': False, 'completed': False, 'percentage': 0, 'best_score': 0, 'total_possible': 0}) %}
            {% set prereq_data = prerequisite_data.get(question.id, {'prerequisites': [], 'prerequisites_met': True}) %}

            <div class="question-row p-6
                        {% if progress.completed %}completed{% elif progress.attempted %}attempted{% elif not prereq_data.prerequisites_met %}locked{% endif %}">

                <div class="flex items-center justify-between">
                    <!-- Question Info -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center justify-center h-10 w-10 rounded-full
                                           {% if progress.completed %}bg-green-100 text-green-600{% elif progress.attempted %}bg-yellow-100 text-yellow-600{% elif not prereq_data.prerequisites_met %}bg-gray-100 text-gray-400{% else %}bg-indigo-100 text-indigo-600{% endif %}">
                                    {% if progress.completed %}
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    {% elif progress.attempted %}
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    {% elif not prereq_data.prerequisites_met %}
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                    {% else %}
                                        <span class="text-sm font-medium">{{ question.id }}</span>
                                    {% endif %}
                                </span>
                            </div>

                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-medium text-gray-900 truncate">{{ question.title }}</h3>
                                <div class="flex items-center space-x-4 mt-1">
                                    <span class="text-sm text-gray-500">{{ progress.total_possible }} points</span>
                                    {% if question.source %}
                                    <span class="status-badge bg-gray-100 text-gray-700">{{ question.source }}</span>
                                    {% endif %}
                                    {% if prereq_data.prerequisites %}
                                    <span class="status-badge prerequisite-count">{{ prereq_data.prerequisites|length }} prerequisites</span>
                                    {% endif %}
                                </div>

                                <!-- Progress Bar -->
                                {% if progress.attempted %}
                                <div class="mt-2">
                                    <div class="progress-bar">
                                        <div class="progress-fill
                                                   {% if progress.completed %}bg-green-500{% else %}bg-yellow-500{% endif %}"
                                             style="width: {{ progress.percentage }}%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>Best: {{ progress.best_score }}/{{ progress.total_possible }}</span>
                                        <span>{{ "%.0f"|format(progress.percentage) }}%</span>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="flex-shrink-0 ml-6">
                        {% if prereq_data.prerequisites_met %}
                        <a href="{{ url_for('load_question', question_id=question.id) }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white
                                  {% if progress.completed %}bg-green-600 hover:bg-green-700{% elif progress.attempted %}bg-yellow-600 hover:bg-yellow-700{% else %}bg-indigo-600 hover:bg-indigo-700{% endif %}">
                            {% if progress.completed %}
                                Review
                            {% elif progress.attempted %}
                                Continue
                            {% else %}
                                Start
                            {% endif %}
                        </a>
                        {% else %}
                        <button disabled
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Locked
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    {% else %}
    <!-- No Questions Available -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No dojo questions available</h3>
        <p class="mt-1 text-sm text-gray-500">
            There are no dojo questions configured for {{ topic.name }} yet.
        </p>
    </div>
    {% endif %}
</div>
{% endblock %}
