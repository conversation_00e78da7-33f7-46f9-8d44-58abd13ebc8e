{% extends "base.html" %}

{% block title %}Time Tracking Test{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">🕒 Time Tracking Test</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>Multi-Tab Time Tracking Test</h5>
                        <p>This page helps you test the new robust time tracking system:</p>
                        <ol>
                            <li><strong>Open multiple tabs</strong> of this page</li>
                            <li><strong>Switch between tabs</strong> and watch the console logs</li>
                            <li><strong>Check the status indicators</strong> below</li>
                            <li><strong>Only one tab should be active</strong> at a time</li>
                        </ol>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Tab Status</h6>
                                    <div id="tab-status" class="badge badge-secondary">Initializing...</div>
                                    <br><br>
                                    <small><strong>Tab ID:</strong> <span id="tab-id">Loading...</span></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Time Tracking</h6>
                                    <div><strong>Current Time:</strong> <span id="current-time">0</span> seconds</div>
                                    <div><strong>Last Update:</strong> <span id="last-update">Never</span></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Activity Log</h6>
                        <div id="activity-log" style="height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;">
                            <div class="text-muted">Activity will appear here...</div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="logCurrentStatus()">Log Current Status</button>
                        <button class="btn btn-secondary" onclick="clearLog()">Clear Log</button>
                        <button class="btn btn-info" onclick="simulateActivity()">Simulate Activity</button>
                    </div>

                    <div class="mt-4">
                        <h6>Instructions for Testing</h6>
                        <ul class="small">
                            <li>Open your browser's developer console (F12) to see detailed logs</li>
                            <li>Open this page in multiple tabs</li>
                            <li>Switch between tabs and observe which one becomes active</li>
                            <li>Try minimizing the browser window or switching to other applications</li>
                            <li>The active tab should automatically switch when you change focus</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test page specific JavaScript
let activityLog = [];

function addToLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    activityLog.push(logEntry);
    
    const logElement = document.getElementById('activity-log');
    const logDiv = document.createElement('div');
    logDiv.textContent = logEntry;
    logDiv.className = 'small';
    logElement.appendChild(logDiv);
    
    // Keep only last 50 entries
    if (activityLog.length > 50) {
        activityLog.shift();
        logElement.removeChild(logElement.firstChild);
    }
    
    // Scroll to bottom
    logElement.scrollTop = logElement.scrollHeight;
}

function updateStatus() {
    if (window.timeTracker) {
        const isActive = window.timeTracker.isActive;
        const tabId = window.timeTracker.tabId;
        const currentTime = window.timeTracker.getCurrentTime();
        
        // Update status indicators
        const statusElement = document.getElementById('tab-status');
        const tabIdElement = document.getElementById('tab-id');
        const currentTimeElement = document.getElementById('current-time');
        
        if (isActive) {
            statusElement.textContent = 'ACTIVE';
            statusElement.className = 'badge badge-success';
        } else {
            statusElement.textContent = 'INACTIVE';
            statusElement.className = 'badge badge-secondary';
        }
        
        tabIdElement.textContent = tabId.substring(0, 12) + '...';
        currentTimeElement.textContent = currentTime;
    }
}

function logCurrentStatus() {
    if (window.timeTracker) {
        const status = {
            isActive: window.timeTracker.isActive,
            tabId: window.timeTracker.tabId,
            currentTime: window.timeTracker.getCurrentTime(),
            isVisible: window.timeTracker.isVisible,
            isIdle: window.timeTracker.isIdle
        };
        addToLog(`Status: ${JSON.stringify(status)}`);
    } else {
        addToLog('Time tracker not initialized');
    }
}

function clearLog() {
    activityLog = [];
    document.getElementById('activity-log').innerHTML = '<div class="text-muted">Activity log cleared...</div>';
}

function simulateActivity() {
    addToLog('Simulated user activity');
    if (window.timeTracker) {
        window.timeTracker.recordActivity();
    }
}

// Set up monitoring when page loads
document.addEventListener('DOMContentLoaded', function() {
    addToLog('Test page loaded');
    
    // Update status every second
    setInterval(updateStatus, 1000);
    
    // Monitor time tracker initialization
    const checkTracker = setInterval(() => {
        if (window.timeTracker) {
            addToLog('Time tracker initialized');
            
            // Override the onActiveChange callback to log changes
            const originalCallback = window.timeTracker.onActiveChange;
            window.timeTracker.onActiveChange = function(isActive) {
                addToLog(`Tab became ${isActive ? 'ACTIVE' : 'INACTIVE'}`);
                if (originalCallback) originalCallback(isActive);
            };
            
            clearInterval(checkTracker);
        }
    }, 100);
});
</script>
{% endblock %}
