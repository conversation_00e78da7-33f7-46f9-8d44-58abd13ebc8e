{% extends "base.html" %}

{% block title %}Manage Notes Relevance{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Manage Notes Relevance</h1>
        <div class="flex space-x-3">
            <a href="{{ url_for('populate_notes_relevance') }}"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                Auto-Populate with RAG
            </a>
            <a href="{{ url_for('admin') }}"
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <select name="subject_id" id="subject_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">All Subjects</option>
                    {% for subject in subjects %}
                    <option value="{{ subject.id }}" {% if current_subject_id == subject.id %}selected{% endif %}>
                        {{ subject.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="topic_id" class="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                <select name="topic_id" id="topic_id" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">All Topics</option>
                    {% for topic in topics %}
                    <option value="{{ topic.id }}" {% if current_topic_id == topic.id %}selected{% endif %}>
                        {{ topic.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" name="search" id="search" value="{{ current_search or '' }}" 
                       placeholder="Search questions..." 
                       class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Questions List -->
    <div class="space-y-6">
        {% for question in questions %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            Question {{ question.id }}: {{ question.title }}
                        </h3>
                        <div class="text-sm text-gray-600 mb-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                                {{ question.subject.name if question.subject else 'No Subject' }}
                            </span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                {{ question.topic.name if question.topic else 'No Topic' }}
                            </span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                {{ 'Dojo' if question.is_dojo else 'Vault' }}
                            </span>
                        </div>
                        <p class="text-sm text-gray-700">{{ question.description[:200] }}{% if question.description and question.description|length > 200 %}...{% endif %}</p>
                    </div>
                </div>

                <!-- Current Notes Relevance -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Current Relevant Notes ({{ question.relevant_notes|length }})</h4>
                    {% if question.relevant_notes %}
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {% for relevance in question.relevant_notes %}
                        {% set chunk = relevance.notes_chunk %}
                        <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                            <div class="flex justify-between items-start mb-2">
                                <h5 class="text-sm font-medium text-gray-900">{{ chunk.title }}</h5>
                                <button onclick="removeNotesRelevance({{ question.id }}, {{ relevance.id }})" 
                                        class="text-red-600 hover:text-red-800 text-xs">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="text-xs text-gray-600 mb-2">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-1">
                                    {{ chunk.filename.replace('.md', '') }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                    {{ relevance.relevance_type.replace('_', ' ').title() }}
                                </span>
                                <div class="flex items-center">
                                    {% for i in range(relevance.strength) %}
                                    <span class="text-yellow-400 text-xs">★</span>
                                    {% endfor %}
                                    {% for i in range(5 - relevance.strength) %}
                                    <span class="text-gray-300 text-xs">★</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-sm text-gray-500 italic">No relevant notes linked yet.</p>
                    {% endif %}
                </div>

                <!-- Add New Relevance -->
                <div class="border-t border-gray-200 pt-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Add Relevant Notes</h4>
                    <form onsubmit="addNotesRelevance(event, {{ question.id }})" class="grid grid-cols-1 md:grid-cols-5 gap-3">
                        <div class="md:col-span-2">
                            <select name="notes_chunk_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="">Select notes section...</option>
                                {% for chunk in notes_chunks %}
                                <option value="{{ chunk.id }}">
                                    {{ chunk.filename.replace('.md', '') }} - {{ chunk.title[:50] }}{% if chunk.title|length > 50 %}...{% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <select name="relevance_type" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="concept_explanation">Concept Explanation</option>
                                <option value="worked_example">Worked Example</option>
                                <option value="related_topic">Related Topic</option>
                                <option value="background_knowledge">Background Knowledge</option>
                                <option value="general">General</option>
                            </select>
                        </div>
                        <div>
                            <select name="strength" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option value="5">★★★★★ (Essential)</option>
                                <option value="4">★★★★☆ (Important)</option>
                                <option value="3">★★★☆☆ (Helpful)</option>
                                <option value="2">★★☆☆☆ (Related)</option>
                                <option value="1">★☆☆☆☆ (Tangential)</option>
                            </select>
                        </div>
                        <div>
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm transition-colors">
                                Add
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if not questions %}
    <div class="text-center py-12">
        <div class="text-gray-400 mb-4">
            <i class="fas fa-search text-4xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No questions found</h3>
        <p class="text-gray-600">Try adjusting your filters or search terms.</p>
    </div>
    {% endif %}
</div>

<script>
async function addNotesRelevance(event, questionId) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    try {
        const response = await fetch(`/admin/api/add_notes_relevance/${questionId}`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            location.reload();
        } else {
            const error = await response.text();
            alert('Error adding relevance: ' + error);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}

async function removeNotesRelevance(questionId, relevanceId) {
    if (!confirm('Are you sure you want to remove this notes relevance?')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/remove_notes_relevance/${relevanceId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            location.reload();
        } else {
            const error = await response.text();
            alert('Error removing relevance: ' + error);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}
</script>
{% endblock %}
