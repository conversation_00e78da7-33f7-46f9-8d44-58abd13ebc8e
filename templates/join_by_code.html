{% extends "base.html" %}

{% block title %}Join Group by Code{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center">
                <span class="text-2xl">🎯</span>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Join a Group
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Enter the invite code provided by your teacher
            </p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="space-y-2">
                    {% for category, message in messages %}
                        <div class="rounded-md p-4 {% if category == 'error' %}bg-red-50 border border-red-200{% elif category == 'success' %}bg-green-50 border border-green-200{% elif category == 'info' %}bg-blue-50 border border-blue-200{% else %}bg-yellow-50 border border-yellow-200{% endif %}">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    {% if category == 'error' %}
                                        <span class="text-red-400">❌</span>
                                    {% elif category == 'success' %}
                                        <span class="text-green-400">✅</span>
                                    {% elif category == 'info' %}
                                        <span class="text-blue-400">ℹ️</span>
                                    {% else %}
                                        <span class="text-yellow-400">⚠️</span>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm {% if category == 'error' %}text-red-800{% elif category == 'success' %}text-green-800{% elif category == 'info' %}text-blue-800{% else %}text-yellow-800{% endif %}">
                                        {{ message }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <form class="mt-8 space-y-6" method="POST">
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="invite_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Invite Code
                    </label>
                    <input 
                        id="invite_code" 
                        name="invite_code" 
                        type="text" 
                        required 
                        class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm uppercase tracking-wider font-mono"
                        placeholder="Enter 8-character code"
                        maxlength="8"
                        value="{{ invite_code or '' }}"
                        style="text-transform: uppercase;"
                    >
                    <p class="mt-1 text-xs text-gray-500">
                        Enter the 8-character code provided by your teacher (e.g., ABC123XY)
                    </p>
                </div>
            </div>

            <div>
                <button 
                    type="submit" 
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                >
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <span class="text-indigo-500 group-hover:text-indigo-400">
                            🚀
                        </span>
                    </span>
                    Join Group
                </button>
            </div>

            <div class="text-center">
                <a href="{{ url_for('groups') }}" class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                    ← Back to Groups
                </a>
            </div>
        </form>

        <!-- Help Section -->
        <div class="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Need help?</h3>
            <ul class="text-xs text-gray-600 space-y-1">
                <li>• Ask your teacher for the invite code</li>
                <li>• Make sure you enter the code exactly as provided</li>
                <li>• Codes are case-insensitive but must be 8 characters</li>
                <li>• Contact your teacher if the code doesn't work</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const inviteCodeInput = document.getElementById('invite_code');
    
    // Auto-uppercase input
    inviteCodeInput.addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });
    
    // Focus on the input field
    inviteCodeInput.focus();
});
</script>
{% endblock %}
