{% extends "base.html" %}

{% block content %}
<style>
/* Custom styling for confidence sliders */
.confidence-slider {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #4f46e5 0%, #4f46e5 50%, #e5e7eb 50%, #e5e7eb 100%);
    outline: none;
}

.confidence-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.confidence-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Hover effects for cards */
.grade-card:hover, .subject-card:hover {
    transform: translateY(-2px);
}

/* Selected state animations */
.grade-card, .subject-card {
    transition: all 0.2s ease-in-out;
}
</style>
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            {% if is_restart %}
                <h1 class="text-4xl font-bold tracking-tight text-gray-900 mb-2">Update Your Preferences</h1>
                <p class="text-lg text-gray-600">Let's update your subject and level preferences</p>
            {% else %}
                <h1 class="text-4xl font-bold tracking-tight text-gray-900 mb-2">Welcome to Vast!</h1>
                <p class="text-lg text-gray-600">Let's personalize your learning experience</p>
            {% endif %}
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-center">
                <div class="flex items-center space-x-4">
                    <!-- Step 1: Grade Level -->
                    <div class="flex items-center">
                        <div id="step1-circle" class="w-10 h-10 rounded-full bg-indigo-600 text-white flex items-center justify-center font-semibold text-sm">
                            1
                        </div>
                        <!-- <span class="ml-2 text-sm font-medium text-gray-900">Grade Level</span> -->
                    </div>

                    <!-- Connector -->
                    <div id="connector1" class="w-16 h-1 bg-gray-200 rounded"></div>

                    <!-- Step 2: Chemistry Topics -->
                    <div class="flex items-center">
                        <div id="step2-circle" class="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-semibold text-sm">
                            2
                        </div>
                        <!-- <span class="ml-2 text-sm font-medium text-gray-500"></span> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Onboarding Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <form id="onboarding-form" method="POST" action="{{ url_for('complete_onboarding') }}">

                <!-- Step 1: Grade Level Selection -->
                <div id="step1" class="step-content">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">What's your current grade level?</h2>
                        <p class="text-gray-600">We're currently piloting chemistry for JC students</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                        <!-- J1 Option -->
                        <label class="grade-option cursor-pointer">
                            <input type="radio" name="grade_level" value="J1" class="sr-only grade-radio">
                            <div class="grade-card border-2 border-gray-200 rounded-xl p-8 text-center hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                                <div class="text-5xl mb-4">🎓</div>
                                <h3 class="text-2xl font-semibold text-gray-900 mb-2">J1</h3>
                                <p class="text-gray-600">Junior College Year 1</p>
                                <!-- <p class="text-sm text-indigo-600 mt-2">Topics 1-10 available</p> -->
                            </div>
                        </label>

                        <!-- J2 Option -->
                        <label class="grade-option cursor-pointer">
                            <input type="radio" name="grade_level" value="J2" class="sr-only grade-radio">
                            <div class="grade-card border-2 border-gray-200 rounded-xl p-8 text-center hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                                <div class="text-5xl mb-4">🏆</div>
                                <h3 class="text-2xl font-semibold text-gray-900 mb-2">J2</h3>
                                <p class="text-gray-600">Junior College Year 2</p>
                                <!-- <p class="text-sm text-indigo-600 mt-2">All topics available</p> -->
                            </div>
                        </label>
                    </div>

                    <div class="flex justify-end mt-8">
                        <button type="button" id="step1-next" class="px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Next: Choose Topics
                        </button>
                    </div>
                </div>

                <!-- Step 2: Chemistry Topics Selection -->
                <div id="step2" class="step-content hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Which chemistry topics do you struggle with?</h2>
                        <p class="text-gray-600">Select the topics you'd like extra help with (you can select multiple)</p>
                    </div>

                    <div id="topics-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
                        <!-- Chemistry topics will be populated by JavaScript based on grade level -->
                    </div>

                    <div class="flex justify-between mt-8">
                        <button type="button" id="step2-back" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition-colors duration-200">
                            Back
                        </button>
                        <button type="submit" id="complete-onboarding" class="px-6 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200">
                            Complete Setup
                        </button>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="chemistry_topics_struggle" id="chemistry_topics_struggle_input">
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    let selectedGrade = '';
    let selectedTopics = [];

    // Chemistry topics based on grade level (from chemistry_notes_markdown)
    const chemistryTopics = {
        'J1': [
            { id: '1a', name: 'Mole Concept and Stoichiometry', icon: '⚖️' },
            { id: '1b', name: 'Redox Reactions', icon: '🔄' },
            { id: '2', name: 'Atomic Structure', icon: '⚛️' },
            { id: '3', name: 'Chemical Bonding I', icon: '🔗' },
            { id: '4', name: 'Gaseous State', icon: '💨' },
            { id: '5a', name: 'Chemical Energetics I', icon: '🔥' },
            { id: '6', name: 'Reaction Kinetics', icon: '⚡' },
            { id: '7', name: 'Chemical Equilibria', icon: '⚖️' },
            { id: '8', name: 'Chemical Bonding II', icon: '🔗' },
            { id: '9', name: 'Organic Chemistry', icon: '🧪' }
        ],
        'J2': [
            { id: '1a', name: 'Mole Concept and Stoichiometry', icon: '⚖️' },
            { id: '1b', name: 'Redox Reactions', icon: '🔄' },
            { id: '2', name: 'Atomic Structure', icon: '⚛️' },
            { id: '3', name: 'Chemical Bonding I', icon: '🔗' },
            { id: '4', name: 'Gaseous State', icon: '💨' },
            { id: '5a', name: 'Chemical Energetics I', icon: '🔥' },
            { id: '5b', name: 'Chemical Energetics II', icon: '🔥' },
            { id: '6', name: 'Reaction Kinetics', icon: '⚡' },
            { id: '7', name: 'Chemical Equilibria', icon: '⚖️' },
            { id: '8', name: 'Chemical Bonding II', icon: '🔗' },
            { id: '9', name: 'Organic Chemistry', icon: '🧪' },
            { id: '10', name: 'Alkanes', icon: '⛽' },
            { id: '11', name: 'Alkenes', icon: '🔗' },
            { id: '12', name: 'The Periodic Table I', icon: '📊' },
            { id: '13', name: 'The Periodic Table II', icon: '📊' },
            { id: '14', name: 'Arenes', icon: '💍' },
            { id: '15', name: 'Acid-Base Equilibria', icon: '🧪' },
            { id: '16', name: 'Solubility Equilibria', icon: '💧' },
            { id: '17', name: 'Halogen Derivatives', icon: '🧂' },
            { id: '18', name: 'Hydroxy Compounds', icon: '🍷' },
            { id: '19', name: 'Carbonyl Compounds', icon: '🧪' },
            { id: '20', name: 'Carboxylic Acids and Derivatives', icon: '🧪' },
            { id: '21', name: 'Nitrogen Compounds', icon: '🧪' },
            { id: '22a', name: 'Electrochemistry I', icon: '🔋' },
            { id: '22b', name: 'Electrochemistry II', icon: '🔋' },
            { id: '23', name: 'Transition Elements', icon: '🌈' }
        ]
    };

    // Grade selection handlers
    document.querySelectorAll('.grade-radio').forEach(radio => {
        radio.addEventListener('change', function() {
            selectedGrade = this.value;

            // Update UI
            document.querySelectorAll('.grade-card').forEach(card => {
                card.classList.remove('border-indigo-500', 'bg-indigo-50');
                card.classList.add('border-gray-200');
            });

            this.parentElement.querySelector('.grade-card').classList.remove('border-gray-200');
            this.parentElement.querySelector('.grade-card').classList.add('border-indigo-500', 'bg-indigo-50');

            // Enable next button
            const nextBtn = document.getElementById('step1-next');
            if (nextBtn) {
                nextBtn.disabled = false;
            }
        });
    });

    // Step navigation
    const step1NextBtn = document.getElementById('step1-next');
    if (step1NextBtn) {
        step1NextBtn.addEventListener('click', function() {
            if (selectedGrade) {
                showStep(2);
                populateTopics();
            }
        });
    }

    const step2BackBtn = document.getElementById('step2-back');
    if (step2BackBtn) {
        step2BackBtn.addEventListener('click', function() {
            showStep(1);
        });
    }

    function showStep(step) {
        // Hide all steps
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show current step
        document.getElementById(`step${step}`).classList.remove('hidden');

        // Update progress indicators
        updateProgressIndicators(step);
        currentStep = step;
    }

    function updateProgressIndicators(step) {
        for (let i = 1; i <= 2; i++) {
            const circle = document.getElementById(`step${i}-circle`);
            if (!circle) continue; // Skip if element doesn't exist

            const text = circle.parentElement.querySelector('span:not([style*="display: none"])');

            if (i < step) {
                // Completed step
                circle.className = 'w-10 h-10 rounded-full bg-green-500 text-white flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = '✓';
                if (text) text.className = 'ml-2 text-sm font-medium text-green-600';
            } else if (i === step) {
                // Current step
                circle.className = 'w-10 h-10 rounded-full bg-indigo-600 text-white flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = i;
                if (text) text.className = 'ml-2 text-sm font-medium text-gray-900';
            } else {
                // Future step
                circle.className = 'w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = i;
                if (text) text.className = 'ml-2 text-sm font-medium text-gray-500';
            }
        }

        // Update connectors
        const connector = document.getElementById('connector1');
        if (connector) {
            if (step > 1) {
                connector.className = 'w-16 h-1 bg-green-500 rounded';
            } else {
                connector.className = 'w-16 h-1 bg-gray-200 rounded';
            }
        }
    }

    function populateTopics() {
        const container = document.getElementById('topics-container');
        container.innerHTML = '';

        const topics = chemistryTopics[selectedGrade] || [];

        topics.forEach(topic => {
            const topicCard = document.createElement('label');
            topicCard.className = 'topic-option cursor-pointer';
            topicCard.innerHTML = `
                <input type="checkbox" value="${topic.id}" class="sr-only topic-checkbox">
                <div class="topic-card border-2 border-gray-200 rounded-xl p-4 text-center hover:border-indigo-300 hover:shadow-md transition-all duration-200">
                    <div class="text-3xl mb-2">${topic.icon}</div>
                    <h3 class="text-sm font-semibold text-gray-900">${topic.name}</h3>
                    <p class="text-xs text-gray-500 mt-1">Topic ${topic.id}</p>
                </div>
            `;

            const checkbox = topicCard.querySelector('.topic-checkbox');
            checkbox.addEventListener('change', function() {
                const card = this.parentElement.querySelector('.topic-card');

                if (this.checked) {
                    selectedTopics.push(this.value);
                    card.classList.remove('border-gray-200');
                    card.classList.add('border-indigo-500', 'bg-indigo-50');
                } else {
                    selectedTopics = selectedTopics.filter(t => t !== this.value);
                    card.classList.remove('border-indigo-500', 'bg-indigo-50');
                    card.classList.add('border-gray-200');
                }
            });

            container.appendChild(topicCard);
        });
    }

    // Form submission
    const onboardingForm = document.getElementById('onboarding-form');
    if (onboardingForm) {
        onboardingForm.addEventListener('submit', function(e) {
            // Populate hidden inputs
            const topicsInput = document.getElementById('chemistry_topics_struggle_input');
            if (topicsInput) {
                topicsInput.value = JSON.stringify(selectedTopics);
            }
        });
    }
});
</script>
{% endblock %}
