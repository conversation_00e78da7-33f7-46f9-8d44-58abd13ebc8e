{% extends "base.html" %}

{% block title %}My Clarifications{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">My Clarifications</h1>
        <p class="mt-2 text-gray-600">View and track your clarification requests to teachers.</p>
    </div>

    {% if clarifications %}
        <div class="space-y-6">
            {% for clarification in clarifications %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900">{{ clarification.subject }}</h3>
                            <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                                <span>
                                    <i class="fas fa-book mr-1"></i>
                                    Question #{{ clarification.question_id }}
                                    {% if clarification.part %}
                                        - Part {{ clarification.part_id }}
                                    {% endif %}
                                </span>
                                <span>
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ clarification.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                </span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if clarification.status == 'pending' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>Pending
                                </span>
                            {% elif clarification.status == 'answered' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Answered
                                </span>
                            {% elif clarification.status == 'closed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times mr-1"></i>Closed
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="border-l-4 border-blue-400 pl-4 mb-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Your Question:</h4>
                        <p class="text-gray-700 whitespace-pre-wrap">{{ clarification.message }}</p>
                    </div>

                    {% if clarification.context_data %}
                        {% set context = clarification.context_data | from_json %}
                        {% if context.part_specific and context.user_answer %}
                            <div class="border-l-4 border-gray-300 pl-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Your Submitted Answer (Part {{ clarification.part_id }}):</h4>
                                <p class="text-gray-700 whitespace-pre-wrap bg-gray-50 p-3 rounded">{{ context.user_answer }}</p>
                                <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                                    {% if context.score is not none %}
                                        <span>Score: {{ context.score }}</span>
                                    {% endif %}
                                    <span>Submitted: {{ context.submission_time }}</span>
                                </div>
                            </div>
                        {% elif not context.part_specific and context.all_submissions %}
                            <div class="border-l-4 border-gray-300 pl-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Your Recent Submissions ({{ context.total_submissions }} total):</h4>
                                <div class="space-y-2">
                                    {% for submission in context.all_submissions %}
                                        <div class="bg-gray-50 p-3 rounded">
                                            <div class="flex justify-between items-start mb-2">
                                                <span class="text-xs font-medium text-gray-600">Part {{ submission.part_id }}</span>
                                                {% if submission.score is not none %}
                                                    <span class="text-xs text-gray-500">Score: {{ submission.score }}</span>
                                                {% endif %}
                                            </div>
                                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ submission.answer }}</p>
                                            <p class="text-xs text-gray-500 mt-1">{{ submission.submission_time }}</p>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}

                    {% if clarification.teacher_response %}
                        <div class="border-l-4 border-green-400 pl-4 mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">
                                Teacher Response:
                                {% if clarification.teacher %}
                                    <span class="text-sm font-normal text-gray-600">by {{ clarification.teacher.username }}</span>
                                {% endif %}
                            </h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ clarification.teacher_response }}</p>
                            <p class="text-xs text-gray-500 mt-2">
                                Responded on {{ clarification.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </p>
                        </div>
                    {% endif %}

                    <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                        <a href="{{ url_for('load_question', question_id=clarification.question_id) }}"
                           class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            View Question
                        </a>
                        
                        {% if clarification.status != 'closed' %}
                            <button onclick="closeClarification({{ clarification.id }})"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <i class="fas fa-times mr-1"></i>Close
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <i class="fas fa-question-circle text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No clarifications yet</h3>
            <p class="text-gray-600 mb-6">You haven't sent any clarification requests to teachers.</p>
            <a href="{{ url_for('dojo') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-book mr-2"></i>Browse Questions
            </a>
        </div>
    {% endif %}
</div>

<script>
function closeClarification(clarificationId) {
    if (!confirm('Are you sure you want to close this clarification? This action cannot be undone.')) {
        return;
    }

    fetch(`/api/clarifications/${clarificationId}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to close clarification. Please try again.');
    });
}
</script>
{% endblock %}
