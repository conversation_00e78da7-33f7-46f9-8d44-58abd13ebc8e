{% extends 'base.html' %}

{% block title %}Manage Prerequisites - {{ question.title }}{% endblock %}

{% block head %}
{{ super() }}
<style>
    .prerequisite-card {
        transition: all 0.2s ease-in-out;
        border: 2px solid transparent;
    }
    
    .prerequisite-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .prerequisite-card.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    
    .question-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .topic-badge {
        background-color: #f3f4f6;
        color: #374151;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="question-info rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex justify-between items-start">
                <div class="text-white">
                    <h1 class="text-3xl font-bold tracking-tight mb-2">Manage Prerequisites</h1>
                    <h2 class="text-xl font-medium mb-2">{{ question.title }}</h2>
                    <div class="flex items-center space-x-4">
                        <span class="topic-badge">{{ question.topic.subject.name }}</span>
                        <span class="topic-badge">{{ question.topic.name }}</span>
                        <span class="topic-badge">ID: {{ question.id }}</span>
                    </div>
                </div>
                <a href="{{ url_for('admin_dojo') }}" 
                   class="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dojo Management
                </a>
            </div>
        </div>
    </div>

    <!-- Prerequisites Form -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
        <div class="p-6">
            <form method="POST" action="{{ url_for('manage_prerequisites', question_id=question.id) }}">
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        Select Prerequisites for "{{ question.title }}"
                    </h3>
                    <p class="text-sm text-gray-600 mb-6">
                        Choose which dojo questions students must complete before attempting this question. 
                        Only dojo questions from the same subject ({{ question.topic.subject.name }}) are shown.
                    </p>
                </div>

                {% if potential_prerequisites %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {% for prereq in potential_prerequisites %}
                    <div class="prerequisite-card bg-white border border-gray-200 rounded-lg p-4 cursor-pointer"
                         onclick="togglePrerequisite({{ prereq.id }})">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <input type="checkbox" 
                                           name="prerequisites" 
                                           value="{{ prereq.id }}" 
                                           id="prereq-{{ prereq.id }}"
                                           {% if prereq.id in current_prerequisites %}checked{% endif %}
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="prereq-{{ prereq.id }}" class="ml-2 text-sm font-medium text-gray-900 cursor-pointer">
                                        Question {{ prereq.id }}
                                    </label>
                                </div>
                                <h4 class="text-sm font-medium text-gray-900 mb-2">{{ prereq.title }}</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ prereq.topic.name }}
                                    </span>
                                    {% if prereq.source %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ prereq.source }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        {% if prereq.description %}
                        <div class="mt-3 text-xs text-gray-600 line-clamp-2">
                            {{ prereq.description[:100] }}{% if prereq.description|length > 100 %}...{% endif %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>

                <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-600">
                        <span id="selected-count">{{ current_prerequisites|length }}</span> prerequisite(s) selected
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" 
                                onclick="clearAllPrerequisites()"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Clear All
                        </button>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Save Prerequisites
                        </button>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No potential prerequisites found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        There are no other dojo questions in {{ question.topic.subject.name }} that can serve as prerequisites.
                    </p>
                    <div class="mt-6">
                        <a href="{{ url_for('admin_dojo') }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                            Back to Dojo Management
                        </a>
                    </div>
                </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>

<script>
function togglePrerequisite(questionId) {
    const checkbox = document.getElementById(`prereq-${questionId}`);
    const card = checkbox.closest('.prerequisite-card');
    
    checkbox.checked = !checkbox.checked;
    
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }
    
    updateSelectedCount();
}

function clearAllPrerequisites() {
    const checkboxes = document.querySelectorAll('input[name="prerequisites"]');
    const cards = document.querySelectorAll('.prerequisite-card');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    cards.forEach(card => {
        card.classList.remove('selected');
    });
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const checkedBoxes = document.querySelectorAll('input[name="prerequisites"]:checked');
    document.getElementById('selected-count').textContent = checkedBoxes.length;
}

// Initialize card states on page load
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[name="prerequisites"]');
    
    checkboxes.forEach(checkbox => {
        const card = checkbox.closest('.prerequisite-card');
        if (checkbox.checked) {
            card.classList.add('selected');
        }
        
        // Prevent checkbox click from triggering card click
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
            updateSelectedCount();
        });
    });
    
    updateSelectedCount();
});
</script>
{% endblock %}
