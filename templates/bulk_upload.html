{% extends 'base.html' %}

{% block title %}Bulk Upload Questions{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl">
        <!-- Header -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center">Bulk Upload Questions</h1>
                <p class="mt-2 text-gray-600 text-center">Upload multiple questions using CSV, Excel files or convert from PDF</p>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <form id="uploadForm" method="POST" enctype="multipart/form-data" class="space-y-6">
                    <!-- Subject Selection -->
                    <div>
                        <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">Subject</label>
                        <div class="mt-2">
                            <select id="subject" name="subject_id" onchange="loadTopics(this.value)" required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                <option selected disabled>Select Subject</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}">{{ subject.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>


                    <!-- Upload Method Tabs -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                            <a href="#" id="tabData" onclick="switchTab('data')"
                               class="border-indigo-500 text-indigo-600 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium">
                                CSV/Excel Upload
                            </a>
                            <a href="#" id="tabPdf" onclick="switchTab('pdf')"
                               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium">
                                PDF OCR
                            </a>
                        </nav>
                    </div>

                    <!-- Data File Upload Section -->
                    <div id="dataUploadSection">
                        <div class="space-y-4">
                            <div class="p-4 bg-green-50 rounded-md">
                                <h3 class="text-md font-medium text-green-800 mb-2">CSV/Excel Upload</h3>
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <label for="upload_file" class="block text-sm font-medium leading-6 text-gray-900">Upload CSV or Excel File</label>
                                        <a href="{{ url_for('download_csv_template') }}"
                                           class="text-sm text-indigo-600 hover:text-indigo-500 font-medium flex items-center">
                                            <i class="fas fa-download mr-1"></i>
                                            Download Template
                                        </a>
                                    </div>
                                    <input type="file" id="upload_file" name="upload_file" accept=".csv,.xlsx,.xls"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                                           onchange="handleFileSelect(event)">
                                </div>
                                <div class="text-sm text-gray-600">
                                    <p class="mb-2"><strong>Supported formats:</strong> CSV (.csv), Excel (.xlsx, .xls)</p>
                                    <p class="mb-2"><strong>Template format:</strong> Download the template above to see the required column structure.</p>
                                    <p><strong>Instructions:</strong></p>
                                    <ul class="list-disc pl-5 mt-1 space-y-1">
                                        <li>Each row represents one part of a question</li>
                                        <li>Use empty rows to separate different questions</li>
                                        <li>For marking points, use format: "description:score" separated by semicolons</li>
                                        <li>Example: "Apply power rule:1;Simplify expression:1"</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Topic Selection (for both upload methods) -->
                    <div>
                        <label for="topic" class="block text-sm font-medium leading-6 text-gray-900">Topic</label>
                        <div class="mt-2">
                            <select id="topic" name="topic_id" required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                <option selected disabled>Select Topic</option>
                            </select>
                        </div>
                    </div>

                    <!-- PDF Upload Section -->
                    <div id="pdfUploadSection" class="hidden">
                        <div class="space-y-4">
                            <div class="p-4 bg-indigo-50 rounded-md">
                                <h3 class="text-md font-medium text-indigo-800 mb-2">Combined PDF (Required)</h3>
                                <label for="question_pdf" class="block text-sm font-medium leading-6 text-gray-900">Upload PDF with Questions & Answers</label>
                                <div class="mt-2">
                                    <input type="file" id="question_pdf" name="question_pdf" accept=".pdf"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                           onchange="handleQuestionPdfSelect(event)">
                                </div>
                                <div class="mt-2 text-sm text-gray-500">
                                    <p>This PDF should contain both questions and answers in a single document.</p>
                                </div>
                                <div id="questionPdfPreview" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Preview</h4>
                                    <div class="border border-gray-200 rounded bg-white h-48 overflow-auto p-2">
                                        <div class="flex justify-center items-center h-full" id="questionPdfContent">
                                            <p class="text-gray-400">PDF preview will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 p-4 bg-blue-50 rounded-md">
                                <h3 class="text-md font-medium text-blue-800 mb-2">Process PDF</h3>
                                <p class="text-sm text-gray-700 mb-4">
                                    The system will extract structured data from your PDF using OCR technology.
                                    This may take a few moments depending on the size and complexity of your document.
                                </p>
                                <button type="button" id="convertPdfButton"
                                        class="text-white bg-indigo-600 hover:bg-indigo-700 font-medium rounded-lg text-sm px-5 py-2.5 mb-2 focus:outline-none w-full"
                                        onclick="convertPdf()">
                                    <i class="fas fa-magic mr-2"></i> Extract Questions & Answers
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button (Initially Hidden) -->
                    <div id="submitButtonContainer" class="hidden">
                        <button type="button" id="submitButton" onclick="submitData()"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Upload Questions
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- CSV Preview Section (Initially Hidden) -->
        <div id="csvPreviewContainer" class="hidden bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Preview & Edit Data</h2>
                    <div class="flex space-x-2">
                        <button id="addRowButton" onclick="addRow()" class="text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded-md flex items-center">
                            <i class="fas fa-plus mr-1"></i> Add Row
                        </button>
                        <button id="addEmptyRowButton" onclick="addEmptyRow()" class="text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-md flex items-center">
                            <i class="fas fa-minus mr-1"></i> Add Empty Row
                        </button>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mb-4 p-4 bg-yellow-50 rounded-md border border-yellow-100">
                    <h3 class="font-medium text-yellow-800 mb-2">Instructions:</h3>
                    <ul class="list-disc pl-5 text-sm text-yellow-700">
                        <li>Each question can have multiple parts (rows)</li>
                        <li>Empty rows separate different questions</li>
                        <li>Double-click on any cell to edit its content</li>
                        <li>For marking points, use format: "description:score" separated by semicolons</li>
                        <li>Click "Add Empty Row" to separate different questions</li>
                    </ul>
                </div>

                <!-- Table container with horizontal scroll for small screens -->
                <div class="overflow-x-auto">
                    <table id="csvPreviewTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr id="tableHeader"></tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script>

<script>
    // Setup PDF.js worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js';

    // Global variable to store the parsed data
    let tableData = {
        headers: [],
        rows: []
    };

    // Load topics based on selected subject
    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                let topicSelect = document.getElementById('topic');
                topicSelect.innerHTML = "<option selected disabled>Select Topic</option>";
                data.topics.forEach(function(topic) {
                    let option = document.createElement("option");
                    option.value = topic.id;
                    option.text = topic.name;
                    topicSelect.appendChild(option);
                });
            });
    }

    // Switch between upload tabs
    function switchTab(tab) {
        // Update tab styling
        if (tab === 'data') {
            document.getElementById('tabData').className = 'border-indigo-500 text-indigo-600 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium';
            document.getElementById('tabPdf').className = 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium';
            document.getElementById('dataUploadSection').classList.remove('hidden');
            document.getElementById('pdfUploadSection').classList.add('hidden');
        } else {
            document.getElementById('tabData').className = 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium';
            document.getElementById('tabPdf').className = 'border-indigo-500 text-indigo-600 whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium';
            document.getElementById('dataUploadSection').classList.add('hidden');
            document.getElementById('pdfUploadSection').classList.remove('hidden');
        }

        // Hide the CSV preview when switching tabs
        document.getElementById('csvPreviewContainer').classList.add('hidden');
        document.getElementById('submitButtonContainer').classList.add('hidden');
    }

    // Handle Question PDF file selection
    function handleQuestionPdfSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Show the PDF preview container
        document.getElementById('questionPdfPreview').classList.remove('hidden');

        // Clear previous preview
        const previewContainer = document.getElementById('questionPdfContent');
        previewContainer.innerHTML = '<div class="flex justify-center items-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div></div>';

        // Create a URL for the PDF
        const fileURL = URL.createObjectURL(file);

        // Load and render the PDF
        pdfjsLib.getDocument(fileURL).promise.then(function(pdf) {
            previewContainer.innerHTML = '';

            // Render just the first page as preview
            renderPreviewPage(pdf, 1, previewContainer);

            if (pdf.numPages > 1) {
                const morePages = document.createElement('div');
                morePages.className = 'text-center text-xs text-gray-500 mt-2';
                morePages.textContent = `+ ${pdf.numPages - 1} more pages`;
                previewContainer.appendChild(morePages);
            }
        }).catch(function(error) {
            previewContainer.innerHTML = `<div class="text-red-500 text-sm">Error loading PDF: ${error.message}</div>`;
        });
    }

    // Handle Answer PDF file selection - removed as we now use a single PDF

    // Render a specific page of the PDF with smaller size for preview
    function renderPreviewPage(pdf, pageNum, container) {
        pdf.getPage(pageNum).then(function(page) {
            const scale = 0.5; // Smaller scale for preview
            const viewport = page.getViewport({ scale });

            // Create a canvas for the page
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            canvas.className = 'mx-auto border border-gray-100';

            container.appendChild(canvas);

            // Render the page content
            page.render({
                canvasContext: context,
                viewport: viewport
            });
        });
    }

    // Render a specific page of the PDF (keep original function for compatibility)
    function renderPage(pdf, pageNum, container) {
        pdf.getPage(pageNum).then(function(page) {
            const viewport = page.getViewport({ scale: 1.0 });

            // Create page wrapper with number
            const pageWrapper = document.createElement('div');
            pageWrapper.className = 'border-b border-gray-200 pb-4 mb-4';

            const pageLabel = document.createElement('div');
            pageLabel.className = 'text-xs text-gray-500 mb-2';
            pageLabel.textContent = `Page ${pageNum}`;
            pageWrapper.appendChild(pageLabel);

            // Create a canvas for the page
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            canvas.className = 'mx-auto border border-gray-100';

            pageWrapper.appendChild(canvas);
            container.appendChild(pageWrapper);

            // Render the page content
            page.render({
                canvasContext: context,
                viewport: viewport
            });
        });
    }

    // Convert PDF to structured data
    function convertPdf() {
        const questionFile = document.getElementById('question_pdf').files[0];
        if (!questionFile) {
            alert('Please select a PDF file first');
            return;
        }

        // Check if subject is selected
        const subject = document.getElementById('subject').value;

        if (!subject || subject === 'Select Subject') {
            alert('Please select a subject.');
            return;
        }

        // Show loading indicator in the preview container
        document.getElementById('questionPdfContent').innerHTML = '<div class="flex justify-center items-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div><p class="ml-3 text-sm">Processing...</p></div>';

        // Disable the convert button
        const convertButton = document.getElementById('convertPdfButton');
        convertButton.disabled = true;
        convertButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing PDF...';
        convertButton.classList.add('bg-gray-500');
        convertButton.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');

        // Submit the form directly to the server for OCR processing
        document.getElementById('uploadForm').submit();
    }

    // Handle file selection for preview
    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Reset the table data
        tableData = {
            headers: [],
            rows: []
        };

        // Show loading indicator
        document.getElementById('csvPreviewContainer').innerHTML = '<div class="p-6 text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading file preview...</p></div>';
        document.getElementById('csvPreviewContainer').classList.remove('hidden');

        // Process file based on its extension
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (fileExtension === 'csv') {
            parseCSV(file);
        } else if (['xlsx', 'xls'].includes(fileExtension)) {
            parseExcel(file);
        } else {
            alert('Unsupported file format. Please use CSV or Excel files.');
        }
    }

    // Parse CSV file
    function parseCSV(file) {
        Papa.parse(file, {
            header: true,
            skipEmptyLines: false, // Capture empty rows
            complete: function(results) {
                tableData.headers = results.meta.fields || [];

                // Process rows to handle empty rows properly
                results.data.forEach(row => {
                    // Check if this is an empty row (all values are empty)
                    const isEmpty = Object.values(row).every(val => !val);
                    if (isEmpty) {
                        // Add a special empty row marker
                        tableData.rows.push('EMPTY_ROW');
                    } else {
                        tableData.rows.push(row);
                    }
                });

                renderTable();
            },
            error: function(error) {
                console.error('Error parsing CSV:', error);
                alert('Error parsing CSV file: ' + error.message);
            }
        });
    }

    // Parse Excel file
    function parseExcel(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });

                // Get the first worksheet
                const worksheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[worksheetName];

                // Convert to JSON with header: true
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });

                if (jsonData.length > 0) {
                    // First row is headers
                    tableData.headers = jsonData[0];

                    // Process the rest of the rows
                    for (let i = 1; i < jsonData.length; i++) {
                        const row = jsonData[i];

                        // Check if this is an empty row
                        const isEmpty = row.every(cell => !cell);
                        if (isEmpty) {
                            tableData.rows.push('EMPTY_ROW');
                        } else {
                            // Convert array row to object with headers as keys
                            const rowObj = {};
                            tableData.headers.forEach((header, index) => {
                                rowObj[header] = row[index] || '';
                            });
                            tableData.rows.push(rowObj);
                        }
                    }

                    renderTable();
                } else {
                    alert('The Excel file appears to be empty.');
                }
            } catch (error) {
                console.error('Error parsing Excel:', error);
                alert('Error parsing Excel file: ' + error.message);
            }
        };
        reader.readAsArrayBuffer(file);
    }

    // Render the data table
    function renderTable() {
        // Reset containers
        document.getElementById('csvPreviewContainer').innerHTML = '';
        document.getElementById('csvPreviewContainer').appendChild(document.createRange().createContextualFragment(`
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Preview & Edit Data</h2>
                    <div class="flex space-x-2">
                        <button id="addRowButton" onclick="addRow()" class="text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded-md flex items-center">
                            <i class="fas fa-plus mr-1"></i> Add Row
                        </button>
                        <button id="addEmptyRowButton" onclick="addEmptyRow()" class="text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded-md flex items-center">
                            <i class="fas fa-minus mr-1"></i> Add Empty Row
                        </button>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mb-4 p-4 bg-yellow-50 rounded-md border border-yellow-100">
                    <h3 class="font-medium text-yellow-800 mb-2">Instructions:</h3>
                    <ul class="list-disc pl-5 text-sm text-yellow-700">
                        <li>Each question can have multiple parts (rows)</li>
                        <li>Empty rows separate different questions</li>
                        <li>Double-click on any cell to edit its content</li>
                        <li>For marking points, use format: "description:score" separated by semicolons</li>
                        <li>Click "Add Empty Row" to separate different questions</li>
                    </ul>
                </div>

                <!-- Table container with horizontal scroll for small screens -->
                <div class="overflow-x-auto">
                    <table id="csvPreviewTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr id="tableHeader"></tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200"></tbody>
                    </table>
                </div>
            </div>
        `));

        // Show the container
        document.getElementById('csvPreviewContainer').classList.remove('hidden');
        document.getElementById('submitButtonContainer').classList.remove('hidden');

        const tableHeader = document.getElementById('tableHeader');
        const tableBody = document.getElementById('tableBody');

        // Add headers
        tableHeader.innerHTML = '';
        tableData.headers.forEach(header => {
            const th = document.createElement('th');
            th.className = 'px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';
            th.textContent = header;
            th.setAttribute('data-header', header);
            tableHeader.appendChild(th);
        });

        // Add action column header
        const actionsTh = document.createElement('th');
        actionsTh.className = 'px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';
        actionsTh.textContent = 'Actions';
        tableHeader.appendChild(actionsTh);

        // Add rows
        tableBody.innerHTML = '';
        tableData.rows.forEach((row, rowIndex) => {
            if (row === 'EMPTY_ROW') {
                // Add a visual separator for empty rows
                const emptyRow = document.createElement('tr');
                emptyRow.className = 'bg-gray-100 border-b border-t border-dashed border-gray-300';
                emptyRow.setAttribute('data-row-type', 'empty');

                const emptyTd = document.createElement('td');
                emptyTd.colSpan = tableData.headers.length + 1;
                emptyTd.className = 'px-3 py-2 text-center text-xs text-gray-500';
                emptyTd.innerHTML = '<span class="font-semibold">Empty row</span> (separates questions)';

                emptyRow.appendChild(emptyTd);
                tableBody.appendChild(emptyRow);
            } else {
                const tr = document.createElement('tr');
                tr.className = rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                tr.setAttribute('data-row-index', rowIndex);

                tableData.headers.forEach(header => {
                    const td = document.createElement('td');
                    td.className = 'px-3 py-4 whitespace-normal text-sm text-gray-500 border-r border-gray-100';
                    td.textContent = row[header] || '';
                    td.setAttribute('data-header', header);
                    td.setAttribute('data-row-index', rowIndex);
                    td.addEventListener('dblclick', function(e) {
                        makeEditable(e.target);
                    });
                    tr.appendChild(td);
                });

                // Add action column
                const actionsTd = document.createElement('td');
                actionsTd.className = 'px-3 py-4 whitespace-nowrap text-right text-sm font-medium';
                actionsTd.innerHTML = `
                    <button onclick="deleteRow(${rowIndex})" class="text-red-600 hover:text-red-900 mr-2">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                tr.appendChild(actionsTd);

                tableBody.appendChild(tr);
            }
        });
    }

    // Make a cell editable
    function makeEditable(cell) {
        const currentValue = cell.textContent;
        const rowIndex = cell.getAttribute('data-row-index');
        const header = cell.getAttribute('data-header');

        cell.innerHTML = '';
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue;
        input.className = 'w-full p-1 border border-indigo-300 rounded-sm focus:outline-none focus:ring-2 focus:ring-indigo-500';
        cell.appendChild(input);

        // Focus the input and select all text
        input.focus();
        input.select();

        // Update data on blur
        input.addEventListener('blur', function() {
            updateCellValue(rowIndex, header, input.value);
            cell.textContent = input.value;
        });

        // Update data on Enter key
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                updateCellValue(rowIndex, header, input.value);
                cell.textContent = input.value;
            }
        });
    }

    // Update cell value in the data
    function updateCellValue(rowIndex, header, value) {
        if (rowIndex && header) {
            tableData.rows[rowIndex][header] = value;
        }
    }

    // Add a new row
    function addRow() {
        const newRow = {};
        tableData.headers.forEach(header => {
            newRow[header] = '';
        });
        tableData.rows.push(newRow);
        renderTable();
    }

    // Add an empty row separator
    function addEmptyRow() {
        tableData.rows.push('EMPTY_ROW');
        renderTable();
    }

    // Delete a row
    function deleteRow(rowIndex) {
        if (confirm('Are you sure you want to delete this row?')) {
            tableData.rows.splice(rowIndex, 1);
            renderTable();
        }
    }

    // Convert the edited data back to CSV and submit the form
    function submitData() {
        if (!validateForm()) return;

        // Create a new blob with the edited data
        const csvData = convertToCSV();
        const blob = new Blob([csvData], { type: 'text/csv' });
        const file = new File([blob], 'edited_data.csv', { type: 'text/csv' });

        // Create a new FileList containing the File object
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);

        // Replace the file input's files with our new FileList
        const fileInput = document.getElementById('upload_file');
        fileInput.files = dataTransfer.files;

        // Submit the form
        document.getElementById('uploadForm').submit();
    }

    // Validate the form before submission
    function validateForm() {
        const subject = document.getElementById('subject').value;

        if (!subject || subject === 'Select Subject') {
            alert('Please select a subject.');
            return false;
        }

        if (tableData.rows.length === 0) {
            alert('No data to upload. Please add at least one question.');
            return false;
        }

        return true;
    }

    // Convert the edited data to CSV format
    function convertToCSV() {
        // Create headers
        let csv = tableData.headers.join(',') + '\n';

        // Add rows
        tableData.rows.forEach(row => {
            if (row === 'EMPTY_ROW') {
                // Add an empty row
                csv += tableData.headers.map(() => '').join(',') + '\n';
            } else {
                // Add a data row
                csv += tableData.headers.map(header => {
                    // Escape commas and quotes in cell values
                    const cellValue = row[header] || '';
                    if (cellValue.includes(',') || cellValue.includes('"')) {
                        return `"${cellValue.replace(/"/g, '""')}"`;
                    }
                    return cellValue;
                }).join(',') + '\n';
            }
        });

        return csv;
    }
</script>
{% endblock %}
