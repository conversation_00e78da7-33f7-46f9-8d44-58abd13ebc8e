{% extends "base.html" %}

{% block content %}
<!-- Enhanced Hero Section -->
<div class="relative isolate overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 -z-10 h-full w-full">
        <div class="absolute inset-0 bg-[linear-gradient(to_right,#f8fafc_1px,transparent_1px),linear-gradient(to_bottom,#f8fafc_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-30"></div>
        <!-- Floating geometric shapes -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-indigo-200 rounded-full opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-lg opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-40 left-20 w-12 h-12 bg-blue-200 rounded-full opacity-20 animate-float"></div>
        <div class="absolute bottom-20 right-10 w-24 h-24 bg-indigo-100 rounded-lg opacity-20 animate-float-delayed"></div>
    </div>

    <!-- Hero Content -->
    <div class="mx-auto max-w-5xl px-6 py-24 sm:py-32 lg:px-8">
        <div class="text-center">
            <!-- Raffles Logo and Branding -->
            <div class="flex items-center justify-center mb-8">
                <img src="{{ url_for('static', filename='images/ricrest.jpg') }}" alt="Raffles Institution" class="h-16 w-16 mr-4 rounded-lg shadow-md">
                <div class="text-left">
                    <div class="text-sm font-semibold text-indigo-600 uppercase tracking-wide">Partnered with</div>
                    <div class="text-lg font-bold text-gray-900">Raffles Institution</div>
                </div>
            </div>

            <!-- Main Headline with Enhanced Typography -->
            <h1 class="text-5xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 sm:text-7xl mb-6 leading-tight">
                Your personal learning companion
            </h1>

            <!-- Subheadline with Icon -->
            <div class="flex items-center justify-center mb-12">
                <div class="flex items-center bg-white rounded-full px-6 py-3 shadow-lg border border-indigo-100">
                    <i class="fas fa-star text-yellow-500 mr-2"></i>
                    <p class="text-xl text-gray-700 font-medium">
                        Accurate and useful feedback, designed to help you score
                    </p>
                    <i class="fas fa-star text-yellow-500 ml-2"></i>
                </div>
            </div>

            <!-- Enhanced Call to Action -->
            <div class="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-x-6">
                {% if not session.get('user_id') %}
                <a href="{{ url_for('register') }}" class="group relative rounded-xl bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-4 text-lg font-semibold text-white shadow-xl hover:shadow-2xl focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-rocket mr-2"></i>
                        Get started
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-200"></i>
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                {% else %}
                <a href="{{ url_for('dashboard') }}" class="group relative rounded-xl bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-4 text-lg font-semibold text-white shadow-xl hover:shadow-2xl focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Go to Dashboard
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-200"></i>
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                {% endif %}

                <!-- Our Features Button -->
                <button onclick="scrollToFeatures()" class="group relative rounded-xl bg-white border-2 border-indigo-200 px-8 py-4 text-lg font-semibold text-indigo-600 shadow-lg hover:shadow-xl hover:border-indigo-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-star mr-2"></i>
                        Our Features
                        <i class="fas fa-chevron-down ml-2 group-hover:translate-y-1 transition-transform duration-200"></i>
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
            </div>

            <!-- Restart Tour Button for Logged-in Users -->
            {% if session.get('user_id') %}
            <div class="mt-6">
                <button id="restart-tour-btn" class="group relative rounded-lg bg-gradient-to-r from-purple-100 to-indigo-100 border border-purple-200 px-6 py-3 text-sm font-semibold text-purple-700 shadow-md hover:shadow-lg hover:from-purple-200 hover:to-indigo-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600 transform hover:scale-105 transition-all duration-300">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-route mr-2"></i>
                        Take the Tour Again
                        <i class="fas fa-redo ml-2 group-hover:rotate-180 transition-transform duration-300"></i>
                    </span>
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Enhanced Grading System Demo Section -->
<div id="features-section" class="bg-white py-20 relative overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50"></div>
    <div class="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-full opacity-10"></div>
    <div class="absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full opacity-10"></div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div class="text-center mb-16">
            <!-- <div class="inline-flex items-center bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full px-6 py-2 mb-6"> -->
                <!-- <i class="fas fa-robot text-indigo-600 mr-2"></i> -->
                <!-- <span class="text-sm font-semibold text-indigo-800 uppercase tracking-wide">AI-Powered Technology</span> -->
            <!-- </div> -->
            <h2 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Intelligent Grading System
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Get instant, detailed feedback with our advanced marking point system that analyzes every aspect of your answer
            </p>
        </div>

        <div class="bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6">
                <div class="flex items-center">
                    <div class="bg-white/20 rounded-lg p-2 mr-4">
                        <i class="fas fa-flask text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-white">Live Demo: Chemistry Question</h3>
                        <p class="text-indigo-100 text-sm">See how our AI grades student responses</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-8">
                <!-- Question and Answer -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <div class="bg-blue-50 rounded-xl p-6 border border-blue-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-question-circle text-blue-600 mr-2"></i>
                            <span class="font-semibold text-blue-800">Question</span>
                        </div>
                        <p class="text-gray-800">Explain the process of electrolysis of sodium chloride solution.</p>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-user-edit text-gray-600 mr-2"></i>
                            <span class="font-semibold text-gray-800">Student Answer</span>
                        </div>
                        <p class="text-gray-800 italic">"During electrolysis of NaCl solution, sodium ions move to the cathode where they are reduced to sodium metal. Chloride ions move to the anode where they are oxidized to chlorine gas."</p>
                    </div>
                </div>

                <!-- AI Analysis Results -->
                <div class="space-y-6">
                    <div class="flex items-center mb-6">
                        <div class="bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg p-2 mr-3">
                            <i class="fas fa-brain text-white"></i>
                        </div>
                        <h4 class="text-xl font-semibold text-gray-900">AI Analysis Results</h4>
                        <div class="ml-auto bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-clock mr-1"></i>
                            Graded in 2.3s
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200">
                            <div class="flex items-center mb-3">
                                <div class="bg-green-500 rounded-full p-1 mr-3">
                                    <i class="fas fa-check text-white text-sm"></i>
                                </div>
                                <span class="font-semibold text-green-800">Correctly identified ion movement</span>
                            </div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-green-700 font-medium">Score: 2/2 points</span>
                                <div class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">100%</div>
                            </div>
                            <p class="text-xs text-green-600 bg-green-100 rounded-lg p-2">
                                <i class="fas fa-quote-left mr-1"></i>
                                Evidence: "sodium ions move to the cathode"
                            </p>
                        </div>

                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200">
                            <div class="flex items-center mb-3">
                                <div class="bg-green-500 rounded-full p-1 mr-3">
                                    <i class="fas fa-check text-white text-sm"></i>
                                </div>
                                <span class="font-semibold text-green-800">Correct oxidation process</span>
                            </div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-green-700 font-medium">Score: 2/2 points</span>
                                <div class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">100%</div>
                            </div>
                            <p class="text-xs text-green-600 bg-green-100 rounded-lg p-2">
                                <i class="fas fa-quote-left mr-1"></i>
                                Evidence: "oxidized to chlorine gas"
                            </p>
                        </div>

                        <div class="bg-gradient-to-br from-yellow-50 to-amber-50 border-2 border-yellow-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200">
                            <div class="flex items-center mb-3">
                                <div class="bg-yellow-500 rounded-full p-1 mr-3">
                                    <i class="fas fa-exclamation text-white text-sm"></i>
                                </div>
                                <span class="font-semibold text-yellow-800">Partial: Reduction process</span>
                            </div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-yellow-700 font-medium">Score: 1/2 points</span>
                                <div class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold">50%</div>
                            </div>
                            <p class="text-xs text-yellow-600 bg-yellow-100 rounded-lg p-2">
                                <i class="fas fa-lightbulb mr-1"></i>
                                Missing: Water reduction at cathode
                            </p>
                        </div>

                        <div class="bg-gradient-to-br from-red-50 to-rose-50 border-2 border-red-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200">
                            <div class="flex items-center mb-3">
                                <div class="bg-red-500 rounded-full p-1 mr-3">
                                    <i class="fas fa-times text-white text-sm"></i>
                                </div>
                                <span class="font-semibold text-red-800">Omitted: Overall equation</span>
                            </div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-red-700 font-medium">Score: 0/1 points</span>
                                <div class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">0%</div>
                            </div>
                            <p class="text-xs text-red-600 bg-red-100 rounded-lg p-2">
                                <i class="fas fa-info-circle mr-1"></i>
                                Missing: Balanced chemical equation
                            </p>
                        </div>
                    </div>

                    <!-- Final Score -->
                    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border-2 border-indigo-200 rounded-xl p-6 mt-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full p-3 mr-4">
                                    <i class="fas fa-trophy text-white text-lg"></i>
                                </div>
                                <div>
                                    <span class="text-lg font-semibold text-indigo-800">Final Score</span>
                                    <p class="text-sm text-indigo-600">Comprehensive analysis complete</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-indigo-600">5/7</div>
                                <div class="text-lg font-semibold text-indigo-500">71%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced RAG Notes Retrieval Demo Section -->
<div class="bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 py-20 relative overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-20 left-20 w-40 h-40 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-10 animate-float"></div>
        <div class="absolute bottom-20 right-20 w-32 h-32 bg-gradient-to-br from-blue-200 to-purple-200 rounded-full opacity-10 animate-float-delayed"></div>
        <div class="absolute top-1/2 left-1/4 w-20 h-20 bg-gradient-to-br from-indigo-200 to-blue-200 rounded-lg opacity-10 animate-float"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div class="text-center mb-16">
            <div class="inline-flex items-center bg-gradient-to-r from-purple-100 to-blue-100 rounded-full px-6 py-2 mb-6">
                <i class="fas fa-search text-purple-600 mr-2"></i>
                <span class="text-sm font-semibold text-purple-800 uppercase tracking-wide">RAG Technology</span>
            </div>
            <h2 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Intelligent Notes Retrieval
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                AI automatically finds and surfaces the most relevant study materials from your notes library
            </p>
        </div>

        <div class="bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-blue-600 px-8 py-6">
                <div class="flex items-center">
                    <div class="bg-white/20 rounded-lg p-2 mr-4">
                        <i class="fas fa-book-open text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-white">Live Demo: Organic Chemistry Query</h3>
                        <p class="text-purple-100 text-sm">Watch AI find relevant notes in real-time</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-8">
                <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
                    <!-- Query Side -->
                    <div class="space-y-6">
                        <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border-2 border-purple-200">
                            <div class="flex items-center mb-4">
                                <div class="bg-purple-500 rounded-lg p-2 mr-3">
                                    <i class="fas fa-question text-white"></i>
                                </div>
                                <span class="font-semibold text-purple-800">Student Question</span>
                            </div>
                            <p class="text-gray-800 text-lg italic">"Explain the mechanism of nucleophilic substitution in alkyl halides."</p>
                        </div>

                        <!-- Processing Animation -->
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
                            <div class="flex items-center mb-4">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg p-2 mr-3">
                                    <i class="fas fa-cogs text-white animate-spin"></i>
                                </div>
                                <span class="font-semibold text-gray-800">AI Processing</span>
                            </div>
                            <div class="space-y-2 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                    <span>Analyzing question content...</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                    <span>Searching 1,967+ note chunks...</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                    <span>Ranking by relevance...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Side -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg p-2 mr-3">
                                <i class="fas fa-magic text-white"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900">AI Retrieved Notes</h4>
                            <div class="ml-auto bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                <i class="fas fa-bolt mr-1"></i>
                                Found in 0.8s
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200 hover:shadow-lg">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="bg-blue-500 rounded-full p-1 mr-3">
                                            <i class="fas fa-star text-white text-xs"></i>
                                        </div>
                                        <span class="font-semibold text-blue-800">Nucleophilic Substitution</span>
                                    </div>
                                    <div class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        95% match
                                    </div>
                                </div>
                                <p class="text-sm text-blue-700 bg-blue-100 rounded-lg p-2">
                                    <i class="fas fa-folder mr-1"></i>
                                    From: Organic Chemistry → Reaction Mechanisms
                                </p>
                            </div>

                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200 hover:shadow-lg">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="bg-blue-500 rounded-full p-1 mr-3">
                                            <i class="fas fa-star text-white text-xs"></i>
                                        </div>
                                        <span class="font-semibold text-blue-800">SN1 vs SN2 Mechanisms</span>
                                    </div>
                                    <div class="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        88% match
                                    </div>
                                </div>
                                <p class="text-sm text-blue-700 bg-blue-100 rounded-lg p-2">
                                    <i class="fas fa-folder mr-1"></i>
                                    From: Organic Chemistry → Alkyl Halides
                                </p>
                            </div>

                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-5 transform hover:scale-105 transition-all duration-200 hover:shadow-lg">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="bg-blue-500 rounded-full p-1 mr-3">
                                            <i class="fas fa-star text-white text-xs"></i>
                                        </div>
                                        <span class="font-semibold text-blue-800">Leaving Groups</span>
                                    </div>
                                    <div class="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        82% match
                                    </div>
                                </div>
                                <p class="text-sm text-blue-700 bg-blue-100 rounded-lg p-2">
                                    <i class="fas fa-folder mr-1"></i>
                                    From: Organic Chemistry → Functional Groups
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Smart Learning Info -->
                <div class="mt-8 bg-gradient-to-r from-indigo-50 to-purple-50 border-2 border-indigo-200 rounded-xl p-6">
                    <div class="flex items-start">
                        <div class="bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg p-2 mr-4 mt-1">
                            <i class="fas fa-lightbulb text-white"></i>
                        </div>
                        <div>
                            <h5 class="font-semibold text-indigo-800 mb-2">Smart Learning Technology</h5>
                            <p class="text-sm text-indigo-700 leading-relaxed">
                                Our advanced AI uses <strong>Retrieval-Augmented Generation (RAG)</strong> with semantic search to instantly find the most relevant content from your study materials. The system searches through <strong>1,967+ notes chunks</strong> across <strong>26 topics</strong>, using state-of-the-art language models to understand context and meaning, not just keywords.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Key Features Summary -->
<div class="bg-white py-20 relative overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-indigo-50/30"></div>
    <div class="absolute top-10 left-10 w-20 h-20 bg-indigo-200 rounded-full opacity-10 animate-float"></div>
    <div class="absolute bottom-10 right-10 w-16 h-16 bg-purple-200 rounded-lg opacity-10 animate-float-delayed"></div>

    <div class="relative mx-auto max-w-6xl px-6 lg:px-8">
        <div class="text-center mb-16">
            <div class="inline-flex items-center bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full px-6 py-2 mb-6">
                <i class="fas fa-award text-indigo-600 mr-2"></i>
                <span class="text-sm font-semibold text-indigo-800 uppercase tracking-wide">Why Choose Vast</span>
            </div>
            <h2 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Built for Academic Excellence
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the future of learning with our cutting-edge AI technology
            </p>
        </div>

        <div class="grid grid-cols-1 gap-8 sm:grid-cols-3">
            <!-- Feature 1 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                <div class="bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl p-4 w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-robot text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">AI-Powered Grading</h3>
                <p class="text-gray-600 leading-relaxed">
                    Get instant, detailed feedback with our advanced marking point system that analyzes every aspect of your answer with precision.
                </p>
                <div class="mt-4 flex items-center text-sm text-indigo-600 font-medium">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Instant feedback</span>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                <div class="bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl p-4 w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Smart Notes Retrieval</h3>
                <p class="text-gray-600 leading-relaxed">
                    AI automatically finds and surfaces the most relevant study materials from your notes library using advanced semantic search.
                </p>
                <div class="mt-4 flex items-center text-sm text-blue-600 font-medium">
                    <i class="fas fa-magic mr-2"></i>
                    <span>1,967+ notes chunks</span>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="group bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl p-4 w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Progress Tracking</h3>
                <p class="text-gray-600 leading-relaxed">
                    Monitor your learning journey with detailed analytics, performance insights, and personalized recommendations.
                </p>
                <div class="mt-4 flex items-center text-sm text-purple-600 font-medium">
                    <i class="fas fa-trophy mr-2"></i>
                    <span>Detailed analytics</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Final CTA Section -->
<div class="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-800 overflow-hidden">
    <!-- Background decoration -->
    <div class="absolute inset-0">
        <div class="absolute top-0 left-0 w-full h-full bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.1)_50%,transparent_75%)] bg-[length:20px_20px] animate-pulse"></div>
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
        <div class="absolute bottom-20 right-20 w-24 h-24 bg-white/10 rounded-lg animate-float-delayed"></div>
        <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-white/5 rounded-full animate-float"></div>
    </div>

    <div class="relative mx-auto max-w-5xl px-6 py-20 lg:px-8">
        <div class="text-center">
            <!-- Icon and Badge -->
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-8">
                <i class="fas fa-graduation-cap text-white mr-2"></i>
                <span class="text-sm font-semibold text-white uppercase tracking-wide">Start Your Journey</span>
            </div>

            <!-- Main Headline -->
            <h2 class="text-4xl font-bold tracking-tight text-white sm:text-6xl mb-6 leading-tight">
                Ready to transform your learning?
            </h2>

            <!-- Subheadline -->
            <p class="text-xl text-indigo-100 mb-12 max-w-3xl mx-auto leading-relaxed">
                Join the next generation of learners using AI-powered education technology to achieve academic excellence
            </p>

            <!-- CTA Button -->
            <div class="flex items-center justify-center gap-x-6 mb-8">
                {% if not session.get('user_id') %}
                <a href="{{ url_for('register') }}" class="group relative rounded-2xl bg-white px-10 py-5 text-xl font-bold text-indigo-600 shadow-2xl hover:shadow-3xl focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-rocket mr-3"></i>
                        Get started now
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-200"></i>
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                {% else %}
                <a href="{{ url_for('dashboard') }}" class="group relative rounded-2xl bg-white px-10 py-5 text-xl font-bold text-indigo-600 shadow-2xl hover:shadow-3xl focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Continue Learning
                        <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform duration-200"></i>
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-indigo-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Hidden elements to maintain compatibility with existing JavaScript -->
<div style="display: none;">
    <!-- Demo elements that existing JS might reference -->
    <div id="demo-replay"></div>
    <div id="start-vault-tour-button"></div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = {{ session.get('user_id') is not none | tojson }};

        if (isLoggedIn) {
            const startTourButton = document.getElementById('start-vault-tour-button');
            if (startTourButton) {
                startTourButton.addEventListener('click', function() {
                    // Clear all tour completion flags to restart the entire tour
                    localStorage.removeItem('globalTourCompleted');
                    localStorage.removeItem('vaultShepherdTourCompleted');
                    localStorage.removeItem('dojoShepherdTourCompleted');
                    window.location.href = "{{ url_for('vault') }}?start_tour=true"; // Redirect to vault to start tour
                });
            }

            // Add restart tour button functionality
            const restartTourButton = document.getElementById('restart-tour-btn');
            if (restartTourButton) {
                restartTourButton.addEventListener('click', function() {
                    // Show confirmation dialog
                    if (confirm('This will restart the complete tour from the beginning. Are you sure?')) {
                        // Clear all tour completion flags to restart the entire tour
                        localStorage.removeItem('globalTourCompleted');
                        localStorage.removeItem('vaultShepherdTourCompleted');
                        localStorage.removeItem('dojoShepherdTourCompleted');

                        // Show loading state
                        restartTourButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Starting Tour...';
                        restartTourButton.disabled = true;

                        // Redirect to vault to start tour after a short delay
                        setTimeout(() => {
                            window.location.href = "{{ url_for('vault') }}?start_tour=true";
                        }, 500);
                    }
                });
            }
        }
    });

    // Smooth scroll to features section
    function scrollToFeatures() {
        // Find the features section by ID
        const featuresSection = document.getElementById('features-section');
        if (featuresSection) {
            featuresSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
</script>

{% endblock %}
