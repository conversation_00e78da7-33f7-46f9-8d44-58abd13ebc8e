{% extends "base.html" %}

{% block content %}
<style>
/* Custom scrollbar for horizontal scroll */
.overflow-x-auto::-webkit-scrollbar {
    height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Smooth scrolling */
.overflow-x-auto {
    scroll-behavior: smooth;
}
</style>
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ user.username }}!</h1>
                    <p class="text-gray-600 mt-1">Here's your personalized learning dashboard</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Development Test Buttons -->
                    {% if user.is_admin %}
                    <a href="{{ url_for('onboarding', restart='true') }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                        <i class="fas fa-cog mr-2 text-gray-400"></i>
                        Test Onboarding
                    </a>
                    {% endif %}
                    {% if user.is_admin %}
                    <button onclick="resetOnboardingForTour()" class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                        <i class="fas fa-undo mr-2 text-red-400"></i>
                        Reset & Start Tour
                    </button>
                    {% endif %}
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Grade Level</p>
                        <p class="font-semibold text-gray-900">{{ user.grade_level }}</p>
                    </div>
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl">
                            {% if user.grade_level == 'PSLE' %}📚
                            {% elif user.grade_level == 'J1' %}🎓
                            {% elif user.grade_level == 'J2' %}🏆
                            {% else %}👤{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Recommended Problems Section -->
        <div class="mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Recommended Problems</h2>
                            <p class="text-gray-600 text-sm mt-1">Problems tailored to your subjects and skill level</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                {{ recommended_problems|length }} problems
                            </span>
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    {% if recommended_problems %}
                        <!-- Horizontal scrolling container -->
                        <div class="overflow-x-auto">
                            <div class="flex space-x-6 pb-4" style="width: max-content;">
                                {% for question in recommended_problems %}
                                <div class="flex-shrink-0 w-80 border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex flex-col h-full">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-3 flex-wrap">
                                                <h3 class="font-medium text-gray-900 text-sm">
                                                    {% if question.title %}
                                                        {{ question.title }}
                                                    {% else %}
                                                        Question #{{ question.id }}
                                                    {% endif %}
                                                </h3>
                                            </div>

                                            <div class="flex items-center space-x-2 mb-3">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ question.subject_name }}
                                                </span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    {{ question.topic_name }}
                                                </span>
                                            </div>

                                            {% if question.description %}
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-3">
                                                {{ question.description[:120] }}{% if question.description|length > 120 %}...{% endif %}
                                            </p>
                                            {% endif %}

                                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                    {{ question.parts|length }} part{{ 's' if question.parts|length != 1 else '' }}
                                                </span>
                                                <span class="flex items-center">
                                                    <span class="mr-1">📊</span>
                                                    {{ question.user_confidence }}/5
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mt-auto">
                                            <a href="{{ url_for('load_question', question_id=question.id) }}"
                                               class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                                                Start Problem
                                                <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="mt-6 text-center">
                            <a href="{{ url_for('vault') }}"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Browse All Problems
                                <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                </svg>
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <div class="text-6xl mb-4">📚</div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No recommendations yet</h3>
                            <p class="text-gray-600 mb-4">We're working on finding the perfect problems for you based on your subjects.</p>
                            <a href="{{ url_for('vault') }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                Explore Problem Vault
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Stats and Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 flex items-center">
                            <span class="mr-2">🔥</span>
                            Learning Streak
                        </span>
                        <span class="font-semibold text-gray-900">{{ user_stats.learning_streak }} day{{ 's' if user_stats.learning_streak != 1 else '' }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 flex items-center">
                            <span class="mr-2">✅</span>
                            Problems Solved
                        </span>
                        <span class="font-semibold text-gray-900">{{ user_stats.problems_solved }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 flex items-center">
                            <span class="mr-2">📝</span>
                            Problems Unsolved
                        </span>
                        <span class="font-semibold text-gray-900">{{ user_stats.problems_unsolved }}</span>
                    </div>
                    {% if user_stats.total_problems > 0 %}
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-gray-600">Progress</span>
                            <span class="text-sm font-medium text-gray-900">{{ ((user_stats.problems_solved / user_stats.total_problems) * 100)|round|int }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ ((user_stats.problems_solved / user_stats.total_problems) * 100)|round }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ url_for('vault') }}"
                       class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                        <span class="text-2xl mr-3">🏛️</span>
                        <div>
                            <p class="font-medium text-gray-900">Problem Vault</p>
                            <p class="text-sm text-gray-600">Browse all problems</p>
                        </div>
                    </a>

                    <a href="{{ url_for('groups') }}"
                       class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                        <span class="text-2xl mr-3">👥</span>
                        <div>
                            <p class="font-medium text-gray-900">Study Groups</p>
                            <p class="text-sm text-gray-600">Join or create groups</p>
                        </div>
                    </a>

                    <a href="{{ url_for('list_problemsets') }}"
                       class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200">
                        <span class="text-2xl mr-3">📋</span>
                        <div>
                            <p class="font-medium text-gray-900">Problem Sets</p>
                            <p class="text-sm text-gray-600">Curated collections</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetOnboardingForTour() {
    if (confirm('This will reset your onboarding and start the initial tour. Are you sure?')) {
        fetch('/api/reset-onboarding-for-tour', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear any existing tour flags
                localStorage.removeItem('globalTourCompleted');
                localStorage.removeItem('vaultShepherdTourCompleted');
                localStorage.removeItem('dojoShepherdTourCompleted');

                // Redirect to onboarding
                window.location.href = '/onboarding';
            } else {
                alert('Error resetting onboarding: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while resetting onboarding.');
        });
    }
}
</script>

{% endblock %}
