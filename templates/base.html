<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">

<head>
    <script type="text/javascript">
   (function(c,l,a,r,i,t,y){
       c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
       t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
       y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
   })(window, document, "clarity", "script", "rirwoibkqy");
</script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-FRWQVNGVXP"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-FRWQVNGVXP');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="VAST - Very Accurate Self Tutoring">
    <title>{{ title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        mono: ['JetBrains Mono', 'monospace'],
                    },
                    keyframes: {
                        'slide-in': {
                            '0%': { transform: 'translateY(-100%)' },
                            '100%': { transform: 'translateY(0)' }
                        },
                        'slide-out': {
                            '0%': { transform: 'translateY(0)' },
                            '100%': { transform: 'translateY(-100%)' }
                        },
                        'blink': {
                            '0%, 100%': { opacity: 1 },
                            '50%': { opacity: 0 }
                        },
                        'float': {
                            '0%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' },
                            '100%': { transform: 'translateY(0)' }
                        },
                        'float-delayed': {
                            '0%': { transform: 'translateY(-5px)' },
                            '50%': { transform: 'translateY(5px)' },
                            '100%': { transform: 'translateY(-5px)' }
                        },
                        'grid': {
                            '0%': { transform: 'translateY(0)' },
                            '100%': { transform: 'translateY(-100%)' }
                        }
                    },
                    animation: {
                        'slide-in': 'slide-in 0.3s ease-out',
                        'slide-out': 'slide-out 0.3s ease-in',
                        'blink': 'blink 1s step-end infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'float-delayed': 'float-delayed 6s ease-in-out infinite',
                        'grid': 'grid 20s linear infinite'
                    }
                }
            }
        }
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://unpkg.com/mathlive"></script>
    <link rel="stylesheet" href="https://unpkg.com/mathlive/dist/mathlive.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Shepherd.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/shepherd.js@latest/dist/css/shepherd.css"/>
    <!-- Shepherd.js Theme (e.g., arrows) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/shepherd.js@latest/dist/css/shepherd-theme-arrows.css"/>
    <script src="https://unpkg.com/htmx.org@2.0.4"
        integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+"
        crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            renderMathInElement(document.body, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html'
            });
        });
    </script>
</head>
<body class="min-h-full flex flex-col" {% if session.get('show_confetti') %}data-show-confetti="true"{% endif %}>
    {% set hide_nav_endpoints = ['index', 'index_simple', 'login', 'register', 'onboarding', 'complete_onboarding'] %}
    {% if request.endpoint not in hide_nav_endpoints and session.get('user_id') %}
    <header class="sticky top-0 z-50 bg-white shadow-sm">
        <nav class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="flex h-16 justify-between">
                <div class="flex">
                    <div class="flex flex-shrink-0 items-center">
                        <a href="{{ url_for('index') }}" class="text-xl font-bold {% if request.endpoint == 'index' %}text-indigo-600{% else %}text-gray-900{% endif %}"><i class="fas fa-home mr-1"></i>Vast</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        {% if session.get('user_id') %}
                            <a href="{{ url_for('dashboard') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'dashboard' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium">
                                <i class="fas fa-tachometer-alt mr-1"></i>Dashboard
                            </a>
                        {% endif %}
                        <a href="{{ url_for('vault') }}" class="inline-flex items-center border-b-2 {% if request.endpoint in ['vault', 'load_question'] %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium">
                            <i class="fas fa-box-archive mr-1"></i>Vault
                        </a>
                        {% if session.get('user_id') %}
                            {% set user = User.query.get(session['user_id']) %}
                            {% if user and user.is_admin %}
                                <a href="{{ url_for('teacher_dashboard') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'teacher_dashboard' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-chalkboard-teacher mr-1"></i>Teacher</a>
                                <a href="{{ url_for('admin') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'admin' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-user-shield mr-1"></i>Admin</a>
                                <a href="{{ url_for('bulk_upload') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'bulk_upload' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium">
                                    <i class="fas fa-upload mr-1"></i>Bulk Upload
                                </a>
                            {% endif %}
                        {% endif %}
                        <a href="{{ url_for('dojo') }}" class="inline-flex items-center border-b-2 {% if request.endpoint in ['dojo', 'load_dojo_questions'] %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-dumbbell mr-1"></i>Dojo</a>
                        <a href="{{ url_for('review') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'review' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-clipboard-check mr-1"></i>Review</a>
                        <!--<a href="{{ url_for('submissions') }}" class="inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700"><i class="fas fa-paper-plane mr-1"></i>Submissions</a>-->
                        <a id="nav-groups-link" href="{{ url_for('groups') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'groups' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-users mr-1"></i>Groups</a>
                        <a href="{{ url_for('feed') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'feed' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-rss mr-1"></i>Feed</a>
                        <a href="{{ url_for('notes') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'notes' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-book mr-1"></i>Notes</a>
                        {% if session.get('user_id') %}
                            <a href="{{ url_for('view_clarifications') }}" class="inline-flex items-center border-b-2 {% if request.endpoint == 'view_clarifications' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-question-circle mr-1"></i>Clarifications</a>
                        {% endif %}
                        <a href="{{ url_for('list_problemsets') }}" class="inline-flex items-center border-b-2 {% if request.endpoint in ['list_problemsets', 'view_problemset', 'do_problemset', 'edit_problemset'] %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700{% endif %} px-1 pt-1 text-sm font-medium"><i class="fas fa-list-check mr-1"></i>Problem Sets</a>
                    </div>
                </div>
                <div class="hidden sm:ml-6 sm:flex sm:items-center">
                    {% if session.get('user_id') %}
                        <a href="{{ url_for('user_profile',username=session.get('username')) }}" class="text-sm font-medium text-gray-700 hover:text-gray-900">
                            {{ session.get('username') }}
                        </a>
                        <a href="{{ url_for('logout') }}" class="ml-4 text-sm font-medium text-gray-700 hover:text-gray-900">Logout</a>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="text-sm font-medium text-gray-700 hover:text-gray-900">Login</a>
                        <a href="{{ url_for('register') }}" class="ml-4 text-sm font-medium text-gray-700 hover:text-gray-900">Register</a>
                    {% endif %}
                </div>
                <div class="-mr-2 flex items-center sm:hidden">
                    <button type="button" class="mobile-menu-button inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="menu-icon block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                        </svg>
                        <svg class="close-icon hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Mobile menu -->
        <div class="sm:hidden hidden transition-all duration-300 ease-in-out opacity-0" id="mobile-menu">
            <div class="space-y-1 pb-3 pt-2">
                {% if session.get('user_id') %}
                    <a href="{{ url_for('dashboard') }}" class="block border-l-4 {% if request.endpoint == 'dashboard' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-tachometer-alt mr-1"></i>Dashboard</a>
                {% endif %}
                <a href="{{ url_for('vault') }}" class="block border-l-4 {% if request.endpoint in ['vault', 'load_question'] %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-box-archive mr-1"></i>Vault</a>
                {% if session.get('user_id') %}
                    {% set user = User.query.get(session['user_id']) %}
                    {% if user and user.is_admin %}
                        <a href="{{ url_for('teacher_dashboard') }}" class="block border-l-4 {% if request.endpoint == 'teacher_dashboard' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-chalkboard-teacher mr-1"></i>Teacher</a>
                        <a href="{{ url_for('admin') }}" class="block border-l-4 {% if request.endpoint == 'admin' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-user-shield mr-1"></i>Admin</a>
                        <a href="{{ url_for('bulk_upload') }}" class="block border-l-4 {% if request.endpoint == 'bulk_upload' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium">
                            <i class="fas fa-upload mr-1"></i>Bulk Upload
                        </a>
                    {% endif %}
                {% endif %}
                <a href="{{ url_for('dojo') }}" class="block border-l-4 {% if request.endpoint in ['dojo', 'load_dojo_questions'] %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-dumbbell mr-1"></i>Dojo</a>
                <a href="{{ url_for('review') }}" class="block border-l-4 {% if request.endpoint == 'review' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-clipboard-check mr-1"></i>Review</a>
                <a href="{{ url_for('submissions') }}" class="block border-l-4 {% if request.endpoint == 'submissions' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-paper-plane mr-1"></i>Submissions</a>
                <a id="mobile-nav-groups-link" href="{{ url_for('groups') }}" class="block border-l-4 {% if request.endpoint == 'groups' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-users mr-1"></i>Groups</a>
                <a href="{{ url_for('feed') }}" class="block border-l-4 {% if request.endpoint == 'feed' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-rss mr-1"></i>Feed</a>
                <a href="{{ url_for('notes') }}" class="block border-l-4 {% if request.endpoint == 'notes' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-book mr-1"></i>Notes</a>
                {% if session.get('user_id') %}
                    <a href="{{ url_for('view_clarifications') }}" class="block border-l-4 {% if request.endpoint == 'view_clarifications' %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-question-circle mr-1"></i>Clarifications</a>
                {% endif %}
                <a href="{{ url_for('list_problemsets') }}" class="block border-l-4 {% if request.endpoint in ['list_problemsets', 'view_problemset', 'do_problemset', 'edit_problemset'] %}border-indigo-500 bg-indigo-50 text-indigo-700{% else %}border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700{% endif %} py-2 pl-3 pr-4 text-base font-medium"><i class="fas fa-list-check mr-1"></i>Problem Sets</a>
            </div>
            <div class="border-t border-gray-200 pb-3 pt-4">
                {% if session.get('user_id') %}
                    <div class="flex items-center px-4">
                        <div class="flex-shrink-0">
                            <span class="text-sm font-medium text-gray-700">{{ session.get('username') }}</span>
                        </div>
                        <a href="{{ url_for('logout') }}" class="ml-auto block text-sm font-medium text-gray-700 hover:text-gray-900">Logout</a>
                    </div>
                {% else %}
                    <div class="space-y-1">
                        <a href="{{ url_for('login') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800">Login</a>
                        <a href="{{ url_for('register') }}" class="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800">Register</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </header>
    {% endif %}

    <main class="flex-grow">
        <div class="mx-auto max-w-7xl {% if request.endpoint in hide_nav_endpoints or not session.get('user_id') %}py-0{% else %}py-6{% endif %} sm:px-6 lg:px-8">
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            <div class="space-y-4">
                {% for category, message in messages %}
                <div class="rounded-md p-4 {% if category == 'success' %}bg-green-50 text-green-700{% elif category == 'error' or category == 'danger' %}bg-red-50 text-red-700{% else %}bg-blue-50 text-blue-700{% endif %}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if category == 'success' %}
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            {% elif category == 'error' or category == 'danger' %}
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                            {% else %}
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium">{{ message }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="bg-white border-t border-gray-200">
        <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">&copy; 2024 Vast. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Mobile menu toggle with improved functionality
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.querySelector('.menu-icon');
        const closeIcon = document.querySelector('.close-icon');

        function toggleMobileMenu() {
            const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';
            mobileMenuButton.setAttribute('aria-expanded', !isExpanded);

            if (!isExpanded) {
                mobileMenu.classList.remove('hidden');
                // Force a reflow before adding the opacity
                mobileMenu.offsetHeight;
                mobileMenu.classList.remove('opacity-0');
                mobileMenu.classList.add('opacity-100');
                mobileMenu.classList.add('animate-slide-in');
                mobileMenu.classList.remove('animate-slide-out');
            } else {
                mobileMenu.classList.add('opacity-0');
                mobileMenu.classList.remove('opacity-100');
                mobileMenu.classList.add('animate-slide-out');
                mobileMenu.classList.remove('animate-slide-in');
                // Wait for animation before hiding
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                }, 300);
            }

            menuIcon.classList.toggle('hidden');
            closeIcon.classList.toggle('hidden');
        }

        // Toggle menu on button click
        mobileMenuButton.addEventListener('click', toggleMobileMenu);

        // Close menu when clicking outside
        document.addEventListener('click', (event) => {
            const isClickInside = mobileMenuButton.contains(event.target) || mobileMenu.contains(event.target);
            if (!isClickInside && !mobileMenu.classList.contains('hidden')) {
                toggleMobileMenu();
            }
        });

        // Close menu on window resize (if switching to desktop view)
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 640 && !mobileMenu.classList.contains('hidden')) {
                toggleMobileMenu();
            }
        });

        // Only run timer if user is logged in
        {% if session.get('user_id') %}
        // Load the RobustTimeTracker library
        const script = document.createElement('script');
        script.src = '/static/js/timeme.min.js';
        script.async = true;
        document.head.appendChild(script);

        // Initialize variables for time tracking
        const userId = {{ session.get('user_id') }};
        let timeTracker = null;

        console.log("Robust time tracking initializing for user:", userId);

        // Function to send time updates to server
        function sendTimeToServer(seconds) {
            return fetch('/update_active_time', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    seconds: seconds
                }),
                credentials: 'same-origin'
            })
            .then(response => {
                console.log("RobustTimeTracker: Server response status:", response.status);
                return response.json();
            })
            .then(data => {
                console.log("RobustTimeTracker: Server response data:", data);
                if (data.status === 'success') {
                    console.log("RobustTimeTracker: Time update successful. New active time:", data.active_time);

                    // Update the timer display if on profile page
                    const timerElement = document.getElementById('daily-active-timer');
                    if (timerElement) {
                        const hours = Math.floor(data.active_time / 3600);
                        const minutes = Math.floor((data.active_time % 3600) / 60);
                        const seconds = data.active_time % 60;
                        timerElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    }
                } else {
                    console.error("RobustTimeTracker: Time update failed:", data.message);
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                console.error('RobustTimeTracker: Error updating active time:', error);
                throw error;
            });
        }

        // Initialize RobustTimeTracker when script is loaded
        script.onload = function() {
            console.log("RobustTimeTracker: Script loaded, checking for RobustTimeTracker class");
            console.log("RobustTimeTracker available:", typeof RobustTimeTracker !== 'undefined');

            if (typeof RobustTimeTracker !== 'undefined') {
                console.log("RobustTimeTracker: Initializing time tracker for user:", userId);

                try {
                    timeTracker = RobustTimeTracker.initialize({
                        userId: userId,
                        idleTimeout: 60000, // 60 seconds
                        updateInterval: 15000, // 15 seconds
                        heartbeatInterval: 2000, // 2 seconds
                        onTimeUpdate: sendTimeToServer,
                        onActiveChange: (isActive) => {
                            console.log("RobustTimeTracker: Active state changed to:", isActive);
                        }
                    });

                    console.log("RobustTimeTracker: Time tracker initialized successfully:", timeTracker);
                } catch (error) {
                    console.error("RobustTimeTracker: Error initializing time tracker:", error);
                }
            } else {
                console.error("RobustTimeTracker: Library not found. Make sure the library is loaded correctly.");
                console.log("Available globals:", Object.keys(window).filter(key => key.includes('Time')));
            }
        };

        // Add error handler for script loading
        script.onerror = function() {
            console.error("RobustTimeTracker: Failed to load time tracking script");
        };

        // Clean up when page unloads
        window.addEventListener('beforeunload', () => {
            if (timeTracker) {
                console.log("RobustTimeTracker: Page unloading - cleaning up");
                timeTracker.cleanup();
            }
        });
        {% endif %}
    </script>
    <!-- Shepherd.js JS -->
    <script src="https://cdn.jsdelivr.net/npm/shepherd.js@latest/dist/js/shepherd.min.js"></script>

    <!-- Confetti script for first login of the day -->
    <script src="{{ url_for('static', filename='js/confetti.js') }}"></script>
</body>
</html>
