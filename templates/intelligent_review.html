{% extends "base.html" %}

{% block title %}Intelligent Review - KYM{% endblock %}

{% block head %}
<style>
    .recommendation-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: box-shadow 0.2s ease;
    }
    
    .recommendation-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .recommendation-type {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        margin-bottom: 8px;
    }
    
    .type-critical { background: #ffebee; color: #c62828; }
    .type-reinforcement { background: #fff3e0; color: #ef6c00; }
    .type-mastery { background: #e8f5e8; color: #2e7d32; }
    .type-new { background: #e3f2fd; color: #1565c0; }
    
    .priority-score {
        float: right;
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .difficulty-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 8px;
    }
    
    .difficulty-easy { background: #c8e6c9; color: #2e7d32; }
    .difficulty-medium { background: #fff9c4; color: #f57f17; }
    .difficulty-hard { background: #ffcdd2; color: #c62828; }
    .difficulty-unknown { background: #f5f5f5; color: #757575; }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .stat-card {
        background: white;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        text-align: center;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #1976d2;
    }
    
    .stat-label {
        font-size: 14px;
        color: #666;
        margin-top: 4px;
    }
    
    .category-section {
        margin-bottom: 32px;
    }
    
    .category-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .category-title {
        font-size: 18px;
        font-weight: bold;
        margin-right: 12px;
    }
    
    .category-count {
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
    }
    
    .insights-section {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 24px;
    }
    
    .insight-item {
        margin-bottom: 8px;
        padding: 8px;
        background: white;
        border-radius: 4px;
        border-left: 4px solid #1976d2;
    }
    
    .mode-toggle {
        margin-bottom: 16px;
    }
    
    .btn-toggle {
        margin-right: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Intelligent Review System</h2>
                    <p class="text-muted mb-0">Personalized recommendations based on your learning patterns</p>
                </div>
                <div class="d-flex gap-2">
                    <button id="refresh-recommendations" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <div class="mode-toggle">
                        <a href="{{ url_for('review') }}?mode=intelligent" class="btn btn-primary btn-sm btn-toggle">Intelligent</a>
                        <a href="{{ url_for('review') }}?mode=legacy" class="btn btn-outline-secondary btn-sm btn-toggle">Legacy View</a>
                    </div>
                </div>
            </div>

            <!-- Category Filters -->
            <div class="mb-4">
                <div class="btn-group" role="group" aria-label="Category filters">
                    <button type="button" class="btn btn-outline-secondary btn-sm category-filter active" data-category="all">
                        All Recommendations
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm category-filter" data-category="critical">
                        Critical (1)
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm category-filter" data-category="reinforcement">
                        Reinforcement (2)
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm category-filter" data-category="mastery">
                        Mastery (3)
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm category-filter" data-category="new">
                        New Challenges (4)
                    </button>
                </div>
                <small class="text-muted d-block mt-2">
                    <i class="fas fa-keyboard"></i> Use keyboard shortcuts: R to refresh, 1-4 to filter categories
                </small>
            </div>

            <!-- Daily Progress -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Daily Progress</h6>
                    <small id="progress-text" class="text-muted">0/5 problems completed today</small>
                </div>
                <div class="progress" style="height: 8px;">
                    <div id="daily-progress" class="progress-bar bg-success" role="progressbar"
                         style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            
            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ review_stats.total_items if review_stats else 0 }}</div>
                    <div class="stat-label">Total Items in Review</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ review_stats.due_today if review_stats else 0 }}</div>
                    <div class="stat-label">Due Today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ review_stats.overdue if review_stats else 0 }}</div>
                    <div class="stat-label">Overdue</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ recommendations|length }}</div>
                    <div class="stat-label">Recommendations</div>
                </div>
            </div>
            
            <!-- Learning Insights -->
            {% if learning_insights %}
            <div class="insights-section">
                <h4>Learning Insights</h4>
                {% if learning_insights.weak_areas %}
                <div class="insight-item">
                    <strong>Areas for Improvement:</strong> 
                    {% for area in learning_insights.weak_areas[:3] %}
                        {{ area.topic_name }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if learning_insights.strong_areas %}
                <div class="insight-item">
                    <strong>Strong Areas:</strong> 
                    {% for area in learning_insights.strong_areas[:3] %}
                        {{ area.topic_name }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if learning_insights.overall_performance %}
                <div class="insight-item">
                    <strong>Overall Performance:</strong> 
                    {{ "%.1f"|format(learning_insights.overall_performance.success_rate * 100) }}% success rate
                    ({{ learning_insights.overall_performance.total_attempts }} attempts)
                </div>
                {% endif %}
            </div>
            {% endif %}
            
            <!-- Critical Reviews (Due Today) -->
            {% if todays_reviews %}
            <div class="category-section">
                <div class="category-header">
                    <h3 class="category-title">Critical Reviews</h3>
                    <span class="category-count">{{ todays_reviews|length }} due today</span>
                </div>
                
                {% for review in todays_reviews[:5] %}
                <div class="recommendation-card" data-type="critical" data-part-id="{{ review.part.id }}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="recommendation-type type-critical">Critical</span>
                            <span class="priority-score">{{ "%.0f"|format(review.priority_score) }}</span>
                        </div>
                    </div>
                    
                    <h5>{{ review.question.title }}</h5>
                    <p class="text-muted mb-2">{{ review.part.description[:100] }}{% if review.part.description|length > 100 %}...{% endif %}</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                {{ review.topic.name if review.topic else 'Unknown Topic' }} • 
                                {{ review.subject.name if review.subject else 'Unknown Subject' }}
                            </small>
                            {% if review.days_overdue > 0 %}
                            <span class="difficulty-badge difficulty-hard">{{ review.days_overdue }} days overdue</span>
                            {% else %}
                            <span class="difficulty-badge difficulty-medium">Due today</span>
                            {% endif %}
                        </div>
                        <a href="{{ url_for('question', question_id=review.question.id) }}" class="btn btn-primary btn-sm">
                            Review Now
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Personalized Recommendations -->
            {% if recommendations %}
            <div class="category-section">
                <div class="category-header">
                    <h3 class="category-title">Personalized Recommendations</h3>
                    <span class="category-count">{{ recommendations|length }} suggestions</span>
                </div>
                
                {% for rec in recommendations %}
                <div class="recommendation-card" data-type="{{ rec.type }}" data-part-id="{{ rec.part_id }}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="recommendation-type type-{{ rec.type }}">
                                {% if rec.type == 'critical' %}Critical
                                {% elif rec.type == 'reinforcement' %}Reinforcement
                                {% elif rec.type == 'mastery' %}Mastery
                                {% elif rec.type == 'new' %}New Challenge
                                {% endif %}
                            </span>
                            <span class="priority-score">{{ "%.0f"|format(rec.priority_score) }}</span>
                        </div>
                    </div>
                    
                    <h5>{{ rec.question.title }}</h5>
                    <p class="text-muted mb-2">{{ rec.reason }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                {{ rec.topic.name if rec.topic else 'Unknown Topic' }} • 
                                {{ rec.subject.name if rec.subject else 'Unknown Subject' }}
                            </small>
                            <span class="difficulty-badge difficulty-{{ rec.estimated_difficulty }}">
                                {{ rec.estimated_difficulty|title }} difficulty
                            </span>
                        </div>
                        <a href="{{ url_for('question', question_id=rec.question_id) }}" class="btn btn-outline-primary btn-sm">
                            Try Problem
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Empty State -->
            {% if not recommendations and not todays_reviews %}
            <div class="text-center py-5">
                <h4>No recommendations available</h4>
                <p class="text-muted">Complete some problems to get personalized recommendations!</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">Go to Dashboard</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Enhanced JavaScript for intelligent review interface
document.addEventListener('DOMContentLoaded', function() {
    // Add click tracking for recommendations
    document.querySelectorAll('.recommendation-card a').forEach(function(link) {
        link.addEventListener('click', function() {
            // Track recommendation clicks for effectiveness analysis
            const card = this.closest('.recommendation-card');
            const questionId = this.href.split('/').pop();

            // Send analytics data to track recommendation effectiveness
            fetch('/api/review-feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question_id: parseInt(questionId),
                    part_id: parseInt(card.dataset.partId || '0'),
                    was_helpful: true, // Assume clicking means it was helpful
                    action: 'clicked'
                })
            }).catch(err => console.log('Analytics tracking failed:', err));

            console.log('Recommendation clicked:', questionId);
        });
    });

    // Add refresh functionality
    const refreshBtn = document.getElementById('refresh-recommendations');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Refreshing...';

            // Reload the page to get fresh recommendations
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    }

    // Add category filtering
    const categoryFilters = document.querySelectorAll('.category-filter');
    categoryFilters.forEach(function(filter) {
        filter.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.dataset.category;

            // Hide all recommendation cards
            document.querySelectorAll('.recommendation-card').forEach(card => {
                card.style.display = 'none';
            });

            // Show only cards of selected category
            if (category === 'all') {
                document.querySelectorAll('.recommendation-card').forEach(card => {
                    card.style.display = 'block';
                });
            } else {
                document.querySelectorAll(`.recommendation-card[data-type="${category}"]`).forEach(card => {
                    card.style.display = 'block';
                });
            }

            // Update active filter
            categoryFilters.forEach(f => f.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Press 'r' to refresh recommendations
        if (e.key === 'r' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const refreshBtn = document.getElementById('refresh-recommendations');
            if (refreshBtn && document.activeElement.tagName !== 'INPUT') {
                e.preventDefault();
                refreshBtn.click();
            }
        }

        // Press number keys 1-4 to filter by category
        if (e.key >= '1' && e.key <= '4' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const categories = ['critical', 'reinforcement', 'mastery', 'new'];
            const categoryIndex = parseInt(e.key) - 1;
            const categoryFilter = document.querySelector(`[data-category="${categories[categoryIndex]}"]`);
            if (categoryFilter && document.activeElement.tagName !== 'INPUT') {
                e.preventDefault();
                categoryFilter.click();
            }
        }
    });

    // Add tooltips for difficulty badges
    const difficultyBadges = document.querySelectorAll('.difficulty-badge');
    difficultyBadges.forEach(function(badge) {
        badge.title = getDifficultyTooltip(badge.textContent.toLowerCase());
    });

    function getDifficultyTooltip(difficulty) {
        const tooltips = {
            'easy': 'Based on your performance, this should be straightforward',
            'medium': 'This might require some effort based on your current level',
            'hard': 'This will be challenging - consider reviewing related topics first',
            'unknown': 'Difficulty unknown - not enough data from similar problems'
        };
        return tooltips[difficulty.split(' ')[0]] || 'Difficulty assessment';
    }

    // Add progress tracking
    let completedToday = 0;
    const progressBar = document.getElementById('daily-progress');
    const progressText = document.getElementById('progress-text');

    function updateProgress() {
        const dailyGoal = 5; // Could be user-configurable
        const percentage = Math.min((completedToday / dailyGoal) * 100, 100);

        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (progressText) {
            progressText.textContent = `${completedToday}/${dailyGoal} problems completed today`;
        }
    }

    // Initialize progress
    updateProgress();
});
</script>
{% endblock %}
