{% extends "base.html" %}

{% block title %}Student Clarifications{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Student Clarifications</h1>
        <p class="mt-2 text-gray-600">Review and respond to student clarification requests.</p>
    </div>

    <!-- Filter tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button onclick="filterClarifications('all')" 
                    class="filter-tab active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-filter="all">
                All Clarifications
            </button>
            <button onclick="filterClarifications('pending')" 
                    class="filter-tab whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-filter="pending">
                Pending
            </button>
            <button onclick="filterClarifications('answered')" 
                    class="filter-tab whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-filter="answered">
                Answered
            </button>
        </nav>
    </div>

    {% if clarifications %}
        <div class="space-y-6" id="clarifications-container">
            {% for clarification in clarifications %}
            <div class="clarification-item bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden" 
                 data-status="{{ clarification.status }}">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900">{{ clarification.subject }}</h3>
                            <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                                <span>
                                    <i class="fas fa-user mr-1"></i>
                                    {{ clarification.student.username }}
                                </span>
                                <span>
                                    <i class="fas fa-book mr-1"></i>
                                    Question #{{ clarification.question_id }}
                                    {% if clarification.part %}
                                        - Part {{ clarification.part_id }}
                                    {% endif %}
                                </span>
                                <span>
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ clarification.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                </span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if clarification.status == 'pending' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>Pending
                                </span>
                            {% elif clarification.status == 'answered' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Answered
                                </span>
                            {% elif clarification.status == 'closed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times mr-1"></i>Closed
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="border-l-4 border-blue-400 pl-4 mb-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Student's Question:</h4>
                        <p class="text-gray-700 whitespace-pre-wrap">{{ clarification.message }}</p>
                    </div>

                    {% if clarification.context_data %}
                        {% set context = clarification.context_data | from_json %}
                        {% if context.part_specific and context.user_answer %}
                            <div class="border-l-4 border-gray-400 pl-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Student's Latest Answer (Part {{ clarification.part_id }}):</h4>
                                <p class="text-gray-700 whitespace-pre-wrap">{{ context.user_answer }}</p>
                                <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                                    {% if context.score is not none %}
                                        <span>Score: {{ context.score }}</span>
                                    {% endif %}
                                    <span>Submitted: {{ context.submission_time }}</span>
                                </div>
                            </div>
                        {% elif not context.part_specific and context.all_submissions %}
                            <div class="border-l-4 border-gray-400 pl-4 mb-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Student's Recent Submissions ({{ context.total_submissions }} total):</h4>
                                <div class="space-y-3">
                                    {% for submission in context.all_submissions %}
                                        <div class="bg-gray-50 p-3 rounded">
                                            <div class="flex justify-between items-start mb-2">
                                                <span class="text-xs font-medium text-gray-600">Part {{ submission.part_id }}</span>
                                                {% if submission.score is not none %}
                                                    <span class="text-xs text-gray-500">Score: {{ submission.score }}</span>
                                                {% endif %}
                                            </div>
                                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ submission.answer }}</p>
                                            <p class="text-xs text-gray-500 mt-1">{{ submission.submission_time }}</p>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}

                    {% if clarification.teacher_response %}
                        <div class="border-l-4 border-green-400 pl-4 mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">
                                Your Response:
                                {% if clarification.teacher %}
                                    <span class="text-sm font-normal text-gray-600">by {{ clarification.teacher.username }}</span>
                                {% endif %}
                            </h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ clarification.teacher_response }}</p>
                            <p class="text-xs text-gray-500 mt-2">
                                Responded on {{ clarification.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </p>
                        </div>
                    {% endif %}

                    <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                        <a href="{{ url_for('load_question', question_id=clarification.question_id) }}"
                           class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            View Question
                        </a>
                        
                        <div class="flex space-x-2">
                            {% if clarification.status == 'pending' %}
                                <button onclick="openResponseModal({{ clarification.id }})"
                                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-reply mr-1"></i>Respond
                                </button>
                            {% endif %}
                            
                            {% if clarification.status != 'closed' %}
                                <button onclick="closeClarification({{ clarification.id }})"
                                        class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-times mr-1"></i>Close
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <i class="fas fa-question-circle text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No clarifications yet</h3>
            <p class="text-gray-600">Students haven't sent any clarification requests.</p>
        </div>
    {% endif %}
</div>

<!-- Response Modal -->
<div id="response-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Respond to Clarification</h3>
                <button onclick="closeResponseModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form id="response-form" class="space-y-4">
                <input type="hidden" id="response-clarification-id" value="">
                
                <div>
                    <label for="teacher-response" class="block text-sm font-medium text-gray-700 mb-1">
                        Your Response <span class="text-red-500">*</span>
                    </label>
                    <textarea id="teacher-response" 
                              name="response" 
                              required
                              rows="6"
                              placeholder="Provide a helpful response to the student's question..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" 
                            onclick="closeResponseModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </button>
                    <button type="button" 
                            onclick="sendResponse()"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-paper-plane mr-2"></i>Send Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.filter-tab {
    border-color: transparent;
    color: #6B7280;
}

.filter-tab.active {
    border-color: #3B82F6;
    color: #3B82F6;
}

.filter-tab:hover {
    color: #374151;
    border-color: #D1D5DB;
}
</style>

<script>
function filterClarifications(status) {
    const items = document.querySelectorAll('.clarification-item');
    const tabs = document.querySelectorAll('.filter-tab');
    
    // Update active tab
    tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.filter === status) {
            tab.classList.add('active');
        }
    });
    
    // Filter items
    items.forEach(item => {
        if (status === 'all' || item.dataset.status === status) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function openResponseModal(clarificationId) {
    document.getElementById('response-clarification-id').value = clarificationId;
    document.getElementById('response-modal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeResponseModal() {
    document.getElementById('response-modal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    document.getElementById('response-form').reset();
}

function sendResponse() {
    const clarificationId = document.getElementById('response-clarification-id').value;
    const response = document.getElementById('teacher-response').value.trim();
    
    if (!response) {
        alert('Please enter a response.');
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#response-modal button[onclick="sendResponse()"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
    submitBtn.disabled = true;

    fetch(`/api/clarifications/${clarificationId}/respond`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ response: response })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to send response. Please try again.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function closeClarification(clarificationId) {
    if (!confirm('Are you sure you want to close this clarification?')) {
        return;
    }

    fetch(`/api/clarifications/${clarificationId}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to close clarification. Please try again.');
    });
}
</script>
{% endblock %}
