{% extends 'base.html' %}

{% block title %}Edit Question{% endblock %}

{% block head %}
{{ super() }}
<!-- Add required libraries for Markdown and LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>

<!-- Custom styles for LaTeX rendering -->
<style>
    /* Ensure inline LaTeX is properly aligned with text */
    .katex-inline {
        display: inline-block;
        vertical-align: middle;
    }

    /* Add some spacing around inline LaTeX */
    .live-preview .katex {
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing */
    .live-preview .katex-display {
        margin: 1em 0;
    }

    /* Make sure inline LaTeX doesn't break the line flow */
    .live-preview p {
        display: block;
        line-height: 1.5;
        margin: 1em 0;
    }

    /* Ensure inline elements are properly aligned */
    .live-preview .katex-html {
        display: inline-block;
        vertical-align: middle;
    }

    /* Style for our custom inline math wrapper */
    .inline-math {
        display: inline;
        vertical-align: baseline;
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing and alignment */
    .katex-display {
        margin: 1em 0;
        text-align: center;
    }

    /* Add some basic styling to the preview area */
    .live-preview {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        min-height: 50px;
        padding: 10px;
        overflow-wrap: break-word;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        margin-top: 0.5rem;
    }

    /* Style for code blocks in markdown */
    .live-preview pre {
        background-color: #f5f5f5;
        padding: 0.5em;
        border-radius: 0.25em;
        overflow-x: auto;
    }

    /* Style for inline code in markdown */
    .live-preview code {
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 0.25em;
        font-family: monospace;
    }

    /* Style for paragraphs in the preview */
    .live-preview p {
        margin-bottom: 1em;
    }

    /* Style for headings in the preview */
    .live-preview h1, .live-preview h2, .live-preview h3,
    .live-preview h4, .live-preview h5, .live-preview h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: bold;
    }

    /* Style for lists in the preview */
    .live-preview ul, .live-preview ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl">
        <!-- Header -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center">Edit Question</h1>
            </div>
        </div>

        <!-- Question Form -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
            <div class="p-6">
                <form method="POST" enctype="multipart/form-data" class="space-y-6">
                    <!-- Subject and Topic Selection -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">Subject</label>
                            <div class="mt-2">
                                <select id="subject" name="subject_id" onchange="loadTopics(this.value)" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Subject</option>
                                    {% for subject in subjects %}
                                        <option value="{{ subject.id }}" {% if question.topic and subject.id == question.topic.subject_id %}selected{% endif %}>
                                            {{ subject.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="topic" class="block text-sm font-medium leading-6 text-gray-900">Topic</label>
                            <div class="mt-2">
                                <select id="topic" name="topic_id" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Topic</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Question Title -->
                    <div>
                        <div class="flex justify-between items-center">
                            <label for="title" class="block text-sm font-medium leading-6 text-gray-900">Question Title</label>
                            <button type="button" onclick="generateQuestionTitle()"
                                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Generate Title
                            </button>
                        </div>
                        <div class="mt-2">
                            <textarea id="title" name="title" rows="2" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ question.title }}</textarea>
                        </div>
                        <!-- Loading indicator for title generation -->
                        <div id="title-loading" class="hidden mt-2 flex items-center text-sm text-gray-600">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating title...
                        </div>
                    </div>

                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium leading-6 text-gray-900">Source</label>
                        <div class="mt-2">
                            <textarea id="source" name="source" rows="2"
                                      class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ question.source }}</textarea>
                        </div>
                    </div>

                    <!-- Question Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium leading-6 text-gray-900">Question Description</label>
                        <div class="mt-2">
                            <textarea id="description" name="description" rows="3"
                                      class="latex-content block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                      placeholder="Optional: Overall question description or context">{{ question.description }}</textarea>
                        </div>
                        <div id="preview-description" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                            <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                            <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                    onclick="document.getElementById('description').value = ''; updatePreview(document.getElementById('description'));">
                                Clear
                            </button>
                        </div>
                    </div>

                    <!-- Parts -->
                    <div class="space-y-6">
                        <h2 class="text-lg font-semibold text-gray-900">Question Parts</h2>
                        {% for part in question.parts %}
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-md font-medium text-gray-900 mb-4">Part {{ loop.index }}</h3>

                            <!-- Part Description -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea id="part_{{ part.id }}_description" name="part_{{ part.id }}_description" rows="2" required
                                          class="latex-content mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ part.description }}</textarea>
                                <div id="preview-part-{{ part.id }}-description" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                                <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                    <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                                    <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                            onclick="document.getElementById('part_{{ part.id }}_description').dispatchEvent(new Event('input'))">
                                        Refresh Preview
                                    </button>
                                </div>
                            </div>

                            <!-- Input Type Selector -->
                            <div class="mb-4">
                                <label for="input_type_{{ part.id }}" class="block text-sm font-medium text-gray-700">Input Type</label>
                                <div class="mt-2">
                                    <select id="input_type_{{ part.id }}" name="input_type_{{ part.id }}" onchange="toggleInputType({{ part.id }})" required
                                            class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                        <option value="saq" {% if part.input_type != 'mcq' %}selected{% endif %}>Short Answer Question (SAQ)</option>
                                        <option value="mcq" {% if part.input_type == 'mcq' %}selected{% endif %}>Multiple Choice Question (MCQ)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- SAQ Answer Section -->
                            <div id="saq_section_{{ part.id }}" class="answer-section mb-4 {% if part.input_type == 'mcq' %}hidden{% endif %}">
                                <label for="part_{{ part.id }}_answer" class="block text-sm font-medium text-gray-700">Answer</label>
                                <textarea id="part_{{ part.id }}_answer" name="part_{{ part.id }}_answer" rows="2" {% if part.input_type != 'mcq' %}required{% endif %}
                                          class="latex-content mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ part.answer }}</textarea>
                                <div id="preview-part-{{ part.id }}-answer" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                                <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                    <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                                    <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                            onclick="document.getElementById('part_{{ part.id }}_answer').dispatchEvent(new Event('input'))">
                                        Refresh Preview
                                    </button>
                                </div>
                            </div>

                            <!-- MCQ Options Section -->
                            <div id="mcq_section_{{ part.id }}" class="answer-section mb-4 {% if part.input_type != 'mcq' %}hidden{% endif %}">
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-gray-700">Multiple Choice Options</label>
                                    <button type="button" onclick="addMcqOption({{ part.id }})" class="text-green-600 hover:text-green-800 text-sm">
                                        <i class="fas fa-plus"></i> Add Option
                                    </button>
                                </div>
                                <div id="mcq_options_{{ part.id }}" class="space-y-3">
                                    {% if part.input_type == 'mcq' and part.options %}
                                        {% for option in part.options %}
                                        <div class="mcq-option">
                                            <div class="flex items-center gap-2 mb-1">
                                                <input type="radio" id="mcq_correct_{{ part.id }}_{{ loop.index0 }}"
                                                       name="mcq_correct_{{ part.id }}" value="{{ loop.index0 }}"
                                                       {% if option.is_correct %}checked{% endif %}
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                                                <textarea name="mcq_option_{{ part.id }}[]" required
                                                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                       placeholder="Option text">{{ option.description }}</textarea>
                                                <button type="button" onclick="removeMcqOption(this, {{ part.id }})" class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <!-- Default options if none exist -->
                                        <div class="mcq-option">
                                            <div class="flex items-center gap-2 mb-1">
                                                <input type="radio" id="mcq_correct_{{ part.id }}_0" name="mcq_correct_{{ part.id }}" value="0" checked
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                                                <textarea name="mcq_option_{{ part.id }}[]" required
                                                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                       placeholder="Option text">Option 1</textarea>
                                                <button type="button" onclick="removeMcqOption(this, {{ part.id }})" class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                        </div>
                                        <div class="mcq-option">
                                            <div class="flex items-center gap-2 mb-1">
                                                <input type="radio" id="mcq_correct_{{ part.id }}_1" name="mcq_correct_{{ part.id }}" value="1"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                                                <textarea name="mcq_option_{{ part.id }}[]" required
                                                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                       placeholder="Option text">Option 2</textarea>
                                                <button type="button" onclick="removeMcqOption(this, {{ part.id }})" class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                        </div>
                                    {% endif %}
                                </div>
                                <p class="mt-1 text-sm text-gray-500">Select the radio button next to the correct answer.</p>
                            </div>

                            <!-- Part Score -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_score" class="block text-sm font-medium text-gray-700">Score</label>
                                <input type="number" id="part_{{ part.id }}_score" name="part_{{ part.id }}_score" min="0" required
                                       value="{{ part.score }}"
                                       class="mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            </div>

                            <!-- Part Attachment -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_attachment" class="block text-sm font-medium text-gray-700">Attachments</label>

                                <!-- Existing Attachments -->
                                {% if part.attachments %}
                                <div class="mt-2 mb-4 space-y-3">
                                    <p class="text-xs text-gray-500">Uncheck to delete an attachment</p>
                                    {% for attachment in part.attachments %}
                                    <div class="p-3 border border-gray-200 rounded-md">
                                        <div class="flex items-center mb-2">
                                            <input type="checkbox" id="attachment-{{ attachment.id }}" name="{{ attachment.filename }}-{{ part.id }}"
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" checked>
                                            <label for="attachment-{{ attachment.id }}" class="ml-2 text-sm text-gray-700">
                                                {{ attachment.filename }}
                                            </label>
                                        </div>
                                        <img src="{{ url_for('serve.serve_file', filename=attachment.filename) }}"
                                             class="rounded-lg max-h-[200px] w-auto border border-gray-200"
                                             alt="Attachment: {{ attachment.filename }}">
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <!-- Upload New Attachments -->
                                <div class="mt-3">
                                    <label for="part_{{ part.id }}_attachment" class="block text-sm font-medium text-gray-700 mb-1">Upload New Attachments</label>
                                    <input type="file" multiple id="part_{{ part.id }}_attachment" name="part_{{ part.id }}_attachment" accept=".jpg,.jpeg,.png,.gif"
                                           class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                </div>
                            </div>

                            <!-- Marking Points -->
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-gray-700">Marking Points</label>
                                    <div class="flex space-x-2">
                                        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm generate-marking-points-btn"
                                                onclick="generateMarkingPoints({{ part.id }})">
                                            <i class="fas fa-magic"></i> Generate
                                            <span class="ml-1 loading-spinner-{{ part.id }} hidden"><i class="fas fa-spinner fa-spin"></i></span>
                                        </button>
                                        <button type="button" onclick="addMarkingPoint({{ part.id }})" class="text-green-600 hover:text-green-800 text-sm">
                                            <i class="fas fa-plus"></i> Add
                                        </button>
                                    </div>
                                </div>
                                <div id="marking_points_{{ part.id }}" class="space-y-2">
                                    {% for point in part.marking_points %}
                                    <div class="flex flex-col mb-3">
                                        <div class="flex gap-2 mb-1">
                                            <textarea name="part_{{ part.id }}_marking_points" required
                                                   class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                   placeholder="Marking point description">{{ point.description }}</textarea>
                                            <input type="number" name="part_{{ part.id }}_marking_scores" value="{{ point.score }}" step="0.1" min="0" required
                                                   class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                   placeholder="Score">
                                            <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div id="preview-part-{{ part.id }}-mp-{{ loop.index }}" class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                let topicSelect = document.getElementById('topic');
                topicSelect.innerHTML = "<option selected disabled>Select Topic</option>";
                data.topics.forEach(function(topic) {
                    let option = document.createElement("option");
                    option.value = topic.id;
                    option.text = topic.name;
                    {% if question.topic %}
                    if (topic.id == {{ question.topic_id }}) {
                        option.selected = true;
                    }
                    {% endif %}
                    topicSelect.appendChild(option);
                });
            });
    }

    function addMarkingPoint(partId) {
        const container = document.getElementById(`marking_points_${partId}`);
        const mpCount = container.querySelectorAll('.flex-col').length + 1;
        const div = document.createElement('div');
        div.className = 'flex flex-col mb-3';
        div.innerHTML = `
            <div class="flex gap-2 mb-1">
                <textarea name="part_${partId}_marking_points" required
                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Marking point description"></textarea>
                <input type="number" name="part_${partId}_marking_scores" value="1" step="0.1" min="0" required
                       class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Score">
                <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div id="preview-part-${partId}-mp-${mpCount}" class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
        `;
        container.appendChild(div);

        // Initialize the new textarea for live preview
        const newTextarea = div.querySelector('.latex-content');
        if (newTextarea) {
            newTextarea.addEventListener('input', function() {
                updatePreview(this);
            });
        }
    }

    function removeMarkingPoint(button) {
        button.closest('.flex-col').remove();
    }

    // Toggle between SAQ and MCQ input types
    function toggleInputType(partId) {
        const inputType = document.getElementById(`input_type_${partId}`).value;
        const saqSection = document.getElementById(`saq_section_${partId}`);
        const mcqSection = document.getElementById(`mcq_section_${partId}`);

        if (inputType === 'saq') {
            saqSection.classList.remove('hidden');
            mcqSection.classList.add('hidden');

            // Make SAQ answer required and MCQ options not required
            document.getElementById(`part_${partId}_answer`).required = true;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partId} textarea`);
            mcqOptions.forEach(option => {
                option.required = false;
            });

            // Make MCQ radio buttons not required
            const mcqRadios = document.querySelectorAll(`input[name="mcq_correct_${partId}"]`);
            mcqRadios.forEach(radio => {
                radio.required = false;
            });
        } else {
            saqSection.classList.add('hidden');
            mcqSection.classList.remove('hidden');

            // Make MCQ options required and SAQ answer not required
            document.getElementById(`part_${partId}_answer`).required = false;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partId} textarea`);
            mcqOptions.forEach(option => {
                option.required = true;
            });

            // Make at least one MCQ radio button required
            const mcqRadios = document.querySelectorAll(`input[name="mcq_correct_${partId}"]`);
            mcqRadios.forEach(radio => {
                radio.required = true;
            });
        }
    }

    // Add a new MCQ option
    function addMcqOption(partId) {
        const container = document.getElementById(`mcq_options_${partId}`);
        const optionCount = container.querySelectorAll('.mcq-option').length;

        const div = document.createElement('div');
        div.className = 'mcq-option';
        div.innerHTML = `
            <div class="flex items-center gap-2 mb-1">
                <input type="radio" id="mcq_correct_${partId}_${optionCount}" name="mcq_correct_${partId}" value="${optionCount}" required
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                <textarea name="mcq_option_${partId}[]" required
                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Option text"></textarea>
                <button type="button" onclick="removeMcqOption(this, ${partId})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
        `;
        container.appendChild(div);

        // Initialize the preview for the new textarea
        setTimeout(function() {
            const newTextarea = div.querySelector('.latex-content');
            if (newTextarea) {
                updatePreview(newTextarea);
                newTextarea.addEventListener('input', function() {
                    updatePreview(this);
                });
            }
        }, 10);
    }

    // Remove an MCQ option
    function removeMcqOption(button, partId) {
        const optionElement = button.closest('.mcq-option');
        const container = document.getElementById(`mcq_options_${partId}`);

        // Don't allow removing if there are only 2 options left
        if (container.querySelectorAll('.mcq-option').length <= 2) {
            alert('A multiple choice question must have at least 2 options.');
            return;
        }

        // Remove the option
        optionElement.remove();

        // Update the value attributes of the radio buttons to match their new positions
        const options = container.querySelectorAll('.mcq-option');
        options.forEach((option, index) => {
            const radio = option.querySelector('input[type="radio"]');
            radio.id = `mcq_correct_${partId}_${index}`;
            radio.value = index;
        });
    }

    // Generate marking points via API
    async function generateMarkingPoints(partId) {
        const descriptionTextarea = document.getElementById(`part_${partId}_description`);
        const inputType = document.getElementById(`input_type_${partId}`).value;
        const scoreInput = document.getElementById(`part_${partId}_score`);
        const mpContainer = document.getElementById(`marking_points_${partId}`);
        const loadingSpinner = document.querySelector(`.loading-spinner-${partId}`);

        if (!descriptionTextarea || !scoreInput || !mpContainer) {
            console.error("Could not find necessary elements for part:", partId);
            alert("Error: Could not find elements for this part.");
            return;
        }

        const partDescription = descriptionTextarea.value;
        const partScore = scoreInput.value;
        let partAnswer = '';

        // Get the answer based on input type
        if (inputType === 'saq') {
            const answerTextarea = document.getElementById(`part_${partId}_answer`);
            if (!answerTextarea) {
                console.error("Could not find answer textarea for part:", partId);
                alert("Error: Could not find answer textarea for this part.");
                return;
            }
            partAnswer = answerTextarea.value;

            if (!partDescription || !partAnswer || !partScore) {
                alert("Please ensure the part description, answer, and score are filled before generating marking points.");
                return;
            }
        } else if (inputType === 'mcq') {
            // For MCQ, we'll use the correct option as the answer
            const correctOptionRadio = document.querySelector(`input[name="mcq_correct_${partId}"]:checked`);
            if (!correctOptionRadio) {
                alert("Please select a correct option for this multiple choice question.");
                return;
            }

            const correctOptionIndex = correctOptionRadio.value;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partId} textarea`);

            if (mcqOptions.length <= correctOptionIndex) {
                alert("Error: Selected correct option does not exist.");
                return;
            }

            partAnswer = mcqOptions[correctOptionIndex].value;

            if (!partDescription || !partAnswer || !partScore) {
                alert("Please ensure the part description, all options, and score are filled before generating marking points.");
                return;
            }
        }

        // Show loading state
        const generateButton = loadingSpinner.parentElement;
        generateButton.disabled = true;
        loadingSpinner.classList.remove('hidden');

        try {
            const response = await fetch('/generate_marking_points', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    partDescription: partDescription,
                    partAnswer: partAnswer,
                    partScore: parseInt(partScore) // Ensure score is sent as a number
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: 'Unknown server error' }));
                throw new Error(`Server error: ${response.status} - ${errorData.message || 'Failed to generate marking points'}`);
            }

            const generatedPoints = await response.json();

            if (!Array.isArray(generatedPoints)) {
                throw new Error("Invalid response format from server. Expected an array.");
            }

            // Clear existing marking points
            mpContainer.innerHTML = '';

            // Add new marking points from response
            generatedPoints.forEach((mp, index) => {
                const div = document.createElement('div');
                div.className = 'flex flex-col mb-3';
                div.innerHTML = `
                    <div class="flex gap-2 mb-1">
                        <textarea name="part_${partId}_marking_points" required
                               class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                               placeholder="Marking point description">${mp.description || ''}</textarea>
                        <input type="number" name="part_${partId}_marking_scores" value="${mp.score || 0}" step="0.1" min="0" required
                               class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                               placeholder="Score">
                        <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div id="preview-part-${partId}-mp-gen-${index}" class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                `;
                mpContainer.appendChild(div);

                // Initialize the new textarea for live preview
                const newTextarea = div.querySelector('.latex-content');
                if (newTextarea) {
                    updatePreview(newTextarea);
                    newTextarea.addEventListener('input', function() {
                        updatePreview(this);
                    });
                }
            });

        } catch (error) {
            console.error('Error generating marking points:', error);
            alert(`Failed to generate marking points: ${error.message}`);
        } finally {
            // Hide loading state
            generateButton.disabled = false;
            loadingSpinner.classList.add('hidden');
        }
    }

    // Load topics for the selected subject on page load
    document.addEventListener('DOMContentLoaded', function() {
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect.value) {
            loadTopics(subjectSelect.value);
        }

        // Initialize live preview functionality
        initLivePreview();

        // Initialize input type toggles for all parts
        document.querySelectorAll('[id^="input_type_"]').forEach(select => {
            const partId = select.id.split('_')[2];
            toggleInputType(partId);
        });
    });

    // Function to initialize live preview
    function initLivePreview() {
        console.log('Initializing live preview functionality');

        // Check if required libraries are loaded
        if (typeof marked === 'undefined') {
            console.error('Marked library is not loaded.');
            return;
        }

        if (typeof katex === 'undefined') {
            console.error('KaTeX library is not loaded.');
            return;
        }

        console.log('Libraries loaded successfully');

        // Configure marked
        marked.use({
            breaks: true,  // Convert line breaks to <br>
            gfm: true,    // Enable GitHub Flavored Markdown
            mangle: false, // Don't mangle email addresses
            headerIds: false // Don't add IDs to headers
        });

        // Function to render LaTeX with KaTeX
        function renderLatex(latex, displayMode) {
            try {
                // Render the LaTeX expression
                const rendered = katex.renderToString(latex, {
                    displayMode: displayMode,
                    throwOnError: false,
                    output: 'html'
                });

                // For inline LaTeX, add a special class to help with styling
                if (!displayMode) {
                    return `<span class="inline-math">${rendered}</span>`;
                }

                return rendered;
            } catch (error) {
                console.error('Error rendering LaTeX:', error, latex);
                return `<span class="text-red-500">Error rendering LaTeX: ${latex}</span>`;
            }
        }

        // Process text containing both Markdown and LaTeX
        function processText(text) {
            if (!text) return '';

            try {
                // Our approach: Process LaTeX first, then Markdown

                // Step 1: Extract and save LaTeX blocks
                let placeholders = [];
                let processedText = text;

                // Extract display math ($$...$$)
                processedText = processedText.replace(/\$\$(.*?)\$\$/gs, function(match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: true
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Extract inline math ($...$)
                processedText = processedText.replace(/\$([^\$\n]+?)\$/g, function(match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: false
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Step 2: Parse the text as Markdown
                let html = marked.parse(processedText);

                // Step 3: Replace placeholders with rendered LaTeX
                placeholders.forEach(placeholder => {
                    const rendered = renderLatex(placeholder.latex, placeholder.displayMode);
                    html = html.replace(
                        new RegExp(`<span id="${placeholder.id}" class="latex-placeholder"></span>`, 'g'),
                        rendered
                    );
                });

                return html;
            } catch (error) {
                console.error('Error in custom processing:', error);
                return `<p class="text-red-500">Error processing text</p><pre>${text}</pre>`;
            }
        }

        // Function to update the preview
        window.updatePreview = function(textarea) {
            // Find the preview element
            let previewElement = null;

            // For marking points, the preview div has a special ID format
            if (textarea.name.includes('marking_points')) {
                const mpContainer = textarea.closest('.flex-col');
                if (mpContainer) {
                    previewElement = mpContainer.querySelector('.live-preview');
                }
            } else {
                // For part description and answer, find the preview by ID
                const textareaId = textarea.id;
                const previewId = textareaId.replace('part_', 'preview-part-').replace('_', '-');
                previewElement = document.getElementById(previewId);
            }

            if (!previewElement) {
                console.error('Preview element not found for textarea:', textarea);
                return;
            }

            const text = textarea.value || '';

            // Process the text and update the preview
            const processedHTML = processText(text);
            previewElement.innerHTML = processedHTML || '<p class="text-gray-400 italic">Preview will appear here</p>';
        };

        // Debounce function to limit update frequency
        function debounce(func, delay) {
            let timeoutId;
            return function(...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                }, delay);
            };
        }

        // Create debounced version of updatePreview
        const debouncedUpdatePreview = debounce(updatePreview, 300);

        // Find all textareas with the latex-content class
        const textareas = document.querySelectorAll('.latex-content');
        console.log('Found textareas:', textareas.length);

        // Initialize each textarea
        textareas.forEach(textarea => {
            console.log('Initializing textarea:', textarea.id || textarea.name);
            // Initial render
            updatePreview(textarea);

            // Add input event listener
            textarea.addEventListener('input', function() {
                debouncedUpdatePreview(this);
            });
        });
    }

    // Generate question title using AI
    async function generateQuestionTitle() {
        const titleTextarea = document.getElementById('title');
        const loadingIndicator = document.getElementById('title-loading');
        const generateButton = document.querySelector('button[onclick="generateQuestionTitle()"]');

        if (!titleTextarea) {
            alert('Error: Could not find title field.');
            return;
        }

        // Show loading state
        loadingIndicator.classList.remove('hidden');
        generateButton.disabled = true;
        generateButton.innerHTML = `
            <svg class="animate-spin w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Generating...
        `;

        try {
            // Collect question description
            const questionDescription = document.getElementById('description')?.value || '';

            // Collect parts descriptions
            const partsDescriptions = [];
            const partDescriptionTextareas = document.querySelectorAll('textarea[name^="part_"][name$="_description"]');
            partDescriptionTextareas.forEach(textarea => {
                if (textarea.value.trim()) {
                    partsDescriptions.push(textarea.value.trim());
                }
            });

            if (!questionDescription && partsDescriptions.length === 0) {
                alert('Please add a question description or part descriptions before generating a title.');
                return;
            }

            // Make API call to generate title
            const response = await fetch('/generate_question_title', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    questionDescription: questionDescription,
                    partsDescriptions: partsDescriptions
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Update the title field with the generated title
                titleTextarea.value = data.title;

                // Show success message briefly
                const successMessage = document.createElement('div');
                successMessage.className = 'mt-2 text-sm text-green-600';
                successMessage.textContent = 'Title generated successfully!';
                titleTextarea.parentNode.appendChild(successMessage);

                setTimeout(() => {
                    successMessage.remove();
                }, 3000);
            } else {
                alert(`Error generating title: ${data.message}`);
            }

        } catch (error) {
            console.error('Error generating title:', error);
            alert('An error occurred while generating the title. Please try again.');
        } finally {
            // Hide loading state
            loadingIndicator.classList.add('hidden');
            generateButton.disabled = false;
            generateButton.innerHTML = `
                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Generate Title
            `;
        }
    }
</script>
{% endblock %}
