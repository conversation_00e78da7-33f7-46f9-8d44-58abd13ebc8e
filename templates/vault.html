{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl">
        <!-- Header -->
        <div id="vault-header" class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center">Problem Vault</h1>
            </div>
        </div>

        <!-- Filter Form -->
        <div id="filter-form-section" class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <form method="GET" action="{{ url_for('vault') }}" class="space-y-6">
                    <div>
                        <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">Subject</label>
                        <div class="mt-2" id="subject-filter-container">
                            <select id="subject" name="subject_id" onchange="loadTopics(this.value)" required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                <option selected disabled>Select Subject</option>
                                {% for subject in subjects %}
                                <option value="{{ subject.id }}">{{ subject.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="topic" class="block text-sm font-medium leading-6 text-gray-900">Topic</label>
                        <div class="mt-2" id="topic-filter-container">
                            <select id="topic" name="topic_id" required
                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                <option selected disabled>Select Topic</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <button type="submit" id="filter-button"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Filter Problems
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Question List -->
        <div id="question-list-section" class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Questions</h2>

                <!-- Card layout for all screen sizes -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for question in questions %}

                    <a href="{{ url_for('load_question', question_id=question.id) }}"
                       class="block bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50 transition-colors">
                        <div class="p-4">
                            <div class="flex flex-col space-y-2">
                                <div class="flex justify-between items-start">
                                    <div class="text-sm font-medium text-gray-900">
                                        {% if question.topic %}
                                            {{ question.topic.subject.name }}
                                        {% else %}
                                            <span class="text-gray-400">No subject</span>
                                        {% endif %}
                                    </div>
                                    {% if question.status == 2 %}
                                        <span class="inline-flex items-center rounded-md bg-green-50 px-1.5 py-0.5 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">Correct</span>
                                    {% elif question.status == 1 %}
                                        <span class="inline-flex items-center rounded-md bg-yellow-50 px-1.5 py-0.5 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20">Partial</span>
                                    {% else %}
                                        <span class="inline-flex items-center rounded-md bg-red-50 px-1.5 py-0.5 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">Not Attempted</span>
                                    {% endif %}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {% if question.topic %}
                                        {{ question.topic.name }}
                                    {% else %}
                                        <span class="text-gray-400">No topic</span>
                                    {% endif %}
                                </div>
                                <div class="text-sm text-gray-900 break-words">{{ question.source }} - {{ question.title }} </div>
                            </div>
                        </div>
                    </a>

                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                let topicSelect = document.getElementById('topic');
                topicSelect.innerHTML = "<option selected disabled>Select Topic</option>";
                data.topics.forEach(function (topic) {
                    let option = document.createElement("option");
                    option.value = topic.id;
                    option.text = topic.name;
                    topicSelect.appendChild(option);
                });
            });
    }

    let currentLoggedInTourInstance = null; // To manage the logged-in tour instance

    function initializeAndStartLoggedInTour() {
        // If a tour instance exists and is active, cancel it before starting a new one
        if (currentLoggedInTourInstance && typeof currentLoggedInTourInstance.isActive === 'function' && currentLoggedInTourInstance.isActive()) {
            currentLoggedInTourInstance.cancel();
        }

        const tour = new Shepherd.Tour({
            useModalOverlay: true,
            defaultStepOptions: {
                classes: 'bg-white rounded-lg shadow-xl border border-gray-300 text-gray-700', // Updated classes
                scrollTo: { behavior: 'smooth', block: 'center' }
            }
        });

        // Check if this is coming from onboarding completion
        const urlParams = new URLSearchParams(window.location.search);
        const isFromOnboarding = urlParams.get('start_tour') === 'true';

        // Add a special welcome step if coming from onboarding
        if (isFromOnboarding) {
            tour.addStep({
                id: 'onboarding-welcome',
                text: `
                    <div class="text-center">
                        <div class="text-4xl mb-4">🎉</div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Welcome to Vast!</h3>
                        <p class="text-gray-600 mb-4">Your profile has been set up successfully. Let's take a quick tour to show you around!</p>
                        <p class="text-sm text-gray-500">This tour will guide you through the main features of the platform.</p>
                    </div>
                `,
                attachTo: { element: '#vault-header', on: 'bottom' },
                buttons: [
                    {
                        action() { return this.next(); },
                        text: 'Start Tour',
                        classes: 'px-6 py-3 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() {
                            localStorage.setItem('globalTourCompleted', 'true');
                            localStorage.setItem('vaultShepherdTourCompleted', 'true');
                            localStorage.setItem('dojoShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });
        }

        tour.addStep({
            id: 'welcome',
            text: 'Welcome to the Problem Vault! This contains all the questions from the wild - real exam questions and challenging problems from various sources.',
            attachTo: { element: '#vault-header', on: 'bottom' },
            buttons: [
                {
                    action() { return this.next(); },
                    text: 'Next',
                    classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                },
                {
                    action() {
                        localStorage.setItem('globalTourCompleted', 'true');
                        localStorage.setItem('vaultShepherdTourCompleted', 'true');
                        localStorage.setItem('dojoShepherdTourCompleted', 'true');
                        return this.complete();
                    },
                    text: 'Skip Tour',
                    classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                }
            ]
        });

        tour.addStep({
            id: 'filter-intro',
            text: 'Use these filters to find specific exam questions by subject and topic. Browse the questions below and click on any to start working on it.',
            attachTo: { element: '#filter-form-section', on: 'bottom' },
            buttons: [
                {
                    action() { return this.back(); },
                    text: 'Back',
                    classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                },
                {
                    action() { return this.next(); },
                    text: 'Next',
                    classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                },
                {
                    action() {
                        localStorage.setItem('globalTourCompleted', 'true');
                        localStorage.setItem('vaultShepherdTourCompleted', 'true');
                        localStorage.setItem('dojoShepherdTourCompleted', 'true');
                        return this.complete();
                    },
                    text: 'Skip Tour',
                    classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                }
            ]
        });




        tour.addStep({
            id: 'dojo-intro',
            text: 'Next, visit the Dojo to practice classic questions that are essential to master. The Dojo contains carefully curated fundamental problems.',
            attachTo: { element: '#nav-dojo-link', on: 'bottom' }, // Assumes #nav-dojo-link exists in base.html
            buttons: [
                {
                    action() { return this.back(); },
                    text: 'Back',
                    classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                },
                {
                    action() {
                        // Mark vault tour as completed
                        localStorage.setItem('vaultShepherdTourCompleted', 'true');
                        // Signal that the dojo tour should start on the next page load
                        localStorage.setItem('startDojoTourNext', 'true');
                        // Redirect to dojo page
                        window.location.href = "{{ url_for('dojo') }}";
                        // Complete this (vault) tour instance
                        return this.complete();
                    },
                    text: 'Go to Dojo & Continue Tour',
                    classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                },
                {
                    action() {
                        localStorage.setItem('globalTourCompleted', 'true');
                        localStorage.setItem('vaultShepherdTourCompleted', 'true');
                        localStorage.setItem('dojoShepherdTourCompleted', 'true');
                        return this.complete();
                    },
                    text: 'Skip Tour',
                    classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                }
            ]
        });

        tour.on('complete', function() {
            // vaultShepherdTourCompleted is now set directly in the button action before redirect
            // or by the tour's own completion if it finishes without redirecting.
            if (!localStorage.getItem('startDojoTourNext')) { // Only set if not redirecting to dojo tour
                localStorage.setItem('vaultShepherdTourCompleted', 'true');
            }
            currentLoggedInTourInstance = null; // Clear instance on complete
        });
        tour.on('cancel', function() {
            // Optionally mark as completed on cancel if you don't want it to reappear
            // localStorage.setItem('vaultShepherdTourCompleted', 'true');
            currentLoggedInTourInstance = null; // Clear instance on cancel
        });

        currentLoggedInTourInstance = tour; // Store the newly created tour instance
        tour.start();
    }

    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = {{ session.get('user_id') is not none | tojson }};
        const loginPageUrl = "{{ url_for('login') }}"; // Used for guest tour redirect

        // Check if we should start the tour (coming from onboarding)
        const urlParams = new URLSearchParams(window.location.search);
        const shouldStartTour = urlParams.get('start_tour') === 'true';

        if (shouldStartTour) {
            // Clear all tour completion flags to ensure the full tour sequence runs
            localStorage.removeItem('globalTourCompleted');
            localStorage.removeItem('vaultShepherdTourCompleted');
            localStorage.removeItem('dojoShepherdTourCompleted');

            // Clean up the URL to remove the start_tour parameter
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }

        if (!isLoggedIn) {
            // Tour for non-logged-in users (Guest Tour) - This logic remains unchanged
            const guestTour = new Shepherd.Tour({
                useModalOverlay: true,
                defaultStepOptions: {
                    classes: 'bg-white rounded-lg shadow-xl border border-gray-300 text-gray-700', // Updated classes
                    scrollTo: { behavior: 'smooth', block: 'center' }
                }
            });

            guestTour.addStep({
                id: 'guest-welcome',
                text: 'Welcome to the Problem Vault! You can browse problems here.',
                attachTo: { element: '#vault-header', on: 'bottom' },
                buttons: [
                    {
                        action() { return this.next(); },
                        text: 'Next',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() { return this.complete(); },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });

            guestTour.addStep({
                id: 'guest-login-prompt',
                text: 'To save your progress, attempt questions, and access all features, please log in.',
                attachTo: { element: '#filter-form-section', on: 'bottom' },
                buttons: [
                    {
                        action() { return this.back(); },
                        text: 'Back',
                        classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                    },
                    {
                        action() { window.location.href = loginPageUrl; return this.complete(); },
                        text: 'Go to Login Page',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() { return this.cancel(); },
                        text: 'Maybe Later',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });
            guestTour.start();

        } else {
            // Logic for logged-in users
            if ((!localStorage.getItem('vaultShepherdTourCompleted') && !localStorage.getItem('globalTourCompleted')) || shouldStartTour) {
                initializeAndStartLoggedInTour();
            }

            const restartButton = document.getElementById('restart-tour-button');
            // The restart button functionality is now moved to the landing page (index.html)
        }
    });
</script>
{% endblock %}
