{% extends 'base.html' %}

{% block title %}Dojo Management - Admin{% endblock %}

{% block head %}
{{ super() }}
<style>
    .dojo-enabled {
        background-color: #dcfce7;
        border-color: #16a34a;
    }
    
    .dojo-disabled {
        background-color: #fef2f2;
        border-color: #dc2626;
    }
    
    .prerequisite-badge {
        background-color: #e0e7ff;
        color: #3730a3;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        margin: 0.125rem;
        display: inline-block;
    }
    
    .filter-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Dojo Management</h1>
                <a href="{{ url_for('admin') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Admin
                </a>
            </div>
            <p class="mt-2 text-gray-600">Manage dojo questions and their prerequisite relationships</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="subject-filter" class="block text-sm font-medium text-white mb-2">Filter by Subject</label>
                    <select id="subject-filter" class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600">
                        <option value="">All Subjects</option>
                        {% for subject in subjects %}
                        <option value="{{ subject.id }}">{{ subject.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="dojo-filter" class="block text-sm font-medium text-white mb-2">Filter by Dojo Status</label>
                    <select id="dojo-filter" class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600">
                        <option value="">All Questions</option>
                        <option value="true">Dojo Questions Only</option>
                        <option value="false">Non-Dojo Questions Only</option>
                    </select>
                </div>
                <div>
                    <label for="search-filter" class="block text-sm font-medium text-white mb-2">Search Questions</label>
                    <input type="text" id="search-filter" placeholder="Search by title..." 
                           class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600">
                </div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Questions</h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject/Topic</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dojo Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prerequisites</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Relevance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="questions-table">
                        {% for question in questions %}
                        <tr class="question-row" 
                            data-subject-id="{{ question.topic.subject_id }}" 
                            data-is-dojo="{{ question.is_dojo|lower }}"
                            data-title="{{ question.title|lower }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ question.title }}</div>
                                <div class="text-sm text-gray-500">ID: {{ question.id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ question.topic.subject.name }}</div>
                                <div class="text-sm text-gray-500">{{ question.topic.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                           {% if question.is_dojo %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                    {% if question.is_dojo %}Dojo Enabled{% else %}Dojo Disabled{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap">
                                    {% for prerequisite in question.prerequisites %}
                                    <span class="prerequisite-badge">
                                        {{ prerequisite.prerequisite_question.title }}
                                    </span>
                                    {% endfor %}
                                    {% if question.prerequisites|length == 0 %}
                                    <span class="text-sm text-gray-500">No prerequisites</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap">
                                    {% for relevance in question.relevant_questions %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-1 mb-1">
                                        {{ relevance.relevant_question.title }}
                                        <span class="ml-1 text-purple-600">
                                            {% for i in range(relevance.strength) %}★{% endfor %}
                                        </span>
                                    </span>
                                    {% endfor %}
                                    {% if question.relevant_questions|length == 0 %}
                                    <span class="text-sm text-gray-500">No relevance</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <!-- Toggle Dojo Status -->
                                    <form method="POST" action="{{ url_for('toggle_dojo_question', question_id=question.id) }}" class="inline">
                                        <button type="submit" 
                                                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md
                                                       {% if question.is_dojo %}text-red-700 bg-red-100 hover:bg-red-200{% else %}text-green-700 bg-green-100 hover:bg-green-200{% endif %}">
                                            {% if question.is_dojo %}Disable Dojo{% else %}Enable Dojo{% endif %}
                                        </button>
                                    </form>
                                    
                                    <!-- Manage Prerequisites -->
                                    <a href="{{ url_for('manage_prerequisites', question_id=question.id) }}"
                                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                                        Prerequisites
                                    </a>

                                    <!-- Manage Relevance -->
                                    <a href="{{ url_for('manage_question_relevance', question_id=question.id) }}"
                                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200">
                                        Relevance
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const subjectFilter = document.getElementById('subject-filter');
    const dojoFilter = document.getElementById('dojo-filter');
    const searchFilter = document.getElementById('search-filter');
    const questionRows = document.querySelectorAll('.question-row');

    function filterQuestions() {
        const selectedSubject = subjectFilter.value;
        const selectedDojoStatus = dojoFilter.value;
        const searchTerm = searchFilter.value.toLowerCase();

        questionRows.forEach(row => {
            let show = true;

            // Filter by subject
            if (selectedSubject && row.dataset.subjectId !== selectedSubject) {
                show = false;
            }

            // Filter by dojo status
            if (selectedDojoStatus && row.dataset.isDojo !== selectedDojoStatus) {
                show = false;
            }

            // Filter by search term
            if (searchTerm && !row.dataset.title.includes(searchTerm)) {
                show = false;
            }

            row.style.display = show ? '' : 'none';
        });
    }

    subjectFilter.addEventListener('change', filterQuestions);
    dojoFilter.addEventListener('change', filterQuestions);
    searchFilter.addEventListener('input', filterQuestions);
});
</script>
{% endblock %}
