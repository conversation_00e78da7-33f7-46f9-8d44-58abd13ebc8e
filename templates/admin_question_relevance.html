{% extends 'base.html' %}

{% block title %}Manage Question Relevance - Admin{% endblock %}

{% block head %}
{{ super() }}
<style>
    .relevance-card {
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        background-color: #f9fafb;
    }
    
    .relevance-card:hover {
        border-color: #6366f1;
        background-color: #f0f9ff;
    }
    
    .relevance-card.selected {
        border-color: #6366f1;
        background-color: #eff6ff;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
    
    .relevance-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .relevance-type-badge {
        background-color: #ddd6fe;
        color: #5b21b6;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        margin-left: 0.5rem;
    }
    
    .strength-indicator {
        display: inline-flex;
        align-items: center;
        margin-left: 0.5rem;
    }
    
    .strength-star {
        color: #fbbf24;
        margin-right: 0.125rem;
    }
    
    .strength-star.empty {
        color: #d1d5db;
    }
    
    .relevance-controls {
        display: none;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #e5e7eb;
    }
    
    .relevance-card.selected .relevance-controls {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="relevance-header rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <div class="flex justify-between items-start">
                <div class="text-white">
                    <h1 class="text-3xl font-bold tracking-tight mb-2">Manage Question Relevance</h1>
                    <p class="text-lg opacity-90">{{ question.title }}</p>
                    <p class="text-sm opacity-75">{{ question.topic.subject.name }} - {{ question.topic.name }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ url_for('admin_dojo') }}" 
                       class="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Back to Dojo Management
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Question Details -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
        <div class="p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-3">Question Details</h2>
            <div class="text-sm text-gray-600">
                <p><strong>ID:</strong> {{ question.id }}</p>
                <p><strong>Description:</strong> {{ question.description[:200] }}{% if question.description|length > 200 %}...{% endif %}</p>
            </div>
        </div>
    </div>

    <!-- Relevance Form -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
        <div class="p-6">
            <form method="POST" action="{{ url_for('manage_question_relevance', question_id=question.id) }}">
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        Select Relevant Questions for "{{ question.title }}"
                    </h3>
                    <p class="text-sm text-gray-600 mb-6">
                        Choose which questions contain relevant knowledge for solving this question. 
                        Questions from the same subject ({{ question.topic.subject.name }}) are shown below.
                    </p>
                </div>

                {% if potential_relevant %}
                <div class="space-y-3 mb-6">
                    {% for potential in potential_relevant %}
                    {% set is_selected = potential.id in current_relevance %}
                    {% set relevance_info = current_relevance.get(potential.id, {'type': 'general', 'strength': 1}) %}
                    
                    <div class="relevance-card {% if is_selected %}selected{% endif %}" 
                         onclick="toggleRelevance({{ potential.id }})">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           id="relevant-{{ potential.id }}" 
                                           name="relevant_questions" 
                                           value="{{ potential.id }}"
                                           {% if is_selected %}checked{% endif %}
                                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="relevant-{{ potential.id }}" class="ml-3 flex-1 cursor-pointer">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ potential.title }}
                                            {% if is_selected %}
                                            <span class="relevance-type-badge">{{ relevance_info.type }}</span>
                                            <span class="strength-indicator">
                                                {% for i in range(1, 6) %}
                                                <span class="strength-star {% if i <= relevance_info.strength %}{% else %}empty{% endif %}">★</span>
                                                {% endfor %}
                                            </span>
                                            {% endif %}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            Topic: {{ potential.topic.name }} | ID: {{ potential.id }}
                                        </div>
                                        {% if potential.description %}
                                        <div class="text-xs text-gray-400 mt-1">
                                            {{ potential.description[:150] }}{% if potential.description|length > 150 %}...{% endif %}
                                        </div>
                                        {% endif %}
                                    </label>
                                </div>
                                
                                <!-- Relevance Controls -->
                                <div class="relevance-controls">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">
                                                Relevance Type
                                            </label>
                                            <select name="relevance_type_{{ potential.id }}" 
                                                    class="block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500">
                                                <option value="general" {% if relevance_info.type == 'general' %}selected{% endif %}>General Knowledge</option>
                                                <option value="similar_concept" {% if relevance_info.type == 'similar_concept' %}selected{% endif %}>Similar Concept</option>
                                                <option value="related_topic" {% if relevance_info.type == 'related_topic' %}selected{% endif %}>Related Topic</option>
                                                <option value="prerequisite_knowledge" {% if relevance_info.type == 'prerequisite_knowledge' %}selected{% endif %}>Prerequisite Knowledge</option>
                                                <option value="application" {% if relevance_info.type == 'application' %}selected{% endif %}>Application/Extension</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">
                                                Relevance Strength (1-5)
                                            </label>
                                            <select name="relevance_strength_{{ potential.id }}" 
                                                    class="block w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500">
                                                {% for i in range(1, 6) %}
                                                <option value="{{ i }}" {% if relevance_info.strength == i %}selected{% endif %}>
                                                    {{ i }} - {% if i == 1 %}Weak{% elif i == 2 %}Mild{% elif i == 3 %}Moderate{% elif i == 4 %}Strong{% else %}Very Strong{% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-500">
                        <span id="selected-count">{{ current_relevance|length }}</span> relevant questions selected
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ url_for('admin_dojo') }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Save Relevance Relationships
                        </button>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No Other Questions Available</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        There are no other questions in {{ question.topic.subject.name }} that can be marked as relevant.
                    </p>
                    <div class="mt-6">
                        <a href="{{ url_for('admin_dojo') }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                            Back to Dojo Management
                        </a>
                    </div>
                </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>

<script>
function toggleRelevance(questionId) {
    const checkbox = document.getElementById(`relevant-${questionId}`);
    const card = checkbox.closest('.relevance-card');
    
    checkbox.checked = !checkbox.checked;
    
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const checkedBoxes = document.querySelectorAll('input[name="relevant_questions"]:checked');
    document.getElementById('selected-count').textContent = checkedBoxes.length;
}

// Initialize count on page load
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});
</script>
{% endblock %}
