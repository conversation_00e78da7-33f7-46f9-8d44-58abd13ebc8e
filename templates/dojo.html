{% extends "base.html" %}

{% block content %}
<div class="relative overflow-hidden">
    <!-- Hero Section with Animated Background -->
    <div class="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 py-12">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        
        <!-- Animated Shapes -->
        <div class="absolute top-20 left-20 w-64 h-64 bg-white/10 rounded-full mix-blend-overlay blur-3xl animate-blob"></div>
        <div class="absolute bottom-10 right-20 w-72 h-72 bg-pink-500/10 rounded-full mix-blend-overlay blur-3xl animate-blob animation-delay-2000"></div>
        
        <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-white tracking-tight mb-4 animate-fade-in">Dojo</h1>
                <p class="max-w-2xl mx-auto text-lg text-white/80 animate-fade-in animation-delay-300">
                    Master your knowledge through focused practice and exploration
                </p>
            </div>
        </div>
    </div>

    <div class="mx-auto max-w-3xl px-4 sm:px-6 lg:px-8 -mt-8">
        <!-- Subjects Panel -->
        <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
            <div class="p-6">
                <div class="flex items-center mb-6">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                        <i class="fas fa-book"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Subjects</h2>
                </div>
                
                <div class="space-y-2">
                    {% for subject in subjects %}
                    <div class="group subject-container" data-subject-id="{{ subject.id }}">
                        <button 
                            value="{{ subject.id }}" 
                            class="w-full px-4 py-3 flex items-center justify-between text-left rounded-lg transition-all duration-200 hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 subject-button"
                            onclick="toggleTopics(this, {{ subject.id }})">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white mr-3 transform transition-transform duration-300 group-hover:scale-110">
                                    <i class="fas fa-book"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ subject.name }}</span>
                            </div>
                            <i class="fas fa-chevron-down text-gray-400 group-hover:text-indigo-600 transition-transform duration-300 chevron-icon"></i>
                        </button>
                        
                        <div id="topicsContainer{{ subject.id }}" class="hidden mt-2 ml-11 pl-4 border-l-2 border-indigo-100 space-y-1 topics-container">
                            <div class="py-2 flex items-center justify-center">
                                <div class="animate-spin h-5 w-5 text-indigo-600">
                                    <i class="fas fa-circle-notch"></i>
                                </div>
                                <span class="ml-2 text-sm text-gray-500">Loading topics...</span>
                            </div>
                            <ul class="space-y-1" id="topicsList{{ subject.id }}">
                                <!-- Topics will be loaded here -->
                            </ul>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }
    
    /* Blob animation */
    @keyframes blob {
        0% {
            transform: translate(0px, 0px) scale(1);
        }
        33% {
            transform: translate(30px, -50px) scale(1.1);
        }
        66% {
            transform: translate(-20px, 20px) scale(0.9);
        }
        100% {
            transform: translate(0px, 0px) scale(1);
        }
    }
    
    .animate-blob {
        animation: blob 7s infinite;
    }
    
    .animation-delay-2000 {
        animation-delay: 2s;
    }
    
    /* Fade in animation */
    @keyframes fadeIn {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.8s ease-out forwards;
    }
    
    .animation-delay-300 {
        animation-delay: 0.3s;
    }
    
    /* Topic item animations */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(-10px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .topic-item {
        animation: slideIn 0.3s ease-out forwards;
    }

    /* Progress bar styles */
    .topic-completed {
        background-color: #f0fdf4;
        border-left: 3px solid #10b981;
    }

    .topic-in-progress {
        background-color: #fffbeb;
        border-left: 3px solid #f59e0b;
    }

    .topic-not-started {
        background-color: #f8fafc;
        border-left: 3px solid #e2e8f0;
    }

    .topic-empty {
        background-color: #f9fafb;
        border-left: 3px solid #d1d5db;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation to cards
        const cards = document.querySelectorAll('.rounded-xl');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease-out';
            card.style.transitionDelay = `${index * 0.1}s`;
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 300);
        });
    });

    // Shepherd.js tour for the dojo page
    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = {{ session.get('user_id') is not none | tojson }};
        const dojoHeader = document.querySelector('h1'); // Main "Dojo" header
        const subjectsPanel = document.querySelector('.bg-white.rounded-xl'); // Subjects panel
        const firstSubjectButton = document.querySelector('.subject-button'); // First subject button

        let isContinuation = false;
        if (localStorage.getItem('startDojoTourNext') === 'true') {
            localStorage.removeItem('startDojoTourNext'); // Consume the signal
            localStorage.removeItem('dojoShepherdTourCompleted'); // Ensure this tour runs
            isContinuation = true;
        }

        const tourCompleted = localStorage.getItem('dojoShepherdTourCompleted');
        const globalTourCompleted = localStorage.getItem('globalTourCompleted');

        // Start tour if logged in AND (it's a continuation OR tour hasn't been completed) AND global tour not completed
        // AND there's a header to attach the first step to.
        if (isLoggedIn && (isContinuation || (!tourCompleted && !globalTourCompleted)) && dojoHeader) {
            const tour = new Shepherd.Tour({
                useModalOverlay: true,
                defaultStepOptions: {
                    classes: 'bg-white rounded-lg shadow-xl border border-gray-300 text-gray-700',
                    scrollTo: { behavior: 'smooth', block: 'center' }
                }
            });

            // Step 1: Welcome to Dojo
            tour.addStep({
                id: 'dojo-welcome',
                text: 'Welcome to the Dojo! This contains all the classic questions that are essential to master. These are carefully curated fundamental problems that will build your foundation.',
                attachTo: { element: dojoHeader, on: 'bottom' },
                buttons: [
                    {
                        action() { return this.next(); },
                        text: 'Next',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() {
                            localStorage.setItem('globalTourCompleted', 'true');
                            localStorage.setItem('vaultShepherdTourCompleted', 'true');
                            localStorage.setItem('dojoShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });

            // Step 2: Try a DOJO Question
            tour.addStep({
                id: 'try-dojo-question',
                text: `
                    <div class="text-center">
                        <div class="text-3xl mb-3">🥋</div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Let's try a DOJO question!</h3>
                        <p class="text-gray-600 mb-4">Experience the DOJO firsthand by attempting a random question from core chemistry topics.</p>
                        <div id="tour-question-loading" class="hidden">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto mb-2"></div>
                            <p class="text-sm text-gray-500">Loading question...</p>
                        </div>
                        <div id="tour-question-info" class="hidden">
                            <div class="bg-blue-50 rounded-lg p-3 mb-3">
                                <p class="text-sm font-medium text-blue-800" id="tour-question-title"></p>
                                <p class="text-xs text-blue-600" id="tour-question-topic"></p>
                            </div>
                        </div>
                    </div>
                `,
                attachTo: { element: dojoHeader, on: 'bottom' },
                buttons: [
                    {
                        action() { return this.back(); },
                        text: 'Back',
                        classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                    },
                    {
                        action() {
                            // Get a random DOJO question and navigate to it
                            const loadingDiv = document.getElementById('tour-question-loading');
                            const infoDiv = document.getElementById('tour-question-info');
                            const titleEl = document.getElementById('tour-question-title');
                            const topicEl = document.getElementById('tour-question-topic');

                            loadingDiv.classList.remove('hidden');

                            fetch('/api/get-random-tour-dojo-question')
                                .then(response => response.json())
                                .then(data => {
                                    loadingDiv.classList.add('hidden');
                                    if (data.success) {
                                        titleEl.textContent = data.question.title;
                                        topicEl.textContent = `Topic: ${data.question.topic_display_name}`;
                                        infoDiv.classList.remove('hidden');

                                        // Navigate to the question after a short delay
                                        setTimeout(() => {
                                            // Mark tour as completed since user is trying a question
                                            localStorage.setItem('dojoShepherdTourCompleted', 'true');
                                            localStorage.setItem('globalTourCompleted', 'true');
                                            window.location.href = `/vault/${data.question.id}`;
                                        }, 2000);
                                    } else {
                                        alert('Could not load a DOJO question. Continuing with tour...');
                                        return this.next();
                                    }
                                })
                                .catch(error => {
                                    console.error('Error loading DOJO question:', error);
                                    loadingDiv.classList.add('hidden');
                                    alert('Could not load a DOJO question. Continuing with tour...');
                                    return this.next();
                                });
                        },
                        text: 'Try a Question',
                        classes: 'px-4 py-2 bg-green-600 text-white text-sm font-semibold rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2'
                    },
                    {
                        action() { return this.next(); },
                        text: 'Skip & Continue',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });



            // Step 3: Subject interaction
            if (firstSubjectButton) {
                tour.addStep({
                    id: 'subject-interaction',
                    text: 'Browse through different subjects and click on any subject to expand it and see the available topics. Each topic contains classic problems that are fundamental to understanding that area.',
                    attachTo: { element: firstSubjectButton, on: 'bottom' },
                    buttons: [
                        {
                            action() { return this.back(); },
                            text: 'Back',
                            classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                        },
                        {
                            action() {
                                localStorage.setItem('dojoShepherdTourCompleted', 'true');
                                return this.complete();
                            },
                            text: 'Finish Tour',
                            classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                        }
                    ]
                });
            }

            tour.on('complete', function() {
                localStorage.setItem('dojoShepherdTourCompleted', 'true');
            });
            tour.on('cancel', function() {
                localStorage.setItem('dojoShepherdTourCompleted', 'true'); // Also mark as completed on cancel
            });

            tour.start();
        } else if (isLoggedIn && isContinuation && !dojoHeader) {
            // If it's a continuation but the main header isn't found, mark as completed.
            localStorage.setItem('dojoShepherdTourCompleted', 'true');
        }
    });

    function toggleTopics(button, subjectId) {
        const topicsContainer = document.getElementById(`topicsContainer${subjectId}`);
        const chevron = button.querySelector('.chevron-icon');
        
        // Reset all subjects to inactive state
        document.querySelectorAll('.subject-button').forEach(btn => {
            btn.classList.remove('bg-indigo-50');
        });
        
        document.querySelectorAll('.chevron-icon').forEach(icon => {
            icon.style.transform = 'rotate(0deg)';
        });
        
        document.querySelectorAll('.topics-container').forEach(container => {
            if (container.id !== `topicsContainer${subjectId}`) {
                container.classList.add('hidden');
            }
        });
        
        // Toggle visibility
        const isHidden = topicsContainer.classList.contains('hidden');
        topicsContainer.classList.toggle('hidden');
        
        // Rotate chevron and highlight active subject
        if (isHidden) {
            chevron.style.transform = 'rotate(180deg)';
            button.classList.add('bg-indigo-50');
        } else {
            chevron.style.transform = 'rotate(0deg)';
            button.classList.remove('bg-indigo-50');
        }
        
        // Load topics if not already loaded
        if (isHidden && !topicsContainer.dataset.loaded) {
            loadTopics(subjectId);
            topicsContainer.dataset.loaded = 'true';
        }
    }

    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                const topicList = document.getElementById(`topicsList${subjectId}`);
                topicList.innerHTML = "";
                
                // Hide loading indicator
                const loadingIndicator = topicList.parentElement.querySelector('div');
                loadingIndicator.style.display = 'none';

                data.topics.forEach(function (topic, index) {
                    const topicUrl = `/dojo/${encodeURIComponent(topic.name)}`;

                    let progressContent = '';
                    let statusClass = '';

                    // Create progress bar and status
                    if (topic.dojo_question_count !== undefined) {
                        if (topic.dojo_question_count > 0) {
                            const progressPercentage = topic.progress_percentage || 0;
                            const completedCount = topic.completed_count || 0;
                            const totalCount = topic.dojo_question_count;

                            // Determine progress bar color
                            let progressColor = 'bg-gray-300';
                            let progressFillColor = 'bg-indigo-500';

                            if (progressPercentage >= 100) {
                                progressFillColor = 'bg-green-500';
                                statusClass = 'topic-completed';
                            } else if (progressPercentage > 0) {
                                progressFillColor = 'bg-yellow-500';
                                statusClass = 'topic-in-progress';
                            } else {
                                statusClass = 'topic-not-started';
                            }

                            progressContent = `
                                <div class="mt-2">
                                    <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
                                        <span>${completedCount}/${totalCount} completed</span>
                                        <span>${progressPercentage}%</span>
                                    </div>
                                    <div class="w-full ${progressColor} rounded-full h-2">
                                        <div class="${progressFillColor} h-2 rounded-full transition-all duration-300" style="width: ${progressPercentage}%"></div>
                                    </div>
                                </div>
                            `;
                        } else {
                            progressContent = '<div class="mt-2"><span class="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-600/20">No questions available</span></div>';
                            statusClass = 'topic-empty';
                        }
                    }

                    const li = document.createElement("li");
                    li.className = `group topic-item ${statusClass}`;
                    li.style.animationDelay = `${index * 0.05}s`;
                    li.innerHTML = `
                        <a href="${topicUrl}" class="block p-3 rounded-md hover:bg-indigo-50 transition-all duration-200">
                            <div class="flex items-center">
                                <i class="fas fa-file-alt text-indigo-400 group-hover:text-indigo-600 mr-3 transition-colors duration-200"></i>
                                <div class="flex-1">
                                    <span class="text-sm font-medium text-gray-900">${topic.name}</span>
                                    ${progressContent}
                                </div>
                            </div>
                        </a>`;
                    topicList.appendChild(li);
                });
            })
            .catch(error => {
                console.error('Error loading topics:', error);
                const topicList = document.getElementById(`topicsList${subjectId}`);
                const loadingIndicator = topicList.parentElement.querySelector('div');
                loadingIndicator.innerHTML = '<div class="text-red-500"><i class="fas fa-exclamation-circle mr-2"></i>Failed to load topics</div>';
            });
    }
</script>
{% endblock %}
