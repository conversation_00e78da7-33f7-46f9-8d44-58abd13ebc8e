{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 min-h-screen bg-gradient-to-b from-white to-gray-50">
    <div class="mx-auto max-w-4xl">
        <!-- <PERSON>er with gradient background -->
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl shadow-xl mb-8">
            <div class="px-6 py-12 sm:px-12">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-white tracking-tight">
                        Review Your Progress
                    </h1>
                    <p class="mt-2 text-indigo-100 max-w-2xl mx-auto">
                        Track your learning journey and identify areas for improvement
                    </p>
                </div>
            </div>
        </div>

        <!-- Filters Card - Redesigned with better spacing and animations -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden mb-10 transform transition-all duration-300 hover:shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">Filter Your Reviews</h2>
                <p class="mt-1 text-sm text-gray-500">Narrow down your submissions by subject and performance</p>
            </div>
            <div id="filters-content" class="p-6 transition-all duration-300">
                <form method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Subject Filter - Enhanced styling -->
                        <div class="space-y-2">
                            <label for="subject_id" class="block text-sm font-medium text-gray-700">Subject</label>
                            <select name="subject_id" id="subject_id" onchange="this.form.submit()" class="block w-full rounded-md border-gray-300 py-1.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm transition duration-150 ease-in-out">
                                <option value="">All Subjects</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if request.args.get('subject_id') == subject.id|string %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>



                        <!-- Status Filter - Enhanced styling -->
                        <div class="space-y-2">
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" onchange="this.form.submit()" class="block w-full rounded-md border-gray-300 py-1.5 pl-3 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm transition duration-150 ease-in-out">
                                <option value="">All Statuses</option>
                                <option value="correct" {% if request.args.get('status') == 'correct' %}selected{% endif %}>Correct</option>
                                <option value="partial" {% if request.args.get('status') == 'partial' %}selected{% endif %}>Partially Correct</option>
                                <option value="incorrect" {% if request.args.get('status') == 'incorrect' %}selected{% endif %}>Incorrect</option>
                            </select>
                        </div>

                        <!-- Clear Filters -->
                        <div class="flex items-end">
                            {% if request.args.get('subject_id') or request.args.get('status') %}
                            <a href="{{ url_for('review') }}" class="w-full inline-flex justify-center items-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors duration-200">
                                <i class="fas fa-times mr-2"></i>
                                Clear Filters
                            </a>
                            {% else %}
                            <div class="w-full text-center text-sm text-gray-400 py-2">
                                No filters applied
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stats Summary - New component -->
        {% if submissions %}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Correct</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set correct_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float == submission.part.score|float %}
                                        {% set correct_count.value = correct_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ correct_count.value }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Partial</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set partial_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float > 0 and submission.score|float < submission.part.score|float %}
                                        {% set partial_count.value = partial_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ partial_count.value }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Incorrect</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set incorrect_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float == 0 %}
                                        {% set incorrect_count.value = incorrect_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ incorrect_count.value }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Submissions - Redesigned with cards instead of table for mobile-friendliness -->
        <div class="mb-10">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Your Submission History</h3>
            
            {% if submissions %}
            <div class="space-y-4">
                {% for submission in submissions %}
                <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <!-- Left side: Question info and status -->
                            <div class="flex items-center space-x-4 flex-1 min-w-0">
                                <!-- Status indicator -->
                                <div class="flex-shrink-0">
                                    <div class="relative w-9 h-9">
                                        <svg class="w-9 h-9 transform -rotate-90" viewBox="0 0 36 36">
                                            <path class="stroke-current text-gray-200" stroke-width="3" fill="none" stroke-linecap="round" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <path class="stroke-current {% if submission.score == submission.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) or 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <text x="18" y="20.5" class="fill-current {% if submission.score == submission.part.score %}text-green-700{% elif submission.score > 0 %}text-amber-700{% else %}text-red-700{% endif %} font-bold text-xs" text-anchor="middle" transform="rotate(90 18 20.5)">{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) | int or 0 }}%</text>
                                        </svg>
                                    </div>
                                    <div class="text-xs font-medium {% if submission.score == submission.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %} mt-1 text-center">
                                        {{ submission.score }}/{{ submission.part.score }}
                                    </div>
                                </div>

                                <!-- Question info -->
                                <div class="flex-grow min-w-0">
                                    <a href="{{ url_for('load_question', question_id=submission.question_id) }}"
                                       class="text-gray-900 hover:text-indigo-600 transition-colors duration-200 font-medium block">
                                        {{ submission.question.title }}
                                    </a>
                                    <div class="text-xs text-gray-500 mt-1">
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-tag mr-1"></i>
                                            {{ submission.part.description }}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-book mr-1"></i>
                                            {{ submission.question.topic.name if submission.question.topic else 'No topic' }}
                                        </span>
                                        <span class="mx-2">•</span>
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-clock mr-1"></i>
                                            {{ submission.timestamp.strftime('%b %d, %Y at %I:%M %p') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Right side: Actions -->
                            <div class="flex items-center space-x-3 flex-shrink-0">
                                <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                                   class="inline-flex items-center justify-center rounded-md bg-indigo-50 px-3.5 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                                    <span>View Details</span>
                                    <i class="fas fa-chevron-right ml-1.5 text-xs opacity-70"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- Empty state - Redesigned with illustration -->
            <div class="text-center py-16 px-4 bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
                <svg class="mx-auto h-24 w-24 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No submissions yet</h3>
                <p class="mt-2 text-sm text-gray-500 max-w-md mx-auto">Start completing questions to track your progress and review your performance over time.</p>
                <div class="mt-6">
                    <a href="{{ url_for('index') }}" class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-indigo-500 hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Explore Questions
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Progress Bar - New component -->
        <!-- hide for now -->
        <!-- {% if submissions %}
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden mb-10">
            <div class="p-6">
                <h3 class="text-sm font-medium text-gray-900 mb-2">Your Overall Progress</h3>
                <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                    {% set total_score = 0 %}
                    {% set max_score = 0 %}
                    {% for submission in submissions %}
                        {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                            {% set total_score = total_score + submission.score|float %}
                            {% set max_score = max_score + submission.part.score|float %}
                        {% endif %}
                    {% endfor %}
                    {% set percentage = (total_score / max_score * 100) if max_score > 0 else 0 %}
                    <div class="bg-indigo-600 h-2.5 rounded-full" style="width: {{ percentage|int }}%"></div>
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500">
                    <span>{{ total_score }} points earned</span>
                    <span>{{ percentage|int }}% complete</span>
                </div>
            </div>
        </div>
        {% endif %} -->

        <!-- Navigation Footer - Enhanced with animation -->
        <div class="mt-10 text-center">
            <a href="{{ url_for('index') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Back to Home
            </a>
        </div>
    </div>
</div>

<style>
    /* Fade in up animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-in-up {
        animation: fadeInUp 0.8s ease-out;
    }
    
    /* Hover scaling animation for cards */
    .card-zoom-hover {
        transition: transform 0.3s ease-in-out;
    }
    
    .card-zoom-hover:hover {
        transform: scale(1.02);
    }
    
    /* Hover effect for submission items */
    .submission-item {
        transition: all 0.3s ease;
    }
    
    .submission-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>

<script>
    // Simple page animations
    document.addEventListener('DOMContentLoaded', function() {
        // Staggered animation for cards
        const cards = document.querySelectorAll('.space-y-4 > div');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 + (index * 100)); // Staggered delay
        });
    });
</script>
{% endblock %}
