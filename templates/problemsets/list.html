{% extends "base.html" %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto" id="problemsets-header-section">
            <h1 class="text-2xl font-semibold text-gray-900">Problem Sets</h1>
            <p class="mt-2 text-sm text-gray-700">A list of all problem sets you have created or have access to.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{{ url_for('create_problemset') }}" id="create-problemset-button" class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                Create Problem Set
            </a>
        </div>
    </div>

    <!-- Your Problem Sets -->
    <div class="mt-8" id="owned-problemsets-section">
        <h2 class="text-lg font-medium text-gray-900">Your Problem Sets</h2>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {% for problemset in owned_problemsets %}
            <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
                <div>
                    <span class="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700 ring-4 ring-white">
                        <i class="fas fa-book-open"></i>
                    </span>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-medium">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="focus:outline-none">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            {{ problemset.name }}
                        </a>
                    </h3>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ problemset.description or 'No description provided' }}
                    </p>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <span class="text-sm text-gray-500">
                        {{ problemset.questions|length }} questions
                    </span>
                    <div class="flex items-center space-x-2">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('problemset_submissions', id=problemset.id) }}" class="text-green-600 hover:text-green-800" title="View Submissions">
                            <i class="fas fa-history"></i>
                        </a>
                        <a href="{{ url_for('edit_problemset', id=problemset.id) }}" class="text-yellow-600 hover:text-yellow-800">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteProblemset({{ problemset.id }})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-span-full">
                <p class="text-gray-500 text-center py-4">You haven't created any problem sets yet.</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Shared Problem Sets -->
    <div class="mt-12" id="shared-problemsets-section">
        <h2 class="text-lg font-medium text-gray-900">Shared With You</h2>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {% for problemset in shared_problemsets %}
            <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
                <div>
                    <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                        <i class="fas fa-share-alt"></i>
                    </span>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-medium">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="focus:outline-none">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            {{ problemset.name }}
                        </a>
                    </h3>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ problemset.description or 'No description provided' }}
                    </p>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <span class="text-sm text-gray-500">
                        {{ problemset.questions|length }} questions
                    </span>
                    <span class="text-sm text-gray-500">
                        Shared by {{ problemset.creator.username }}
                    </span>
                    <div class="flex items-center space-x-2">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('problemset_submissions', id=problemset.id) }}" class="text-green-600 hover:text-green-800" title="View Submissions">
                            <i class="fas fa-history"></i>
                        </a>
                        <a href="{{ url_for('edit_problemset', id=problemset.id) }}" class="text-yellow-600 hover:text-yellow-800">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteProblemset({{ problemset.id }})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-span-full">
                <p class="text-gray-500 text-center py-4">No problem sets have been shared with you.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
// Problemsets tour has been removed - tours are now limited to Dojo and Vault only
</script>
{% endblock %} 
