{% extends "base.html" %}

{% block head %}
{{ super() }}
<style>
    @media print {
        body {
            font-size: 12pt;
            color: #000;
            background-color: #fff;
        }
        .no-print {
            display: none !important;
        }
        .print-only {
            display: block !important;
        }
        .page-break {
            page-break-before: always;
        }
        .print-container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }
        .print-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .print-section {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
    }
    .print-only {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 print-container">
    <!-- Print Header - Only visible when printing -->
    <div class="print-only print-header">
        <h1 class="text-2xl font-bold">{{ submission.problemset.name }} - Submission Report</h1>
        <p>{{ submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
    </div>

    <div class="flex justify-between items-center mb-6 no-print">
        <h1 class="text-2xl font-bold text-gray-900">Submission Details</h1>
        <div class="flex space-x-4">
            <button onclick="window.print()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-print mr-2"></i>Export to PDF
            </button>
            <a href="{{ url_for('problemset_submissions', id=submission.problemset.id) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-arrow-left mr-2"></i>Back to Submissions
            </a>
        </div>
    </div>

    <!-- Submission Overview -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8 print-section">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ submission.problemset.name }}</h3>
            <div class="text-right">
                <span class="inline-flex items-center rounded-md px-3 py-1.5 text-sm font-medium
                    {% if submission.total_score == submission.max_score %}
                        bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                    {% elif submission.total_score > 0 %}
                        bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                    {% else %}
                        bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20
                    {% endif %}">
                    Score: {{ submission.total_score }}/{{ submission.max_score }}
                    {% if submission.max_score > 0 %}
                    ({{ "%.1f"|format(submission.total_score / submission.max_score * 100) }}%)
                    {% endif %}
                </span>
            </div>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Student</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle text-gray-400 mr-2"></i>
                            {{ submission.user.username }}
                        </div>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Submission Date</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {{ submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium
                            {% if submission.status == 'completed' %}
                                bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                            {% elif submission.status == 'in_progress' %}
                                bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                            {% else %}
                                bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-600/20
                            {% endif %}">
                            {{ submission.status|title }}
                        </span>
                    </dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Question Submissions -->
    <div class="space-y-8">
        {% for question_submission in submission.question_submissions|sort(attribute='question.id') %}
        <div class="bg-white shadow overflow-hidden sm:rounded-lg print-section">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Question {{ loop.index }}</h3>
                <div class="text-right">
                    <span class="inline-flex items-center rounded-md px-2 py-1 text-sm font-medium
                        {% if question_submission.score == question_submission.part.score %}
                            bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                        {% elif question_submission.score > 0 %}
                            bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                        {% else %}
                            bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20
                        {% endif %}">
                        Score: {{ "%.1f"|format(question_submission.score) }}/{{ "%.1f"|format(question_submission.part.score) }}
                    </span>
                </div>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                <!-- Question Description -->
                <div class="mb-6">
                    <h4 class="text-base font-medium text-gray-900 mb-2">Question:</h4>
                    <div class="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg">
                        {{ question_submission.question.description|safe }}
                    </div>
                    {% if question_submission.question.attachment %}
                    <div class="mt-4">
                        <img src="{{ url_for('serve.serve_file', filename=question_submission.question.attachment) }}" alt="Question attachment" class="max-w-full h-auto rounded-lg border border-gray-200">
                    </div>
                    {% endif %}
                </div>

                <!-- Part Information -->
                <div class="mb-6">
                    <h4 class="text-base font-medium text-gray-900 mb-2">Part {{ question_submission.part.part_number }}:</h4>
                    <div class="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg">
                        {{ question_submission.part.description|safe }}
                    </div>
                    {% if question_submission.part.attachment %}
                    <div class="mt-4">
                        <img src="{{ url_for('serve.serve_file', filename=question_submission.part.attachment) }}" alt="Part attachment" class="max-w-full h-auto rounded-lg border border-gray-200">
                    </div>
                    {% endif %}
                </div>

                <!-- Student Answer -->
                <div class="mb-6">
                    <h4 class="text-base font-medium text-gray-900 mb-2">Your Answer:</h4>
                    <div class="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg border {% if question_submission.score == question_submission.part.score %}border-green-200{% elif question_submission.score > 0 %}border-yellow-200{% else %}border-red-200{% endif %}">
                        {{ question_submission.answer|safe }}
                    </div>
                </div>

                <!-- Feedback -->
                {% if question_submission.feedback %}
                <div class="mb-6">
                    <h4 class="text-base font-medium text-gray-900 mb-2">Feedback:</h4>
                    <div class="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg border border-indigo-200">
                        {{ question_submission.feedback|safe }}
                    </div>
                </div>
                {% endif %}

                <!-- Marking Points (if available) -->
                {% if question_submission.part.marking_points %}
                <div>
                    <h4 class="text-base font-medium text-gray-900 mb-2">Marking Scheme & Evaluation:</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Marking Point</th>
                                    <th scope="col" class="px-3 py-3.5 text-center text-sm font-semibold text-gray-900">Status</th>
                                    <th scope="col" class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Score</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                {% set feedback_data = {} %}
                                {% if question_submission.feedback %}
                                    {% set feedback_data = question_submission.feedback|from_json %}
                                {% endif %}

                                {% for mp in question_submission.part.marking_points|sort(attribute='order') %}
                                {% set evaluation = feedback_data.evaluated_points|selectattr('id', 'equalto', mp.id)|first if feedback_data.evaluated_points else None %}

                                <tr class="{% if evaluation and evaluation.achieved %}bg-green-50{% elif evaluation and evaluation.partial %}bg-yellow-50{% elif evaluation %}bg-red-50{% endif %}">
                                    <td class="py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6">
                                        <div class="font-medium">{{ mp.description }}</div>
                                        {% if evaluation and evaluation.evidence %}
                                        <div class="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded border-l-4 {% if evaluation.achieved %}border-green-400{% elif evaluation.partial %}border-yellow-400{% else %}border-red-400{% endif %}">
                                            <strong>Evidence found:</strong> "{{ evaluation.evidence }}"
                                        </div>
                                        {% elif evaluation and not evaluation.achieved and not evaluation.partial %}
                                        <div class="mt-2 text-xs text-red-600 italic">
                                            No evidence found in your answer for this marking point.
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-4 text-center text-sm">
                                        {% if evaluation %}
                                            {% if evaluation.achieved %}
                                                <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                                    <i class="fas fa-check mr-1"></i>
                                                    Achieved
                                                </span>
                                            {% elif evaluation.partial %}
                                                <span class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                                                    <i class="fas fa-minus mr-1"></i>
                                                    Partial
                                                </span>
                                            {% else %}
                                                <span class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                                    <i class="fas fa-times mr-1"></i>
                                                    Not Achieved
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-600">
                                                <i class="fas fa-question mr-1"></i>
                                                Not Evaluated
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-4 text-right text-sm">
                                        {% if evaluation %}
                                            <div class="font-medium {% if evaluation.achieved %}text-green-600{% elif evaluation.partial %}text-yellow-600{% else %}text-red-600{% endif %}">
                                                {{ "%.1f"|format(evaluation.achieved_score) }}/{{ "%.1f"|format(mp.score) }}
                                            </div>
                                        {% else %}
                                            <div class="text-gray-500">{{ mp.score }}</div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
    // Add event listener for print completion
    window.addEventListener('afterprint', function() {
        console.log('Print completed or cancelled');
    });
</script>
{% endblock %}