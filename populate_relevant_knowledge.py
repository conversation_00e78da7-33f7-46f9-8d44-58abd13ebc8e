#!/usr/bin/env python3
"""
RAG-based Relevant Knowledge Population Script

This script uses Sentence Transformers and FAISS to find the 3 most relevant DOJO questions
for each question in the database and populates the QuestionRelevance relationships.
"""

import os
import sys
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Question, QuestionRelevance, Topic, Subject, Part, MarkingPoint

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from sentence_transformers import SentenceTransformer
    import faiss
    from sklearn.preprocessing import normalize
except ImportError as e:
    logger.error(f"Required libraries not installed: {e}")
    logger.error("Please install: pip install sentence-transformers faiss-cpu scikit-learn")
    sys.exit(1)


class RelevantKnowledgePopulator:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", similarity_threshold: float = 0.3):
        """
        Initialize the RAG-based relevant knowledge populator.
        
        Args:
            model_name: Sentence transformer model to use
            similarity_threshold: Minimum similarity score to consider relevant (0-1)
        """
        self.model_name = model_name
        self.similarity_threshold = similarity_threshold
        self.model = None
        self.dojo_questions = []
        self.dojo_embeddings = None
        self.faiss_index = None
        
        logger.info(f"Initializing with model: {model_name}, threshold: {similarity_threshold}")
    
    def load_model(self):
        """Load the sentence transformer model."""
        logger.info(f"Loading sentence transformer model: {self.model_name}")
        self.model = SentenceTransformer(self.model_name)
        logger.info("Model loaded successfully")
    
    def extract_question_text(self, question: Question) -> str:
        """
        Extract and combine text from question for embedding.
        
        Args:
            question: Question object
            
        Returns:
            Combined text string for embedding
        """
        text_parts = []
        
        # Add question title and description
        if question.title:
            text_parts.append(f"Title: {question.title}")
        if question.description:
            text_parts.append(f"Question: {question.description}")
        
        # Add topic context
        if question.topic:
            text_parts.append(f"Topic: {question.topic.name}")
        
        # Add marking points from all parts
        for part in question.parts:
            if part.description and part.description != question.description:
                text_parts.append(f"Part: {part.description}")
            
            # Add marking points
            marking_points = MarkingPoint.query.filter_by(part_id=part.id).all()
            for mp in marking_points:
                if mp.description:
                    text_parts.append(f"Answer: {mp.description}")
        
        return " | ".join(text_parts)
    
    def load_dojo_questions(self, subject_id: Optional[int] = None) -> List[Dict]:
        """
        Load all DOJO questions from the database.
        
        Args:
            subject_id: Optional subject ID to filter by (None for all subjects)
            
        Returns:
            List of DOJO question dictionaries
        """
        logger.info("Loading DOJO questions from database...")
        
        query = Question.query.filter_by(is_dojo=True)
        
        if subject_id:
            query = query.join(Topic).filter(Topic.subject_id == subject_id)
        
        dojo_questions = query.all()
        
        self.dojo_questions = []
        for q in dojo_questions:
            question_text = self.extract_question_text(q)
            self.dojo_questions.append({
                'id': q.id,
                'text': question_text,
                'topic_id': q.topic_id,
                'topic_name': q.topic.name if q.topic else 'Unknown',
                'subject_id': q.topic.subject_id if q.topic else None,
                'title': q.title or f"Question {q.id}"
            })
        
        logger.info(f"Loaded {len(self.dojo_questions)} DOJO questions")
        return self.dojo_questions
    
    def create_embeddings(self):
        """Create embeddings for all DOJO questions and build FAISS index."""
        if not self.dojo_questions:
            raise ValueError("No DOJO questions loaded. Call load_dojo_questions() first.")
        
        logger.info("Creating embeddings for DOJO questions...")
        
        # Extract texts for embedding
        texts = [q['text'] for q in self.dojo_questions]
        
        # Generate embeddings
        self.dojo_embeddings = self.model.encode(texts, show_progress_bar=True)
        
        # Normalize embeddings for cosine similarity
        self.dojo_embeddings = normalize(self.dojo_embeddings, norm='l2')
        
        # Create FAISS index
        dimension = self.dojo_embeddings.shape[1]
        self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
        self.faiss_index.add(self.dojo_embeddings.astype('float32'))
        
        logger.info(f"Created FAISS index with {self.faiss_index.ntotal} vectors")
    
    def find_relevant_questions(self, question: Question, top_k: int = 3) -> List[Tuple[int, float, str]]:
        """
        Find the most relevant DOJO questions for a given question.
        
        Args:
            question: Question to find relevant knowledge for
            top_k: Number of relevant questions to return
            
        Returns:
            List of tuples (question_id, similarity_score, relevance_type)
        """
        # Extract text and create embedding
        question_text = self.extract_question_text(question)
        question_embedding = self.model.encode([question_text])
        question_embedding = normalize(question_embedding, norm='l2')
        
        # Search for similar questions
        scores, indices = self.faiss_index.search(question_embedding.astype('float32'), top_k + 10)  # Get extra to filter
        
        relevant_questions = []
        same_subject_id = question.topic.subject_id if question.topic else None
        
        for score, idx in zip(scores[0], indices[0]):
            if len(relevant_questions) >= top_k:
                break
                
            dojo_q = self.dojo_questions[idx]
            
            # Skip if same question
            if dojo_q['id'] == question.id:
                continue
            
            # Skip if below threshold
            if score < self.similarity_threshold:
                continue
            
            # Skip if different subject
            if same_subject_id and dojo_q['subject_id'] != same_subject_id:
                continue
            
            # Determine relevance type based on topic and score
            relevance_type = self._determine_relevance_type(question, dojo_q, score)
            
            relevant_questions.append((dojo_q['id'], float(score), relevance_type))
        
        return relevant_questions
    
    def _determine_relevance_type(self, question: Question, dojo_question: Dict, score: float) -> str:
        """Determine the relevance type based on topic relationship and similarity score."""
        if question.topic_id == dojo_question['topic_id']:
            return 'similar_concept' if score > 0.7 else 'related_topic'
        elif score > 0.8:
            return 'similar_concept'
        elif score > 0.6:
            return 'related_topic'
        else:
            return 'general'
    
    def _score_to_strength(self, score: float) -> int:
        """Convert similarity score (0-1) to relevance strength (1-5)."""
        if score >= 0.9:
            return 5
        elif score >= 0.8:
            return 4
        elif score >= 0.7:
            return 3
        elif score >= 0.6:
            return 2
        else:
            return 1
    
    def populate_all_relevant_knowledge(self, subject_id: Optional[int] = None, overwrite_existing: bool = False):
        """
        Populate relevant knowledge for all questions.
        
        Args:
            subject_id: Optional subject ID to process (None for all subjects)
            overwrite_existing: Whether to overwrite existing relationships
        """
        logger.info("Starting relevant knowledge population...")
        
        # Load questions to process
        query = Question.query
        if subject_id:
            query = query.join(Topic).filter(Topic.subject_id == subject_id)
        
        all_questions = query.all()
        
        processed = 0
        created_relationships = 0
        skipped = 0
        
        for question in all_questions:
            # Skip if already has relevant knowledge and not overwriting
            if not overwrite_existing and question.relevant_questions:
                skipped += 1
                continue
            
            # Find relevant DOJO questions
            relevant = self.find_relevant_questions(question, top_k=3)
            
            if not relevant:
                logger.debug(f"No relevant questions found for Question {question.id}")
                processed += 1
                continue
            
            # Clear existing relationships if overwriting
            if overwrite_existing:
                QuestionRelevance.query.filter_by(question_id=question.id).delete()
            
            # Create new relationships
            for relevant_id, score, rel_type in relevant:
                strength = self._score_to_strength(score)
                
                relationship = QuestionRelevance(
                    question_id=question.id,
                    relevant_question_id=relevant_id,
                    relevance_type=rel_type,
                    strength=strength
                )
                
                db.session.add(relationship)
                created_relationships += 1
                
                logger.debug(f"Question {question.id} -> DOJO {relevant_id} "
                           f"(score: {score:.3f}, type: {rel_type}, strength: {strength})")
            
            processed += 1
            
            # Commit in batches
            if processed % 50 == 0:
                db.session.commit()
                logger.info(f"Processed {processed} questions, created {created_relationships} relationships")
        
        # Final commit
        db.session.commit()
        
        logger.info(f"Completed! Processed: {processed}, Skipped: {skipped}, "
                   f"Created relationships: {created_relationships}")


def main():
    """Main function to run the relevant knowledge population."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Populate relevant knowledge using RAG')
    parser.add_argument('--subject-id', type=int, help='Subject ID to process (default: all subjects)')
    parser.add_argument('--overwrite', action='store_true', help='Overwrite existing relationships')
    parser.add_argument('--threshold', type=float, default=0.3, help='Similarity threshold (default: 0.3)')
    parser.add_argument('--model', default='all-MiniLM-L6-v2', help='Sentence transformer model')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    
    args = parser.parse_args()
    
    with app.app_context():
        try:
            # Initialize populator
            populator = RelevantKnowledgePopulator(
                model_name=args.model,
                similarity_threshold=args.threshold
            )
            
            # Load model and data
            populator.load_model()
            populator.load_dojo_questions(subject_id=args.subject_id)
            
            if not populator.dojo_questions:
                logger.error("No DOJO questions found. Make sure you have DOJO questions in your database.")
                return
            
            # Create embeddings
            populator.create_embeddings()
            
            if args.dry_run:
                logger.info("DRY RUN MODE - No changes will be made")
                # TODO: Add dry run logic to show what would be created
                return
            
            # Populate relevant knowledge
            populator.populate_all_relevant_knowledge(
                subject_id=args.subject_id,
                overwrite_existing=args.overwrite
            )
            
            logger.info("Relevant knowledge population completed successfully!")
            
        except Exception as e:
            logger.error(f"Error during processing: {e}")
            db.session.rollback()
            raise


if __name__ == "__main__":
    main()
