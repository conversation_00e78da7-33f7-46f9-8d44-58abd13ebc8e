#!/usr/bin/env python3
"""
Script to generate rubrics for all chemistry questions in the database
"""

import json
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from app import app
from models import db, Question, Part, Topic, Subject, MarkingPoint
import google.generativeai as genai
from dotenv import load_dotenv
from pinecone import Pinecone

# Load environment variables
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Initialize Pinecone
pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
pinecone_index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

def get_pinecone_context(part, question):
    """Get relevant context from Pinecone for better rubric generation"""
    try:
        # Create query text from question and part context
        query_text = f"{part.description}"
        if part.answer:
            query_text += f" {part.answer}"
        if question.topic:
            query_text += f" {question.topic.name}"
        if question.topic and question.topic.subject:
            query_text += f" {question.topic.subject.name}"
        if question.description:
            query_text += f" {question.description}"

        # Query Pinecone index for relevant context (using correct API format)
        query_payload = {
            "inputs": {
                "text": query_text
            },
            "top_k": 5  # Get relevant context for rubric generation
        }

        query_response = pinecone_index.search(
            namespace="__default__",
            query=query_payload
        )

        context_text = ""
        if 'result' in query_response and 'hits' in query_response['result']:
            for item in query_response['result']['hits']:
                if 'fields' in item:
                    title = item['fields'].get('title', 'Unknown')
                    content = item['fields'].get('content', '')
                    context_text += f"{title}: {content}\n"

        return context_text
    except Exception as e:
        print(f"    Warning: Could not retrieve Pinecone context: {e}")
        return ""

def get_question_context(question):
    """Get context from all parts of the question for comprehensive rubric generation"""
    context = f"Full Question Context:\n"
    context += f"Question Title: {question.title or 'Untitled'}\n"
    context += f"Question Description: {question.description or 'No description'}\n"
    context += f"Question Source: {question.source or 'Unknown'}\n"
    context += f"Topic: {question.topic.name if question.topic else 'Unknown'}\n"
    context += f"Subject: {question.topic.subject.name if question.topic and question.topic.subject else 'Unknown'}\n\n"

    context += "All Parts of this Question:\n"
    for i, part in enumerate(question.parts, 1):
        context += f"Part {i} (ID: {part.id}):\n"
        context += f"  Description: {part.description}\n"
        context += f"  Answer: {part.answer or 'No answer provided'}\n"
        context += f"  Score: {part.score} points\n"
        context += f"  Input Type: {part.input_type}\n\n"

    return context

def generate_rubric_for_part(part, question_context, pinecone_context):
    """Generate a detailed rubric for a specific part using Gemini AI with enhanced context"""

    # Get marking points for this part
    marking_points = MarkingPoint.query.filter_by(part_id=part.id).order_by(MarkingPoint.order).all()

    if not marking_points:
        print(f"  Warning: No marking points found for part {part.id}")
        return None

    # Create marking points text
    marking_points_text = ""
    for i, mp in enumerate(marking_points, 1):
        marking_points_text += f"{i}. {mp.description} ({mp.score} points)\n"

    prompt = f"""
You are an expert educational assessment designer specializing in chemistry. Create a detailed objective rubric for grading student answers to this chemistry question part.

{question_context}

CURRENT PART BEING ASSESSED:
Question Part: {part.description}
Model Answer: {part.answer or "No model answer provided"}
Total Score: {part.score} points
Input Type: {part.input_type}

Current Marking Points:
{marking_points_text}

RELEVANT CHEMISTRY KNOWLEDGE CONTEXT:
{pinecone_context}

Create a comprehensive rubric that:
1. Clearly defines what constitutes full marks, partial marks, and no marks for each criterion
2. Provides specific examples of acceptable answers and common variations
3. Identifies typical student misconceptions and how they should be penalized
4. Ensures objective, consistent grading across different graders
5. Covers all aspects of the model answer and marking points
6. Incorporates the broader question context to understand the learning objectives
7. Uses the chemistry knowledge context to anticipate student responses
8. Is detailed enough for any AI or human grader to apply consistently

Format the rubric as a structured text that includes:
- Clear scoring criteria for each marking point
- Examples of full credit, partial credit, and no credit responses
- Common misconceptions and their point deductions
- Key chemistry concepts that must be demonstrated
- Acceptable alternative explanations or phrasings

The rubric should be comprehensive yet practical for automated grading systems.
"""

    try:
        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        print(f"  Error generating rubric for part {part.id}: {e}")
        return None

def process_part_for_rubric(part_data_tuple):
    """Process a single part for rubric generation (for multithreading)"""
    part_id, part_description, part_answer, part_score, part_input_type, question_context, pinecone_context = part_data_tuple

    # Each thread needs its own application context
    with app.app_context():
        try:
            print(f"    Processing Part {part_id}...")

            # Recreate part object in this thread's context
            part = Part.query.get(part_id)
            if not part:
                print(f"    ❌ Part {part_id} not found in database")
                return part_id, None, False

            rubric = generate_rubric_for_part(part, question_context, pinecone_context)

            if rubric:
                # Get marking points for this part
                marking_points = MarkingPoint.query.filter_by(part_id=part.id).order_by(MarkingPoint.order).all()

                part_data = {
                    "part_id": part.id,
                    "description": part.description,
                    "answer": part.answer,
                    "score": part.score,
                    "input_type": part.input_type,
                    "rubric": rubric,
                    "marking_points": [
                        {
                            "id": mp.id,
                            "description": mp.description,
                            "score": mp.score,
                            "order": mp.order
                        }
                        for mp in marking_points
                    ]
                }
                print(f"    ✅ Part {part.id} rubric generated successfully")
                return part.id, part_data, True
            else:
                print(f"    ❌ Failed to generate rubric for part {part.id}")
                return part.id, None, False
        except Exception as e:
            print(f"    ❌ Error processing part {part_id}: {e}")
            return part_id, None, False

def get_chemistry_questions():
    """Get all chemistry questions from the database"""
    # Find chemistry subject
    chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
    if not chemistry_subject:
        print("Chemistry subject not found in database")
        return []

    # Get all chemistry questions with their parts and topics (eager loading)
    chemistry_questions = db.session.query(Question).join(Topic).filter(
        Topic.subject_id == chemistry_subject.id
    ).options(
        db.joinedload(Question.topic).joinedload(Topic.subject),
        db.joinedload(Question.parts)
    ).all()

    print(f"Found {len(chemistry_questions)} chemistry questions")
    return chemistry_questions

def generate_all_rubrics():
    """Generate rubrics for all chemistry questions and save to JSON file"""

    with app.app_context():
        chemistry_questions = get_chemistry_questions()

        if not chemistry_questions:
            print("No chemistry questions found")
            return
    
        rubrics_data = {
            "metadata": {
                "total_questions": len(chemistry_questions),
                "generated_at": None,
                "subject": "h2-chemistry"
            },
            "rubrics": {}
        }

        total_parts = 0
        successful_rubrics = 0

        # Use multithreading to process parts in parallel
        max_workers = min(5, os.cpu_count() or 1)  # Limit concurrent requests to avoid rate limits
        print(f"Using {max_workers} threads for parallel processing")

        for question in chemistry_questions:
            print(f"\nProcessing Question {question.id}: {question.title or 'Untitled'}")

            # Get context for this question
            question_context = get_question_context(question)

            question_data = {
                "question_id": question.id,
                "title": question.title,
                "description": question.description,
                "source": question.source,
                "topic": question.topic.name if question.topic else None,
                "parts": {}
            }

            if not question.parts:
                print("  No parts found for this question")
                continue

            # Prepare tasks for parallel processing
            part_tasks = []
            for part in question.parts:
                total_parts += 1
                # Get Pinecone context for each part
                pinecone_context = get_pinecone_context(part, question)
                # Create tuple with all necessary data for the thread
                part_data_tuple = (
                    part.id,
                    part.description,
                    part.answer,
                    part.score,
                    part.input_type,
                    question_context,
                    pinecone_context
                )
                part_tasks.append(part_data_tuple)

            print(f"  Processing {len(part_tasks)} parts in parallel...")

            # Process parts in parallel
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_part = {
                    executor.submit(process_part_for_rubric, part_tuple): part_tuple[0]
                    for part_tuple in part_tasks
                }

                # Collect results as they complete
                for future in as_completed(future_to_part):
                    part_id, part_data, success = future.result()

                    if success and part_data:
                        successful_rubrics += 1
                        question_data["parts"][str(part_id)] = part_data

            if question_data["parts"]:  # Only add if we have parts with rubrics
                rubrics_data["rubrics"][str(question.id)] = question_data
                print(f"  ✅ Question {question.id} completed with {len(question_data['parts'])} parts")
    
        # Update metadata
        from datetime import datetime
        rubrics_data["metadata"]["generated_at"] = datetime.now().isoformat()
        rubrics_data["metadata"]["total_parts"] = total_parts
        rubrics_data["metadata"]["successful_rubrics"] = successful_rubrics

        # Save to JSON file
        output_file = "chemistry_rubrics.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(rubrics_data, f, indent=2, ensure_ascii=False)

        print(f"\n🎉 Rubric generation complete!")
        print(f"📊 Statistics:")
        print(f"   - Total questions processed: {len(chemistry_questions)}")
        print(f"   - Total parts processed: {total_parts}")
        print(f"   - Successful rubrics generated: {successful_rubrics}")
        print(f"   - Success rate: {(successful_rubrics/total_parts*100):.1f}%")
        print(f"📁 Rubrics saved to: {output_file}")

if __name__ == "__main__":
    print("🧪 Chemistry Rubric Generator")
    print("=" * 50)
    generate_all_rubrics()
