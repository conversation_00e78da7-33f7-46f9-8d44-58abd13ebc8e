# Chemistry Notes RAG System

A comprehensive Retrieval-Augmented Generation (RAG) system for chemistry notes that chunks notes based on their sections and provides semantic search capabilities.

## Overview

This system consists of three main components:

1. **Notes Chunking** (`chunk_notes.py`) - Processes markdown notes and splits them into semantic chunks based on section headers
2. **RAG System** (`notes_rag_system.py`) - Creates embeddings and provides semantic search functionality
3. **API Endpoints** - REST API for searching and retrieving note sections

## Features

- **Intelligent Chunking**: Automatically splits notes based on markdown headers while preserving hierarchy
- **Semantic Search**: Uses sentence transformers for understanding conceptual relationships
- **Fast Retrieval**: FAISS vector database for efficient similarity search
- **Contextual Information**: Provides parent and sibling sections for better understanding
- **Database Integration**: Stores chunks and embeddings in your existing database
- **REST API**: Easy integration with your web application

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements_notes_rag.txt
```

2. Create the database tables:
```bash
python -c "from app import app; from models import db; app.app_context().push(); db.create_all()"
```

## Usage

### 1. Chunk Your Notes

First, process your chemistry notes to create chunks:

```bash
python chunk_notes.py
```

This will:
- Process all `.md` files in the `chemistry_notes_markdown` directory
- Extract sections based on markdown headers (# ## ### etc.)
- Clean and normalize the content
- Save chunks to the database
- Generate a JSON file for inspection

### 2. Create Embeddings

Generate embeddings for semantic search:

```bash
python notes_rag_system.py
```

This will:
- Load a sentence transformer model (default: `all-MiniLM-L6-v2`)
- Create embeddings for all chunks
- Build a FAISS index for fast search
- Test the system with a sample query

### 3. Use the API

The system provides REST API endpoints for integration:

#### Search Notes
```bash
POST /api/notes/search
Content-Type: application/json

{
    "query": "organic chemistry functional groups",
    "top_k": 5,
    "min_score": 0.3
}
```

#### Get Chunk Context
```bash
GET /api/notes/chunk/{chunk_id}/context
```

## Database Schema

### NotesChunk
- `id`: Primary key
- `content`: Cleaned text content
- `title`: Section title
- `filename`: Source markdown file
- `chapter_id`: Chapter identifier
- `section_id`: URL-safe section identifier
- `level`: Header level (1-6)
- `parent_sections`: JSON array of parent section titles
- `start_line`, `end_line`: Position in source file
- `word_count`, `char_count`: Content statistics
- `chunk_hash`: SHA256 hash for deduplication
- `created_at`, `updated_at`: Timestamps

### NotesEmbedding
- `id`: Primary key
- `chunk_id`: Foreign key to NotesChunk
- `model_name`: Embedding model used
- `embedding_vector`: JSON array of embedding values
- `vector_dimension`: Size of embedding vector
- `created_at`: Timestamp

## Configuration

### Chunking Parameters

You can customize the chunking behavior in `chunk_notes.py`:

- `min_content_length`: Minimum characters for a chunk (default: 50)
- `use_parallel`: Enable parallel processing (default: True)
- `max_workers`: Number of worker processes (default: 32)

### RAG Parameters

Customize the RAG system in `notes_rag_system.py`:

- `model_name`: Sentence transformer model (default: "all-mpnet-base-v2" - state-of-the-art)
- `batch_size`: Embedding batch size (default: 32, use 16 for larger models)
- `similarity_threshold`: Minimum similarity for search results (default: 0.3)

### Model Information

**Current Model: all-mpnet-base-v2**
- **Type**: State-of-the-art sentence transformer
- **Dimensions**: 768 (vs 384 for smaller models)
- **Parameters**: 109M (vs 22M for lightweight models)
- **Accuracy**: Excellent for scientific and technical content
- **Speed**: Moderate (optimized for accuracy over speed)

## Testing

Run the comprehensive test suite:

```bash
python test_notes_rag.py
```

This will test:
- Notes chunking functionality
- Database models and tables
- RAG dependencies
- Embedding and search system
- API endpoints

## API Reference

### Search Notes

**Endpoint**: `POST /api/notes/search`

**Request Body**:
```json
{
    "query": "search query",
    "top_k": 5,
    "min_score": 0.3
}
```

**Response**:
```json
{
    "status": "success",
    "query": "search query",
    "results": [
        {
            "id": 1,
            "title": "Section Title",
            "content": "Preview of content...",
            "full_content": "Complete content",
            "filename": "chapter.md",
            "chapter_id": "chapter",
            "section_id": "section-title",
            "level": 2,
            "parent_sections": ["Parent Section"],
            "similarity_score": 0.85,
            "relevance_type": "highly_relevant",
            "word_count": 150,
            "char_count": 800
        }
    ],
    "total_results": 1,
    "parameters": {
        "top_k": 5,
        "min_score": 0.3
    }
}
```

### Get Chunk Context

**Endpoint**: `GET /api/notes/chunk/{chunk_id}/context`

**Response**:
```json
{
    "status": "success",
    "context": {
        "chunk": { /* chunk data */ },
        "parent_sections": [ /* parent chunks */ ],
        "sibling_sections": [ /* sibling chunks */ ]
    }
}
```

## Performance

- **Chunking**: Processes ~25 chemistry notes files in under 30 seconds
- **Embedding**: Creates embeddings for ~1000 chunks in under 2 minutes
- **Search**: Sub-second response times for similarity search
- **Memory**: ~500MB RAM for typical chemistry notes corpus

## Troubleshooting

### Common Issues

1. **Dependencies not installed**:
   ```bash
   pip install sentence-transformers faiss-cpu scikit-learn numpy
   ```

2. **Database tables missing**:
   ```bash
   python -c "from app import app; from models import db; app.app_context().push(); db.create_all()"
   ```

3. **No chunks found**:
   - Ensure markdown files exist in `chemistry_notes_markdown/`
   - Run `python chunk_notes.py` first

4. **Search returns no results**:
   - Run `python notes_rag_system.py` to create embeddings
   - Check that chunks exist in database
   - Try lowering `min_score` parameter

### Logs

Check the application logs for detailed error information. The system uses structured logging with timestamps and log levels.

## Contributing

When adding new features:

1. Update the database models if needed
2. Add appropriate tests to `test_notes_rag.py`
3. Update this README with new functionality
4. Ensure backward compatibility

## License

This RAG system is part of the VastLearn chemistry education platform.
