"""
Utility functions for handling question misconceptions
"""

import json
import os
import logging

logger = logging.getLogger(__name__)

MISCONCEPTIONS_FILE = "question_misconceptions.json"

def load_misconceptions():
    """Load misconceptions from JSON file"""
    if os.path.exists(MISCONCEPTIONS_FILE):
        try:
            with open(MISCONCEPTIONS_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading misconceptions: {e}")
    return {}

def save_misconceptions(misconceptions_data):
    """Save misconceptions to JSON file"""
    try:
        with open(MISCONCEPTIONS_FILE, 'w') as f:
            json.dump(misconceptions_data, f, indent=2)
        logger.info(f"Saved misconceptions to {MISCONCEPTIONS_FILE}")
        return True
    except Exception as e:
        logger.error(f"Error saving misconceptions: {e}")
        return False

def get_part_misconceptions(question_id, part_id):
    """Get misconceptions for a specific question and part"""
    misconceptions_data = load_misconceptions()
    question_key = str(question_id)
    part_key = str(part_id)
    
    if question_key in misconceptions_data and part_key in misconceptions_data[question_key]:
        return misconceptions_data[question_key][part_key].get('common_mistakes', [])
    return []

def update_part_misconceptions(question_id, part_id, misconceptions):
    """Update misconceptions for a specific question and part"""
    misconceptions_data = load_misconceptions()
    question_key = str(question_id)
    part_key = str(part_id)
    
    # Ensure the structure exists
    if question_key not in misconceptions_data:
        misconceptions_data[question_key] = {}
    
    if part_key not in misconceptions_data[question_key]:
        misconceptions_data[question_key][part_key] = {}
    
    # Update the misconceptions
    misconceptions_data[question_key][part_key]['common_mistakes'] = misconceptions
    
    # Save the updated data
    return save_misconceptions(misconceptions_data)

def get_all_question_misconceptions(question_id):
    """Get all misconceptions for all parts of a question"""
    misconceptions_data = load_misconceptions()
    question_key = str(question_id)
    
    if question_key in misconceptions_data:
        return misconceptions_data[question_key]
    return {}
