#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to insert questions from new_question.json into the database.
Each question will have only 1 part, with the question field as the part description
and each item in the answer list as individual marking points.
All questions will be assigned to subject_id=3 (h2-chemistry).
"""

import json
import os
import sys
from flask import Flask

# Add the current directory to Python path to import models
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flask app and models
from app import app
from models import db, Subject, Topic, Question, Part, MarkingPoint

def load_questions_from_json(file_path):
    """Load questions from the JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {file_path}: {e}")
        return None

def get_or_create_topic(topic_name, subject_id):
    """Get existing topic or create a new one"""
    topic = Topic.query.filter_by(name=topic_name, subject_id=subject_id).first()
    if not topic:
        topic = Topic(name=topic_name, subject_id=subject_id)
        db.session.add(topic)
        db.session.flush()  # Get the ID without committing
        print(f"Created new topic: {topic_name}")
    return topic

def insert_question(topic, question_text, answer_list):
    """Insert a single question with its part and marking points"""
    try:
        # Create the question
        question = Question(
            title=f"Question from {topic.name}",
            description=question_text,
            topic_id=topic.id,
            is_dojo=False
        )
        db.session.add(question)
        db.session.flush()  # Get the question ID
        
        # Create the single part
        part = Part(
            description=question_text,
            answer="",  # We'll store individual marking points separately
            score=len(answer_list),  # Total score equals number of marking points
            question_id=question.id,
            input_type="saq"  # Short answer question
        )
        db.session.add(part)
        db.session.flush()  # Get the part ID
        
        # Create marking points for each answer item
        for i, marking_point_text in enumerate(answer_list):
            marking_point = MarkingPoint(
                part_id=part.id,
                description=marking_point_text.strip(),
                score=1.0,  # Each marking point worth 1 point
                order=i + 1,  # Order starting from 1
                is_auto_generated=False
            )
            db.session.add(marking_point)
        
        return question
        
    except Exception as e:
        print(f"Error inserting question: {e}")
        db.session.rollback()
        raise

def verify_insertion():
    """Verify that the questions were inserted correctly"""
    with app.app_context():
        subject = Subject.query.get(3)
        if not subject:
            print("Subject not found")
            return

        print(f"\n📊 Verification Report for {subject.name}:")

        # Count questions by topic
        topics = Topic.query.filter_by(subject_id=subject.id).all()
        total_questions = 0
        total_marking_points = 0

        for topic in topics:
            question_count = Question.query.filter_by(topic_id=topic.id).count()
            if question_count > 0:
                # Count marking points for this topic
                marking_points_count = db.session.query(MarkingPoint).join(Part).join(Question).filter(
                    Question.topic_id == topic.id
                ).count()

                print(f"  {topic.name}: {question_count} questions, {marking_points_count} marking points")
                total_questions += question_count
                total_marking_points += marking_points_count

        print(f"\n📈 Total: {total_questions} questions, {total_marking_points} marking points")

def main():
    """Main function to process and insert all questions"""
    import argparse

    parser = argparse.ArgumentParser(description='Insert questions from JSON into database')
    parser.add_argument('--verify-only', action='store_true', help='Only verify existing data, do not insert')
    args = parser.parse_args()

    if args.verify_only:
        verify_insertion()
        return

    # Check if the JSON file exists
    json_file = 'new_question.json'
    if not os.path.exists(json_file):
        print(f"Error: {json_file} not found in current directory")
        return

    # Load questions from JSON
    questions_data = load_questions_from_json(json_file)
    if not questions_data:
        return

    with app.app_context():
        try:
            # Verify subject_id=3 exists
            subject = Subject.query.get(3)
            if not subject:
                print("Error: Subject with ID 3 not found in database")
                print("Available subjects:")
                subjects = Subject.query.all()
                for s in subjects:
                    print(f"  ID {s.id}: {s.name}")
                return

            print(f"Inserting questions for subject: {subject.name} (ID: {subject.id})")

            total_questions = 0
            total_marking_points = 0

            # Process each topic in the JSON
            for topic_name, questions_list in questions_data.items():
                print(f"\nProcessing topic: {topic_name}")

                # Get or create the topic
                topic = get_or_create_topic(topic_name, subject.id)

                # Process each question in this topic
                for question_data in questions_list:
                    question_text = question_data.get('question', '').strip()
                    answer_list = question_data.get('answer', [])

                    if not question_text:
                        print("  Skipping question with empty text")
                        continue

                    if not answer_list:
                        print(f"  Skipping question with no answer points: {question_text[:50]}...")
                        continue

                    # Insert the question
                    insert_question(topic, question_text, answer_list)
                    total_questions += 1
                    total_marking_points += len(answer_list)

                    print(f"  Inserted question with {len(answer_list)} marking points")

            # Commit all changes
            db.session.commit()
            print(f"\n✅ Successfully inserted {total_questions} questions with {total_marking_points} total marking points")

            # Run verification
            verify_insertion()

        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during insertion: {e}")
            raise

if __name__ == "__main__":
    main()
